# Sistem Status Absen dengan localStorage

## Deskripsi
Sistem ini mengimplementasikan penyimpanan status absen di localStorage dengan key `status_absen` untuk memastikan aplikasi dapat menentukan jenis absensi (masuk/pulang) bahkan dalam kondisi offline.

## Fitur Utama

### 1. Penyimpanan Status Absen
- **Key localStorage**: `status_absen`
- **Format data**:
```json
{
  "jenis": "masuk" | "pulang",
  "tanggal": "YYYY-MM-DD",
  "timestamp": "ISO string"
}
```

### 2. Logika Penentuan Jenis Absensi

#### Mode Online
1. Cek data absensi dari server API
2. Jika sudah ada `jam_masuk` tapi belum ada `jam_pulang` → set ke "pulang"
3. Jika sudah ada `jam_masuk` dan `jam_pulang` → set ke "masuk" (shift berikutnya)
4. Jika belum ada data → set ke "masuk"
5. Simpan status ke localStorage untuk referensi offline

#### Mode Offline
1. Ambil data dari localStorage dengan key `status_absen`
2. Cek tanggal data:
   - Jika tanggal ≠ hari ini → return "masuk" (reset untuk hari baru)
   - Jika tanggal = hari ini:
     - Jika jenis = "masuk" → return "pulang"
     - Jika jenis = "pulang" → return "masuk"

### 3. Sinkronisasi Data

#### Ketika Kembali Online
- Aplikasi otomatis melakukan recheck status absensi dari server
- Update localStorage dengan data terbaru dari server
- Sinkronisasi data offline yang tertunda

#### Ketika Menjadi Offline
- Aplikasi beralih menggunakan data dari localStorage
- Tampilkan indikator mode offline di UI

## Implementasi Teknis

### Fungsi Utama

#### `saveStatusAbsen(jenis: 'masuk' | 'pulang')`
```typescript
const saveStatusAbsen = (jenis: 'masuk' | 'pulang') => {
  const today = new Date().toISOString().split('T')[0];
  const statusAbsen = {
    jenis: jenis,
    tanggal: today,
    timestamp: new Date().toISOString()
  };
  localStorage.setItem('status_absen', JSON.stringify(statusAbsen));
};
```

#### `checkStatusAbsenFromStorage()`
```typescript
const checkStatusAbsenFromStorage = () => {
  try {
    const statusAbsenStr = localStorage.getItem('status_absen');
    if (!statusAbsenStr) return 'masuk';

    const statusAbsen = JSON.parse(statusAbsenStr);
    const today = new Date().toISOString().split('T')[0];
    
    // Reset jika data bukan hari ini
    if (statusAbsen.tanggal !== today) return 'masuk';

    // Tentukan jenis absensi berikutnya
    return statusAbsen.jenis === 'masuk' ? 'pulang' : 'masuk';
  } catch (error) {
    return 'masuk';
  }
};
```

### Titik Penyimpanan Status
Status absen disimpan di localStorage pada saat:
1. **Absensi berhasil dikirim ke server** (mode online)
2. **Absensi disimpan offline** (mode offline)
3. **Server error tapi data disimpan offline**
4. **Network error tapi data disimpan offline**

### Event Listeners
- **online**: Recheck status dari server, update localStorage
- **offline**: Beralih ke mode localStorage

## Keuntungan Sistem

### 1. Konsistensi Data
- Status absen tetap akurat meskipun aplikasi offline
- Tidak ada kebingungan antara absen masuk/pulang

### 2. User Experience
- Aplikasi tetap berfungsi normal dalam kondisi offline
- Indikator visual untuk mode offline
- Automatic sync ketika kembali online

### 3. Data Integrity
- Reset otomatis untuk hari baru
- Fallback ke default "masuk" jika ada error
- Backup data di localStorage

## Skenario Penggunaan

### Skenario 1: Normal Online
1. User buka aplikasi (online)
2. Cek status dari server
3. Tampilkan jenis absensi yang sesuai
4. Setelah absen, simpan status ke localStorage

### Skenario 2: Offline dari Awal
1. User buka aplikasi (offline)
2. Cek status dari localStorage
3. Tampilkan jenis absensi berdasarkan data lokal
4. Setelah absen, update status di localStorage

### Skenario 3: Online → Offline
1. User mulai online, kemudian koneksi terputus
2. Aplikasi beralih ke mode localStorage
3. Status absen tetap konsisten

### Skenario 4: Offline → Online
1. User mulai offline, kemudian koneksi kembali
2. Aplikasi sync data offline
3. Recheck status dari server
4. Update localStorage dengan data terbaru

## Sinkronisasi Offline yang Diperbaiki

### Masalah Sebelumnya: Data Terkirim 3x
**Penyebab:**
- Fungsi `syncOfflineData` duplikat di `Absensi.tsx` dan `Home.tsx`
- Multiple event listeners untuk online/offline
- Tidak ada debouncing atau flag untuk mencegah multiple calls

**Dampak:**
- Data absensi terkirim 3x ke server
- Duplikasi data di database
- Performance issue

### Solusi yang Diimplementasikan

#### 1. Centralized Sync Function
- ✅ Hanya satu fungsi `syncOfflineData` di `Absensi.tsx`
- ✅ Hapus fungsi duplikat di `Home.tsx`
- ✅ Semua sync logic terpusat

#### 2. Sync Lock Mechanism
```typescript
// Cek apakah sedang ada proses sync yang berjalan
const syncInProgress = localStorage.getItem('sync_in_progress');
if (syncInProgress) {
  console.log('Sync already in progress, skipping...');
  return;
}

// Set flag dengan timestamp
localStorage.setItem('sync_in_progress', Date.now().toString());
```

#### 3. Debouncing
```typescript
const debouncedSyncOfflineData = () => {
  if (syncTimeoutRef.current) {
    clearTimeout(syncTimeoutRef.current);
  }

  syncTimeoutRef.current = setTimeout(() => {
    syncOfflineData();
  }, 1000); // Delay 1 detik
};
```

#### 4. Stuck Flag Cleanup
```typescript
const cleanupSyncFlag = () => {
  const syncInProgress = localStorage.getItem('sync_in_progress');
  if (syncInProgress) {
    const now = Date.now();
    const flagTime = parseInt(syncInProgress);

    // Jika flag sudah lebih dari 30 detik, hapus
    if (now - flagTime > 30000) {
      localStorage.removeItem('sync_in_progress');
    }
  }
};
```

#### 5. Data Deduplication
```typescript
// Cek apakah data sudah pernah di-sync
if (data.synced) {
  console.log('Data already synced, removing from queue:', data.id);
  offlineQueue.splice(i, 1);
  continue;
}
```

### Hasil Perbaikan
- ✅ **No More Duplicates**: Data hanya terkirim 1x
- ✅ **Performance**: Sync lebih efisien
- ✅ **Reliability**: Tidak ada stuck sync process
- ✅ **Clean Code**: Single source of truth untuk sync logic
- ✅ **Auto Sync on Home**: Sinkronisasi otomatis saat halaman Home dimuat

## Sinkronisasi Dipindah ke Halaman Home

### Perubahan Arsitektur

**Sebelumnya:**
- ❌ Sync logic di halaman `Absensi.tsx`
- ❌ Sync hanya terjadi saat buka halaman absensi
- ❌ User harus manual ke halaman absensi untuk sync

**Sekarang:**
- ✅ Sync logic di halaman `Home.tsx`
- ✅ Sync otomatis saat halaman Home dimuat
- ✅ Sync otomatis saat device kembali online
- ✅ User experience yang lebih baik

### Implementasi di Home.tsx

#### 1. Auto Sync on Load
```typescript
useEffect(() => {
  fetchAbsensiHariIni();

  // Bersihkan flag sync yang mungkin tertinggal
  cleanupSyncFlag();

  // Sync data offline jika ada koneksi internet
  if (navigator.onLine) {
    debouncedSyncOfflineData();
  }
}, []);
```

#### 2. Auto Sync on Online Event
```typescript
useEffect(() => {
  const handleOnline = () => {
    console.log('[Home] Device is online, syncing offline data...');
    debouncedSyncOfflineData();
  };

  window.addEventListener('online', handleOnline);
  return () => window.removeEventListener('online', handleOnline);
}, []);
```

#### 3. Auto Sync on Visibility Change
```typescript
useEffect(() => {
  const handleVisibilityChange = () => {
    if (!document.hidden) {
      fetchAbsensiHariIni();
      // Sync data offline jika ada koneksi
      if (navigator.onLine) {
        debouncedSyncOfflineData();
      }
    }
  };

  document.addEventListener('visibilitychange', handleVisibilityChange);
  return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
}, []);
```

### Keuntungan Pemindahan ke Home

1. **🏠 Seamless Experience**
   - Sync otomatis saat user buka aplikasi
   - Tidak perlu manual ke halaman absensi

2. **⚡ Faster Sync**
   - Home adalah halaman yang paling sering dibuka
   - Data ter-sync lebih cepat

3. **🔄 Better UX**
   - User langsung melihat data terbaru di Home
   - Notifikasi sync success di halaman utama

4. **📱 Mobile-First**
   - Sesuai dengan behavior mobile app
   - Background sync yang natural

### Cleanup di Absensi.tsx

- ✅ Hapus fungsi `syncOfflineData`
- ✅ Hapus fungsi `debouncedSyncOfflineData`
- ✅ Hapus fungsi `cleanupSyncFlag`
- ✅ Hapus `syncTimeoutRef`
- ✅ Update event handlers untuk tidak sync

### Testing

**Skenario 1: Buka Aplikasi**
1. User buka aplikasi → Home dimuat
2. Auto sync data offline (jika ada)
3. Data terbaru tampil di Home

**Skenario 2: Kembali Online**
1. Device offline → online
2. Event listener triggered di Home
3. Auto sync data offline
4. Notifikasi sync success

**Skenario 3: Switch Tab/App**
1. User switch ke app lain → kembali
2. Visibility change triggered
3. Auto sync jika online
4. Data refresh

## Troubleshooting

### Masalah: Status Absen Offline Tidak Akurat

#### Gejala
- Saat offline, aplikasi tidak mendeteksi bahwa sudah absen masuk
- Masih menampilkan "absen masuk" padahal seharusnya "absen pulang"

#### Penyebab
- Data offline queue atau backup tidak terbaca dengan benar
- Format data tidak sesuai ekspektasi

#### Solusi
1. **Debug via Console Browser**:
   ```javascript
   // Panggil fungsi debug di console browser
   debugStatusAbsen()
   ```

2. **Cek Data Manual**:
   ```javascript
   // Cek offline queue
   console.log(JSON.parse(localStorage.getItem('offline_absensi_queue') || '[]'))

   // Cek backup data
   console.log(JSON.parse(localStorage.getItem('absensi_backup') || '[]'))

   // Cek status absen
   console.log(JSON.parse(localStorage.getItem('status_absen') || '{}'))
   ```

3. **Reset Data Jika Perlu**:
   ```javascript
   // Hapus semua data absen
   localStorage.removeItem('status_absen')
   localStorage.removeItem('offline_absensi_queue')
   localStorage.removeItem('absensi_backup')
   ```

#### Logika Pengecekan Offline
Sistem mengecek status absen offline dengan urutan:
1. **Offline Queue**: Data absensi yang belum terkirim ke server
2. **Backup Data**: Data absensi yang sudah berhasil dikirim ke server
3. **Gabungan**: Analisis semua data hari ini untuk menentukan status

### Data Tidak Konsisten
- Hapus localStorage: `localStorage.removeItem('status_absen')`
- Restart aplikasi untuk reset ke default

### Status Salah Setelah Tengah Malam
- Sistem otomatis reset untuk hari baru
- Jika masih bermasalah, cek format tanggal di localStorage

### Mode Offline Tidak Berfungsi
- Pastikan event listener online/offline terpasang
- Cek console untuk error parsing localStorage
- Gunakan fungsi `debugStatusAbsen()` di console untuk analisis detail

### UI Debug Info
Saat offline, aplikasi menampilkan:
- Status koneksi (Online/Offline)
- Informasi "Sudah absen masuk hari ini" atau "Belum absen masuk hari ini"
- Jumlah data yang menunggu sinkronisasi
