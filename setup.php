<?php
// Setup script untuk membuat folder dan file yang diperlukan
header('Content-Type: text/html; charset=utf-8');

echo "<h1>Setup Backend Lembur</h1>";

$success = true;

// 1. Buat folder uploads
echo "<h2>1. Setup Upload Folder</h2>";
$upload_dir = 'uploads/';
if (!is_dir($upload_dir)) {
    if (mkdir($upload_dir, 0755, true)) {
        echo "✅ Created folder: <strong>$upload_dir</strong><br>";
    } else {
        echo "❌ Failed to create folder: <strong>$upload_dir</strong><br>";
        $success = false;
    }
} else {
    echo "✅ Folder already exists: <strong>$upload_dir</strong><br>";
}

// Set permissions
if (is_dir($upload_dir)) {
    if (chmod($upload_dir, 0755)) {
        echo "✅ Set permissions for: <strong>$upload_dir</strong><br>";
    } else {
        echo "⚠️ Could not set permissions for: <strong>$upload_dir</strong><br>";
    }
}

// 2. Buat folder logs
echo "<h2>2. Setup Log Folder</h2>";
$log_dir = 'logs/';
if (!is_dir($log_dir)) {
    if (mkdir($log_dir, 0755, true)) {
        echo "✅ Created folder: <strong>$log_dir</strong><br>";
    } else {
        echo "❌ Failed to create folder: <strong>$log_dir</strong><br>";
        $success = false;
    }
} else {
    echo "✅ Folder already exists: <strong>$log_dir</strong><br>";
}

// 3. Buat folder config jika belum ada
echo "<h2>3. Setup Config Folder</h2>";
$config_dir = 'config/';
if (!is_dir($config_dir)) {
    if (mkdir($config_dir, 0755, true)) {
        echo "✅ Created folder: <strong>$config_dir</strong><br>";
    } else {
        echo "❌ Failed to create folder: <strong>$config_dir</strong><br>";
        $success = false;
    }
} else {
    echo "✅ Folder already exists: <strong>$config_dir</strong><br>";
}

// 4. Buat folder api jika belum ada
echo "<h2>4. Setup API Folder</h2>";
$api_dir = 'api/';
if (!is_dir($api_dir)) {
    if (mkdir($api_dir, 0755, true)) {
        echo "✅ Created folder: <strong>$api_dir</strong><br>";
    } else {
        echo "❌ Failed to create folder: <strong>$api_dir</strong><br>";
        $success = false;
    }
} else {
    echo "✅ Folder already exists: <strong>$api_dir</strong><br>";
}

// 5. Test write permissions
echo "<h2>5. Test Write Permissions</h2>";
$test_file = $upload_dir . 'test_write.txt';
if (file_put_contents($test_file, 'test') !== false) {
    echo "✅ Upload folder is writable<br>";
    unlink($test_file); // Delete test file
} else {
    echo "❌ Upload folder is not writable<br>";
    $success = false;
}

// 6. Create .htaccess for uploads folder (security)
echo "<h2>6. Setup Security</h2>";
$htaccess_content = "# Prevent direct access to uploaded files
<Files *.php>
    Order Deny,Allow
    Deny from all
</Files>

# Allow only image files
<FilesMatch \"\.(jpg|jpeg|png|gif)$\">
    Order Allow,Deny
    Allow from all
</FilesMatch>";

$htaccess_file = $upload_dir . '.htaccess';
if (file_put_contents($htaccess_file, $htaccess_content) !== false) {
    echo "✅ Created security file: <strong>.htaccess</strong><br>";
} else {
    echo "⚠️ Could not create security file: <strong>.htaccess</strong><br>";
}

// 7. Database connection test
echo "<h2>7. Test Database Connection</h2>";
if (file_exists('config/database.php')) {
    try {
        require_once 'config/database.php';
        $database = new Database();
        $conn = $database->getConnection();
        if ($conn) {
            echo "✅ Database connection: <strong style='color: green;'>SUCCESS</strong><br>";
            
            // Check if lembur table exists
            $stmt = $conn->prepare("SHOW TABLES LIKE 'lembur'");
            $stmt->execute();
            if ($stmt->rowCount() > 0) {
                echo "✅ Table 'lembur': <strong style='color: green;'>EXISTS</strong><br>";
            } else {
                echo "⚠️ Table 'lembur': <strong style='color: orange;'>NOT EXISTS</strong><br>";
                echo "<p>Silakan jalankan script SQL dari file <code>database/lembur_table.sql</code></p>";
            }
        } else {
            echo "❌ Database connection: <strong style='color: red;'>FAILED</strong><br>";
            $success = false;
        }
    } catch (Exception $e) {
        echo "❌ Database error: <strong style='color: red;'>" . $e->getMessage() . "</strong><br>";
        echo "<p>Pastikan konfigurasi database di <code>config/database.php</code> sudah benar.</p>";
        $success = false;
    }
} else {
    echo "❌ Database config file not found: <strong>config/database.php</strong><br>";
    $success = false;
}

// 8. Summary
echo "<h2>8. Setup Summary</h2>";
if ($success) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<strong>✅ Setup completed successfully!</strong><br>";
    echo "Anda dapat melanjutkan untuk test API di: <a href='api/test_lembur.php'>api/test_lembur.php</a>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<strong>❌ Setup completed with errors!</strong><br>";
    echo "Silakan perbaiki error di atas sebelum melanjutkan.";
    echo "</div>";
}

// 9. Next steps
echo "<h2>9. Next Steps</h2>";
echo "<ol>";
echo "<li>Pastikan konfigurasi database di <code>config/database.php</code> sudah benar</li>";
echo "<li>Jalankan script SQL dari <code>database/lembur_table.sql</code> untuk membuat tabel</li>";
echo "<li>Test API di <a href='api/test_lembur.php'>api/test_lembur.php</a></li>";
echo "<li>Test frontend di aplikasi React</li>";
echo "</ol>";

// 10. File structure
echo "<h2>10. File Structure</h2>";
echo "<pre>";
echo "project/
├── api/
│   ├── lembur.php          (Main API file)
│   └── test_lembur.php     (API test file)
├── config/
│   └── database.php        (Database configuration)
├── uploads/                (Photo storage)
│   └── .htaccess          (Security file)
├── logs/                   (Error logs)
├── database/
│   └── lembur_table.sql   (Database schema)
└── setup.php              (This file)";
echo "</pre>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}
h1, h2 {
    color: #333;
}
code {
    background: #f4f4f4;
    padding: 2px 4px;
    border-radius: 3px;
}
pre {
    background: #f4f4f4;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
}
</style>
