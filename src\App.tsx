import React, { useEffect } from 'react';
import { Redirect, Route, useHistory } from 'react-router-dom';
import { IonApp, IonRouterOutlet, setupIonicReact } from '@ionic/react';
import { IonReactRouter } from '@ionic/react-router';
import Home from './pages/Home';
import Login from './pages/Login';
import Absensi from './pages/Absensi';
import Histori from './pages/Histori';
import IzinDinas from './pages/IzinDinas';
import HistoriIzinDinas from './pages/HistoriIzinDinas';
import Rapat from './pages/Rapat';
import LaporanHarian from './pages/LaporanHarian';
import Profile from './pages/Profile';
import BarcodeTest from './components/BarcodeTest';
import { fetchAndStoreLokasi } from './utils/lokasi';
import { fetchAndStoreBidang } from './utils/bidang';
import { fetchAndStoreJamKerja } from './utils/jamKerja';
import { fetchAndStoreJamKerjaBidang } from './utils/jamKerjaBidang';
import GantiPassword from './pages/ganti_password';

/* Core CSS required for Ionic components to work properly */
import '@ionic/react/css/core.css';

/* Basic CSS for apps built with Ionic */
import '@ionic/react/css/normalize.css';
import '@ionic/react/css/structure.css';
import '@ionic/react/css/typography.css';

/* Optional CSS utils that can be commented out */
import '@ionic/react/css/padding.css';
import '@ionic/react/css/float-elements.css';
import '@ionic/react/css/text-alignment.css';
import '@ionic/react/css/text-transformation.css';
import '@ionic/react/css/flex-utils.css';
import '@ionic/react/css/display.css';

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* import '@ionic/react/css/palettes/dark.always.css'; */
/* import '@ionic/react/css/palettes/dark.class.css'; */
import '@ionic/react/css/palettes/dark.system.css';

/* Theme variables */
import './theme/variables.css';

setupIonicReact();

const PrivateRoute = ({ children, ...rest }: any) => {
  const isLoggedIn = !!localStorage.getItem('user');
  return (
    <Route
      {...rest}
      render={({ location }) =>
        isLoggedIn ? (
          children
        ) : (
          <Redirect to={{ pathname: '/login', state: { from: location } }} />
        )
      }
    />
  );
};

const Logout: React.FC = () => {
  useEffect(() => {
    localStorage.removeItem('user');
  }, []);
  return <Redirect to="/login" />;
};

const App: React.FC = () => {
  useEffect(() => {
    fetchAndStoreLokasi();
    fetchAndStoreBidang();
    fetchAndStoreJamKerja();
    fetchAndStoreJamKerjaBidang();
  }, []);

  return (
  <IonApp>
    <IonReactRouter>
      <IonRouterOutlet>
          <PrivateRoute exact path="/home">
          <Home />
          </PrivateRoute>
          <PrivateRoute exact path="/absensi">
            <Absensi />
          </PrivateRoute>
          <PrivateRoute exact path="/histori">
            <Histori />
          </PrivateRoute>
          <PrivateRoute exact path="/izin-dinas">
            <IzinDinas />
          </PrivateRoute>
          <PrivateRoute exact path="/histori-izin-dinas">
            <HistoriIzinDinas />
          </PrivateRoute>
          <PrivateRoute exact path="/rapat">
            <Rapat />
          </PrivateRoute>
          <PrivateRoute exact path="/laporan-harian">
            <LaporanHarian />
          </PrivateRoute>
          <PrivateRoute exact path="/profile">
            <Profile />
          </PrivateRoute>
          <PrivateRoute exact path="/barcode-test">
            <BarcodeTest />
          </PrivateRoute>
          <PrivateRoute exact path="/ganti-password">
            <GantiPassword />
          </PrivateRoute>
          <Route exact path="/logout" render={() => {
            localStorage.removeItem('user');
            window.location.replace('/login');
            return null;
          }} />
          <Route exact path="/login" render={() => {
            const isLoggedIn = !!localStorage.getItem('user');
            return isLoggedIn ? <Redirect to="/home" /> : <Login />;
          }} />
        <Route exact path="/">
            <Redirect to="/login" />
        </Route>
      </IonRouterOutlet>
    </IonReactRouter>
  </IonApp>
);
};

export default App;
