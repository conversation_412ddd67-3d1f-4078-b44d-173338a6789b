# Setup Backend untuk Fitur Lembur

## 🚀 Quick Start

### 1. Upload File Backend
Upload file-file berikut ke server Anda:
- `api/lembur.php` - Main API file
- `config/database.php` - Database configuration
- `setup.php` - Setup script
- `database/lembur_table.sql` - Database schema

### 2. Jalankan Setup
Buka browser dan akses: `http://your-domain.com/setup.php`

Setup script akan otomatis:
- ✅ Membuat folder `uploads/` dan `logs/`
- ✅ Set permissions yang benar
- ✅ Test database connection
- ✅ Membuat file security `.htaccess`

### 3. Konfigurasi Database
Edit file `config/database.php` sesuai dengan setup database Anda:

```php
private $host = "localhost";        // Database host
private $db_name = "absensipdam";   // Database name
private $username = "root";         // Database username
private $password = "";             // Database password
```

### 4. Buat Tabel Database
Jalankan script SQL dari file `database/lembur_table.sql`:

```sql
CREATE TABLE IF NOT EXISTS lembur (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama_karyawan VARCHAR(100) NOT NULL,
    keterangan TEXT,
    tanggal_lembur DATE NOT NULL,
    jam_mulai TIME NOT NULL,
    foto_mulai VARCHAR(255),
    jam_selesai TIME,
    foto_selesai VARCHAR(255),
    status ENUM('Menunggu', 'Disetujui', 'Ditolak') DEFAULT 'Menunggu',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 5. Test API
Akses: `http://your-domain.com/api/test_lembur.php`

## 📁 File Structure
```
project/
├── api/
│   ├── lembur.php          ← Main API file
│   └── test_lembur.php     ← API test file
├── config/
│   └── database.php        ← Database config
├── uploads/                ← Photo storage (auto-created)
│   └── .htaccess          ← Security file
├── logs/                   ← Error logs (auto-created)
├── database/
│   └── lembur_table.sql   ← Database schema
└── setup.php              ← Setup script
```

### Contoh config/database.php:
```php
<?php
class Database {
    private $host = "localhost";
    private $db_name = "nama_database_anda";
    private $username = "username_db";
    private $password = "password_db";
    public $conn;

    public function getConnection() {
        $this->conn = null;
        try {
            $this->conn = new PDO("mysql:host=" . $this->host . ";dbname=" . $this->db_name, $this->username, $this->password);
            $this->conn->exec("set names utf8");
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        return $this->conn;
    }
}
?>
```

## 5. Testing API

### Test GET Request:
```bash
curl -X GET "https://absensiku.trunois.my.id/api/lembur.php" \
  -H "x-api-key: absensiku_api_key_2023" \
  -H "Content-Type: application/json"
```

### Test POST Request:
```bash
curl -X POST "https://absensiku.trunois.my.id/api/lembur.php" \
  -H "x-api-key: absensiku_api_key_2023" \
  -H "Content-Type: application/json" \
  -d '{
    "nama_karyawan": "Test User",
    "keterangan": "Testing lembur",
    "tanggal_lembur": "2024-01-20",
    "jam_mulai": "18:00",
    "foto_mulai": "base64_encoded_image_here"
  }'
```

## 6. Troubleshooting

### Error 500 Internal Server Error
1. **Cek log error server** (biasanya di `/var/log/apache2/error.log` atau `/var/log/nginx/error.log`)
2. **Pastikan tabel database sudah dibuat**
3. **Cek koneksi database**
4. **Pastikan folder uploads ada dan writable**
5. **Cek syntax PHP**

### Error CORS
Pastikan header CORS sudah ada di file API:
```php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, x-api-key');
```

### Error Permission Denied
```bash
chmod 755 api/
chmod 644 api/lembur.php
chmod 755 uploads/
```

## 7. Status Lembur

- **Menunggu**: Lembur sedang berlangsung (belum selesai)
- **Disetujui**: Lembur sudah selesai dan disetujui
- **Ditolak**: Lembur ditolak oleh admin

## 8. Flow Aplikasi

1. **Mulai Lembur**: 
   - Frontend kirim POST dengan data awal
   - Status: 'Menunggu'
   - jam_selesai dan foto_selesai: NULL

2. **Selesai Lembur**:
   - Frontend kirim PUT dengan jam_selesai dan foto_selesai
   - Status: 'Disetujui' (otomatis)

3. **Admin Review** (opsional):
   - Admin bisa mengubah status menjadi 'Ditolak' jika perlu

## 9. File Structure
```
project/
├── api/
│   └── lembur.php
├── config/
│   ├── database.php
│   └── config.php
├── uploads/
│   ├── lembur_mulai_*.jpg
│   └── lembur_selesai_*.jpg
└── database/
    └── lembur_table.sql
```

## 10. Security Notes

- API key validation sudah ada
- Input sanitization melalui PDO prepared statements
- File upload validation (base64 decode)
- CORS headers untuk cross-origin requests
