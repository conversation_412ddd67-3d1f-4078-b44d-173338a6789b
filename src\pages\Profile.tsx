import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonCard,
  IonCardHeader,
  IonCardSubtitle,
  IonCardTitle,
  IonCardContent,
  IonAvatar,
  IonItem,
  IonLabel,
  IonSpinner,
  IonButton,
  useIonRouter,
  IonList,
  IonIcon,
  IonChip,
  IonSkeletonText
} from '@ionic/react';
import { useEffect, useState } from 'react';
import {
  idCardOutline,
  businessOutline,
  briefcaseOutline,
  locationOutline,
  keyOutline
} from 'ionicons/icons';
import './Profile.css';

interface Karyawan {
  id: string;
  nik: string;
  nama: string;
  bidang_id: string;
  bidang: string;
  jabatan: string;
  foto_profil: string;
  lokasi_id: string;
  nama_lokasi: string;
}

const Profile: React.FC = () => {
  const [karyawan, setKaryawan] = useState<Karyawan | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useIonRouter(); // Untuk navigasi

  useEffect(() => {
    const userData = localStorage.getItem('user');
    
    if (userData) {
      const user = JSON.parse(userData);
      const nik = user.nik;
      
      fetch(`https://absensiku.trunois.my.id/api/karyawan.php?api_key=absensiku_api_key_2023&nik=${encodeURIComponent(nik)}`)
        .then(response => response.json())
        .then(data => {
          if (data.status === 'success') {
            setKaryawan(data.data[0]);
          }
          setLoading(false);
        })
        .catch(error => {
          console.error('Error fetching data:', error);
          setLoading(false);
        });
    }
  }, []);

  if (loading) {
    return (
      <IonPage>
        <IonHeader>
          <IonToolbar>
            <IonTitle>Profil Karyawan</IonTitle>
          </IonToolbar>
        </IonHeader>
        <IonContent className="profile-page" fullscreen>
          <div className="profile-hero">
            <div className="avatar-wrap skeleton">
              <IonSkeletonText animated style={{ width: 120, height: 120, borderRadius: '50%' }} />
            </div>
            <div className="name-wrap">
              <IonSkeletonText animated style={{ width: '60%', height: 20, borderRadius: 8 }} />
              <IonSkeletonText animated style={{ width: '40%', height: 16, borderRadius: 8, marginTop: 8 }} />
            </div>
            <div className="chip-wrap">
              <IonSkeletonText animated style={{ width: 120, height: 28, borderRadius: 20 }} />
              <IonSkeletonText animated style={{ width: 140, height: 28, borderRadius: 20 }} />
            </div>
          </div>
          <IonCard className="profile-card glass">
            <IonCardHeader>
              <IonCardTitle>Informasi Karyawan</IonCardTitle>
              <IonCardSubtitle>Memuat data...</IonCardSubtitle>
            </IonCardHeader>
            <IonCardContent>
              <IonList lines="none" className="info-list">
                <IonItem className="info-item">
                  <IonIcon icon={idCardOutline} slot="start" className="info-icon" />
                  <IonLabel>
                    <p className="label">NIK</p>
                    <IonSkeletonText animated style={{ width: '40%', height: 16, borderRadius: 6 }} />
                  </IonLabel>
                </IonItem>
                <IonItem className="info-item">
                  <IonIcon icon={briefcaseOutline} slot="start" className="info-icon" />
                  <IonLabel>
                    <p className="label">Jabatan</p>
                    <IonSkeletonText animated style={{ width: '50%', height: 16, borderRadius: 6 }} />
                  </IonLabel>
                </IonItem>
                <IonItem className="info-item">
                  <IonIcon icon={businessOutline} slot="start" className="info-icon" />
                  <IonLabel>
                    <p className="label">Bidang</p>
                    <IonSkeletonText animated style={{ width: '35%', height: 16, borderRadius: 6 }} />
                  </IonLabel>
                </IonItem>
                <IonItem className="info-item">
                  <IonIcon icon={locationOutline} slot="start" className="info-icon" />
                  <IonLabel>
                    <p className="label">Lokasi</p>
                    <IonSkeletonText animated style={{ width: '45%', height: 16, borderRadius: 6 }} />
                  </IonLabel>
                </IonItem>
              </IonList>
              <div className="action-group">
                <IonButton expand="block" color="warning" disabled>
                  <IonIcon icon={keyOutline} slot="start" />
                  Ganti Password
                </IonButton>
              </div>
            </IonCardContent>
          </IonCard>
        </IonContent>
      </IonPage>
    );
  }

  if (!karyawan) {
    return (
      <IonPage>
        <IonHeader>
          <IonToolbar>
            <IonTitle>Profil Karyawan</IonTitle>
          </IonToolbar>
        </IonHeader>
        <IonContent>
          <p>Data karyawan tidak ditemukan</p>
        </IonContent>
      </IonPage>
    );
  }

  return (
      <IonPage>
        <IonHeader>
          <IonToolbar>
            <IonTitle>Profil Karyawan</IonTitle>
          </IonToolbar>
        </IonHeader>
        <IonContent className="profile-page" fullscreen>
          <div className="profile-hero">
            <div className="avatar-wrap">
              <IonAvatar className="profile-avatar">
                {karyawan.foto_profil ? (
                  <img
                    src={`https://absensiku.trunois.my.id/uploads/${karyawan.foto_profil}`}
                    alt="Foto Profil"
                  />)
                  : (
                  <div className="default-avatar">
                    {karyawan.nama.charAt(0).toUpperCase()}
                  </div>
                )}
              </IonAvatar>
            </div>
            <h2 className="profile-name">{karyawan.nama}</h2>
            <p className="profile-role">{karyawan.jabatan}</p>
            <div className="chip-wrap">
              <IonChip color="light" className="profile-chip">
                <IonIcon icon={businessOutline} />
                <span>{karyawan.bidang}</span>
              </IonChip>
              <IonChip color="light" className="profile-chip">
                <IonIcon icon={locationOutline} />
                <span>{karyawan.nama_lokasi}</span>
              </IonChip>
            </div>
          </div>

          <IonCard className="profile-card glass">
            <IonCardHeader>
              <IonCardTitle>Informasi Karyawan</IonCardTitle>
              <IonCardSubtitle>Detail singkat</IonCardSubtitle>
            </IonCardHeader>
            <IonCardContent>
              <IonList lines="none" className="info-list">
                <IonItem className="info-item">
                  <IonIcon icon={idCardOutline} slot="start" className="info-icon" />
                  <IonLabel>
                    <p className="label">NIK</p>
                    <h3 className="value">{karyawan.nik}</h3>
                  </IonLabel>
                </IonItem>
                <IonItem className="info-item">
                  <IonIcon icon={briefcaseOutline} slot="start" className="info-icon" />
                  <IonLabel>
                    <p className="label">Jabatan</p>
                    <h3 className="value">{karyawan.jabatan}</h3>
                  </IonLabel>
                </IonItem>
                <IonItem className="info-item">
                  <IonIcon icon={businessOutline} slot="start" className="info-icon" />
                  <IonLabel>
                    <p className="label">Bidang</p>
                    <h3 className="value">{karyawan.bidang}</h3>
                  </IonLabel>
                </IonItem>
                <IonItem className="info-item">
                  <IonIcon icon={locationOutline} slot="start" className="info-icon" />
                  <IonLabel>
                    <p className="label">Lokasi</p>
                    <h3 className="value">{karyawan.nama_lokasi}</h3>
                  </IonLabel>
                </IonItem>
              </IonList>

              <div className="action-group">
                <IonButton expand="block" color="warning" onClick={() => router.push('/ganti-password', 'forward')}>
                  <IonIcon icon={keyOutline} slot="start" />
                  Ganti Password
                </IonButton>
              </div>
            </IonCardContent>
          </IonCard>
        </IonContent>
      </IonPage>
  );
};

export default Profile;
