-- Tabel untuk fitur lembur
-- Jalankan script ini di database MySQL/MariaDB Anda

CREATE TABLE IF NOT EXISTS lembur (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama_karyawan VARCHAR(100) NOT NULL,
    keterangan TEXT,
    tanggal_lembur DATE NOT NULL,
    jam_mulai TIME NOT NULL,
    foto_mulai VARCHAR(255),     -- simpan path file foto atau URL
    jam_selesai TIME,
    foto_selesai VARCHAR(255),   -- simpan path file foto atau URL
    status ENUM('Menunggu', 'Disetujui', 'Ditolak') DEFAULT 'Menunggu',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Index untuk performa yang lebih baik
CREATE INDEX idx_nama_karyawan ON lembur(nama_karyawan);
CREATE INDEX idx_tanggal_lembur ON lembur(tanggal_lembur);
CREATE INDEX idx_status ON lembur(status);
CREATE INDEX idx_created_at ON lembur(created_at);

-- Contoh data dummy untuk testing (opsional)
INSERT INTO lembur (nama_karyawan, keterangan, tanggal_lembur, jam_mulai, foto_mulai, jam_selesai, foto_selesai, status) VALUES
('John Doe', 'Menyelesaikan laporan bulanan', '2024-01-15', '18:00:00', '../uploads/lembur_mulai_1705320000.jpg', '20:30:00', '../uploads/lembur_selesai_1705329000.jpg', 'Disetujui'),
('Jane Smith', 'Maintenance server', '2024-01-16', '19:00:00', '../uploads/lembur_mulai_1705406400.jpg', '22:00:00', '../uploads/lembur_selesai_1705417200.jpg', 'Disetujui'),
('Bob Wilson', 'Persiapan presentasi client', '2024-01-17', '17:30:00', '../uploads/lembur_mulai_1705491000.jpg', NULL, NULL, 'Menunggu');
