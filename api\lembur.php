<?php
// Error reporting untuk debugging (hapus di production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database config
require_once '../config/database.php';

// CORS Headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, x-api-key, Authorization');
header('Content-Type: application/json; charset=utf-8');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Function untuk validasi API Key
function validateApiKey($api_key) {
    return $api_key === 'absensiku_api_key_2023';
}

// Function untuk response JSON
function sendResponse($status, $message, $data = null, $http_code = 200) {
    http_response_code($http_code);
    $response = [
        'status' => $status,
        'message' => $message
    ];
    if ($data !== null) {
        $response['data'] = $data;
    }
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit();
}

// Function untuk log error
function logError($message, $error = null) {
    $log = date('Y-m-d H:i:s') . " - " . $message;
    if ($error) {
        $log .= " - Error: " . $error;
    }
    error_log($log . "\n", 3, '../logs/lembur_error.log');
}

try {
    // Validasi API key
    $headers = getallheaders();
    $api_key = isset($headers['x-api-key']) ? $headers['x-api-key'] : 
               (isset($headers['X-Api-Key']) ? $headers['X-Api-Key'] : null);
    
    if (!$api_key || !validateApiKey($api_key)) {
        sendResponse('error', 'Invalid API Key', null, 401);
    }

    // Koneksi database
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        logError('Database connection failed');
        sendResponse('error', 'Database connection failed', null, 500);
    }

    // Get request method
    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            handleGet($conn);
            break;
        case 'POST':
            handlePost($conn);
            break;
        case 'PUT':
            handlePut($conn);
            break;
        default:
            sendResponse('error', 'Method not allowed', null, 405);
    }

} catch (Exception $e) {
    logError('Unexpected error', $e->getMessage());
    sendResponse('error', 'Internal server error', null, 500);
}

// Handle GET request - Ambil data lembur
function handleGet($conn) {
    try {
        $sql = "SELECT 
                    id,
                    nama_karyawan,
                    keterangan,
                    tanggal_lembur,
                    jam_mulai,
                    foto_mulai,
                    jam_selesai,
                    foto_selesai,
                    status,
                    created_at,
                    updated_at
                FROM lembur 
                ORDER BY created_at DESC";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Convert data untuk frontend
        foreach ($result as &$row) {
            $row['id'] = (string)$row['id'];
            // Format tanggal dan waktu jika perlu
            if ($row['tanggal_lembur']) {
                $row['tanggal_lembur'] = date('Y-m-d', strtotime($row['tanggal_lembur']));
            }
            if ($row['jam_mulai']) {
                $row['jam_mulai'] = date('H:i', strtotime($row['jam_mulai']));
            }
            if ($row['jam_selesai']) {
                $row['jam_selesai'] = date('H:i', strtotime($row['jam_selesai']));
            }
        }
        
        echo json_encode($result, JSON_UNESCAPED_UNICODE);
        
    } catch (PDOException $e) {
        logError('GET request failed', $e->getMessage());
        sendResponse('error', 'Failed to fetch data', null, 500);
    }
}

// Handle POST request - Tambah lembur baru
function handlePost($conn) {
    try {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            sendResponse('error', 'Invalid JSON format', null, 400);
        }
        
        // Validasi data required
        $required_fields = ['nama_karyawan', 'tanggal_lembur', 'jam_mulai', 'foto_mulai'];
        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                sendResponse('error', "Field '$field' is required", null, 400);
            }
        }
        
        // Validasi format tanggal
        if (!validateDate($data['tanggal_lembur'])) {
            sendResponse('error', 'Invalid date format. Use YYYY-MM-DD', null, 400);
        }
        
        // Validasi format jam
        if (!validateTime($data['jam_mulai'])) {
            sendResponse('error', 'Invalid time format. Use HH:MM', null, 400);
        }
        
        // Cek apakah sudah ada lembur aktif untuk karyawan ini
        $check_sql = "SELECT id FROM lembur 
                      WHERE nama_karyawan = :nama_karyawan 
                      AND status = 'Menunggu' 
                      AND jam_selesai IS NULL";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bindValue(':nama_karyawan', $data['nama_karyawan']);
        $check_stmt->execute();
        
        if ($check_stmt->rowCount() > 0) {
            sendResponse('error', 'Anda masih memiliki lembur aktif yang belum diselesaikan', null, 400);
        }
        
        // Simpan foto
        $foto_path = null;
        if (!empty($data['foto_mulai'])) {
            $foto_path = savePhoto($data['foto_mulai'], 'mulai');
            if (!$foto_path) {
                sendResponse('error', 'Failed to save photo', null, 500);
            }
        }
        
        // Insert data
        $sql = "INSERT INTO lembur 
                (nama_karyawan, keterangan, tanggal_lembur, jam_mulai, foto_mulai, status) 
                VALUES 
                (:nama_karyawan, :keterangan, :tanggal_lembur, :jam_mulai, :foto_mulai, :status)";
        
        $stmt = $conn->prepare($sql);
        $stmt->bindValue(':nama_karyawan', $data['nama_karyawan']);
        $stmt->bindValue(':keterangan', $data['keterangan'] ?? '');
        $stmt->bindValue(':tanggal_lembur', $data['tanggal_lembur']);
        $stmt->bindValue(':jam_mulai', $data['jam_mulai']);
        $stmt->bindValue(':foto_mulai', $foto_path);
        $stmt->bindValue(':status', $data['status'] ?? 'Menunggu');
        
        if ($stmt->execute()) {
            $id = $conn->lastInsertId();
            sendResponse('success', 'Lembur berhasil dimulai', ['id' => $id]);
        } else {
            sendResponse('error', 'Failed to save data', null, 500);
        }
        
    } catch (PDOException $e) {
        logError('POST request failed', $e->getMessage());
        sendResponse('error', 'Database error', null, 500);
    } catch (Exception $e) {
        logError('POST request error', $e->getMessage());
        sendResponse('error', 'Internal error', null, 500);
    }
}

// Handle PUT request - Update lembur selesai
function handlePut($conn) {
    try {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            sendResponse('error', 'Invalid JSON format', null, 400);
        }
        
        // Validasi data required
        $required_fields = ['id', 'jam_selesai', 'foto_selesai'];
        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                sendResponse('error', "Field '$field' is required", null, 400);
            }
        }
        
        // Validasi format jam
        if (!validateTime($data['jam_selesai'])) {
            sendResponse('error', 'Invalid time format. Use HH:MM', null, 400);
        }
        
        // Cek apakah lembur exists dan masih aktif
        $check_sql = "SELECT id, jam_mulai FROM lembur WHERE id = :id AND status = 'Menunggu'";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bindValue(':id', $data['id'], PDO::PARAM_INT);
        $check_stmt->execute();
        
        if ($check_stmt->rowCount() === 0) {
            sendResponse('error', 'Lembur tidak ditemukan atau sudah selesai', null, 404);
        }
        
        // Simpan foto selesai
        $foto_path = null;
        if (!empty($data['foto_selesai'])) {
            $foto_path = savePhoto($data['foto_selesai'], 'selesai');
            if (!$foto_path) {
                sendResponse('error', 'Failed to save photo', null, 500);
            }
        }
        
        // Update data
        $sql = "UPDATE lembur 
                SET jam_selesai = :jam_selesai, 
                    foto_selesai = :foto_selesai, 
                    status = :status,
                    updated_at = NOW()
                WHERE id = :id";
        
        $stmt = $conn->prepare($sql);
        $stmt->bindValue(':jam_selesai', $data['jam_selesai']);
        $stmt->bindValue(':foto_selesai', $foto_path);
        $stmt->bindValue(':status', $data['status'] ?? 'Disetujui');
        $stmt->bindValue(':id', $data['id'], PDO::PARAM_INT);
        
        if ($stmt->execute()) {
            sendResponse('success', 'Lembur berhasil diselesaikan');
        } else {
            sendResponse('error', 'Failed to update data', null, 500);
        }
        
    } catch (PDOException $e) {
        logError('PUT request failed', $e->getMessage());
        sendResponse('error', 'Database error', null, 500);
    } catch (Exception $e) {
        logError('PUT request error', $e->getMessage());
        sendResponse('error', 'Internal error', null, 500);
    }
}

// Function untuk save foto
function savePhoto($base64_data, $type) {
    try {
        // Pastikan folder uploads ada
        $upload_dir = '../uploads/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        // Generate filename
        $filename = 'lembur_' . $type . '_' . time() . '_' . uniqid() . '.jpg';
        $file_path = $upload_dir . $filename;
        
        // Decode dan save
        $image_data = base64_decode($base64_data);
        if ($image_data === false) {
            return false;
        }
        
        if (file_put_contents($file_path, $image_data) === false) {
            return false;
        }
        
        return $file_path;
        
    } catch (Exception $e) {
        logError('Save photo failed', $e->getMessage());
        return false;
    }
}

// Function untuk validasi tanggal
function validateDate($date) {
    $d = DateTime::createFromFormat('Y-m-d', $date);
    return $d && $d->format('Y-m-d') === $date;
}

// Function untuk validasi waktu
function validateTime($time) {
    return preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $time);
}
?>
