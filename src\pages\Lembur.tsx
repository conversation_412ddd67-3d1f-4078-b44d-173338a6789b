import React, { useState, useEffect, useRef } from 'react';
import {
  IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonButton, IonIcon, IonText, IonCard, IonCardContent, IonItem, IonLabel, IonInput, IonTextarea, IonSpinner, IonToast, IonButtons, IonBackButton, IonAlert, IonBadge, IonModal, IonList, IonDatetime
} from '@ionic/react';
import {
  cameraOutline, timeOutline, checkmarkCircleOutline, warningOutline, informationCircleOutline, refreshOutline, closeOutline, documentTextOutline, calendarOutline
} from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import './Lembur.css';

interface LemburData {
  id: string;
  nama_karyawan: string;
  keterangan: string;
  tanggal_lembur: string;
  jam_mulai: string;
  foto_mulai: string;
  jam_selesai?: string;
  foto_selesai?: string;
  status: '<PERSON>unggu' | 'Disetujui' | 'Ditolak';
  created_at: string;
  updated_at: string;
}

const Lembur: React.FC = () => {
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const history = useHistory();

  // Camera refs
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Form states
  const [keterangan, setKeterangan] = useState('');
  const [jamMulai, setJamMulai] = useState('');
  const [jamSelesai, setJamSelesai] = useState('');
  const [fotoMulai, setFotoMulai] = useState<string>('');
  const [fotoSelesai, setFotoSelesai] = useState<string>('');

  // Camera states
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [cameraType, setCameraType] = useState<'mulai' | 'selesai'>('mulai');
  const [stream, setStream] = useState<MediaStream | null>(null);

  // UI states
  const [loading, setLoading] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastColor, setToastColor] = useState<'success' | 'danger' | 'warning'>('success');
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');

  // Data states
  const [lemburAktif, setLemburAktif] = useState<LemburData | null>(null);
  const [riwayatLembur, setRiwayatLembur] = useState<LemburData[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  // Get current date and time
  const getCurrentDate = () => {
    return new Date().toISOString().split('T')[0];
  };

  const getCurrentTime = () => {
    const now = new Date();
    return now.toTimeString().slice(0, 5);
  };

  // Initialize camera
  const initCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'user' }
      });
      setStream(mediaStream);
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      showToastMessage('Gagal mengakses kamera', 'danger');
    }
  };

  // Stop camera
  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
  };

  // Capture photo
  const capturePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const canvas = canvasRef.current;
      const video = videoRef.current;
      const context = canvas.getContext('2d');
      
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      if (context) {
        context.drawImage(video, 0, 0);
        const imageData = canvas.toDataURL('image/jpeg', 0.8);
        const base64Data = imageData.split(',')[1];
        
        if (cameraType === 'mulai') {
          setFotoMulai(base64Data);
        } else {
          setFotoSelesai(base64Data);
        }
        
        setIsCameraOpen(false);
        stopCamera();
        showToastMessage('Foto berhasil diambil', 'success');
      }
    }
  };

  // Open camera
  const openCamera = (type: 'mulai' | 'selesai') => {
    setCameraType(type);
    setIsCameraOpen(true);
    initCamera();
  };

  // Close camera
  const closeCamera = () => {
    setIsCameraOpen(false);
    stopCamera();
  };

  // Show toast message
  const showToastMessage = (message: string, color: 'success' | 'danger' | 'warning' = 'success') => {
    setToastMessage(message);
    setToastColor(color);
    setShowToast(true);
  };

  // Fetch data lembur
  const fetchLemburData = async () => {
    try {
      setRefreshing(true);
      const response = await fetch('https://absensiku.trunois.my.id/api/lembur.php?api_key=absensiku_api_key_2023', {
        method: 'GET',
        headers: {
          'x-api-key': 'absensiku_api_key_2023',
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        const userLembur = data.filter((item: LemburData) => 
          item.nama_karyawan === user.nama || item.nama_karyawan === user.name
        );
        
        // Cari lembur yang sedang aktif (belum selesai)
        const aktif = userLembur.find((item: LemburData) =>
          item.status === 'Menunggu' && !item.jam_selesai
        );

        setLemburAktif(aktif || null);
        setRiwayatLembur(userLembur.filter((item: LemburData) =>
          (item.status === 'Disetujui' || item.status === 'Ditolak') && item.jam_selesai
        ));
      }
    } catch (error) {
      console.error('Error fetching lembur data:', error);
      showToastMessage('Gagal memuat data lembur', 'danger');
    } finally {
      setRefreshing(false);
    }
  };

  // Submit lembur mulai
  const submitLemburMulai = async () => {
    if (!keterangan.trim()) {
      showToastMessage('Keterangan harus diisi', 'warning');
      return;
    }

    if (!jamMulai) {
      showToastMessage('Jam mulai harus diisi', 'warning');
      return;
    }

    if (!fotoMulai) {
      showToastMessage('Foto mulai harus diambil', 'warning');
      return;
    }

    try {
      setLoading(true);
      const lemburData = {
        nama_karyawan: user.nama || user.name,
        keterangan: keterangan.trim(),
        tanggal_lembur: getCurrentDate(),
        jam_mulai: jamMulai,
        foto_mulai: fotoMulai,
        status: 'Menunggu'
      };

      const response = await fetch('https://absensiku.trunois.my.id/api/lembur.php?api_key=absensiku_api_key_2023', {
        method: 'POST',
        headers: {
          'x-api-key': 'absensiku_api_key_2023',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(lemburData)
      });

      const result = await response.json();

      if (result.status === 'success') {
        showToastMessage('Lembur berhasil dimulai', 'success');
        // Reset form
        setKeterangan('');
        setJamMulai('');
        setFotoMulai('');
        // Refresh data
        fetchLemburData();
      } else {
        showToastMessage(result.message || 'Gagal memulai lembur', 'danger');
      }
    } catch (error) {
      console.error('Error submitting lembur:', error);
      showToastMessage('Terjadi kesalahan saat memulai lembur', 'danger');
    } finally {
      setLoading(false);
    }
  };

  // Submit lembur selesai
  const submitLemburSelesai = async () => {
    if (!lemburAktif) {
      showToastMessage('Tidak ada lembur aktif', 'warning');
      return;
    }

    if (!jamSelesai) {
      showToastMessage('Jam selesai harus diisi', 'warning');
      return;
    }

    if (!fotoSelesai) {
      showToastMessage('Foto selesai harus diambil', 'warning');
      return;
    }

    try {
      setLoading(true);
      const updateData = {
        id: lemburAktif.id,
        jam_selesai: jamSelesai,
        foto_selesai: fotoSelesai,
        status: 'Selesai'
      };

      const response = await fetch('https://absensiku.trunois.my.id/api/lembur.php?api_key=absensiku_api_key_2023', {
        method: 'PUT',
        headers: {
          'x-api-key': 'absensiku_api_key_2023',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      const result = await response.json();

      if (result.status === 'success') {
        showToastMessage('Lembur berhasil diselesaikan', 'success');
        // Reset form
        setJamSelesai('');
        setFotoSelesai('');
        // Refresh data
        fetchLemburData();
      } else {
        showToastMessage(result.message || 'Gagal menyelesaikan lembur', 'danger');
      }
    } catch (error) {
      console.error('Error finishing lembur:', error);
      showToastMessage('Terjadi kesalahan saat menyelesaikan lembur', 'danger');
    } finally {
      setLoading(false);
    }
  };

  // Format time display
  const formatTime = (timeString: string) => {
    return timeString ? timeString.slice(0, 5) : '--:--';
  };

  // Format date display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Calculate duration
  const calculateDuration = (start: string, end: string) => {
    if (!start || !end) return '--:--';

    const startTime = new Date(`2000-01-01 ${start}`);
    const endTime = new Date(`2000-01-01 ${end}`);
    const diff = endTime.getTime() - startTime.getTime();

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    return `${hours}:${minutes.toString().padStart(2, '0')}`;
  };

  // Load data on component mount
  useEffect(() => {
    fetchLemburData();
  }, []);

  // Set default time when component mounts
  useEffect(() => {
    if (!jamMulai && !lemburAktif) {
      setJamMulai(getCurrentTime());
    }
    if (!jamSelesai && lemburAktif) {
      setJamSelesai(getCurrentTime());
    }
  }, [lemburAktif]);

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonButtons slot="start">
            <IonBackButton defaultHref="/home" />
          </IonButtons>
          <IonTitle>Lembur</IonTitle>
          <IonButtons slot="end">
            <IonButton fill="clear" onClick={fetchLemburData} disabled={refreshing}>
              <IonIcon icon={refreshOutline} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      <IonContent fullscreen>
        {/* Status Lembur Aktif */}
        {lemburAktif && (
          <IonCard className="lembur-active-card">
            <IonCardContent>
              <div className="lembur-status-header">
                <IonIcon icon={timeOutline} className="status-icon active" />
                <div>
                  <h3>Lembur Sedang Berlangsung</h3>
                  <p>Dimulai: {formatTime(lemburAktif.jam_mulai)} - {formatDate(lemburAktif.tanggal_lembur)}</p>
                </div>
              </div>
              <div className="lembur-details">
                <p><strong>Keterangan:</strong> {lemburAktif.keterangan}</p>
                <p><strong>Durasi:</strong> {calculateDuration(lemburAktif.jam_mulai, getCurrentTime())}</p>
              </div>
            </IonCardContent>
          </IonCard>
        )}

        {/* Form Mulai Lembur */}
        {!lemburAktif && (
          <IonCard>
            <IonCardContent>
              <div className="form-header">
                <IonIcon icon={documentTextOutline} className="form-icon" />
                <h2>Mulai Lembur</h2>
              </div>

              <IonItem>
                <IonLabel position="stacked">Tanggal Lembur</IonLabel>
                <IonInput
                  value={getCurrentDate()}
                  readonly
                  className="readonly-input"
                />
              </IonItem>

              <IonItem>
                <IonLabel position="stacked">Keterangan Lembur *</IonLabel>
                <IonTextarea
                  value={keterangan}
                  onIonInput={(e) => setKeterangan(e.detail.value!)}
                  placeholder="Masukkan keterangan pekerjaan lembur..."
                  rows={3}
                />
              </IonItem>

              <IonItem>
                <IonLabel position="stacked">Jam Mulai *</IonLabel>
                <IonInput
                  type="time"
                  value={jamMulai}
                  onIonInput={(e) => setJamMulai(e.detail.value!)}
                />
              </IonItem>

              <div className="photo-section">
                <IonLabel>Foto Mulai Lembur *</IonLabel>
                {fotoMulai ? (
                  <div className="photo-preview">
                    <img src={`data:image/jpeg;base64,${fotoMulai}`} alt="Foto Mulai" />
                    <IonButton
                      fill="clear"
                      size="small"
                      onClick={() => setFotoMulai('')}
                      className="remove-photo-btn"
                    >
                      <IonIcon icon={closeOutline} />
                    </IonButton>
                  </div>
                ) : (
                  <IonButton
                    expand="block"
                    fill="outline"
                    onClick={() => openCamera('mulai')}
                    className="camera-btn"
                  >
                    <IonIcon icon={cameraOutline} slot="start" />
                    Ambil Foto Mulai
                  </IonButton>
                )}
              </div>

              <IonButton
                expand="block"
                onClick={submitLemburMulai}
                disabled={loading || !keterangan.trim() || !jamMulai || !fotoMulai}
                className="submit-btn"
              >
                {loading ? <IonSpinner name="crescent" /> : 'Mulai Lembur'}
              </IonButton>
            </IonCardContent>
          </IonCard>
        )}

        {/* Form Selesai Lembur */}
        {lemburAktif && (
          <IonCard>
            <IonCardContent>
              <div className="form-header">
                <IonIcon icon={checkmarkCircleOutline} className="form-icon finish" />
                <h2>Selesai Lembur</h2>
              </div>

              <IonItem>
                <IonLabel position="stacked">Jam Selesai *</IonLabel>
                <IonInput
                  type="time"
                  value={jamSelesai}
                  onIonInput={(e) => setJamSelesai(e.detail.value!)}
                />
              </IonItem>

              <div className="photo-section">
                <IonLabel>Foto Selesai Lembur *</IonLabel>
                {fotoSelesai ? (
                  <div className="photo-preview">
                    <img src={`data:image/jpeg;base64,${fotoSelesai}`} alt="Foto Selesai" />
                    <IonButton
                      fill="clear"
                      size="small"
                      onClick={() => setFotoSelesai('')}
                      className="remove-photo-btn"
                    >
                      <IonIcon icon={closeOutline} />
                    </IonButton>
                  </div>
                ) : (
                  <IonButton
                    expand="block"
                    fill="outline"
                    onClick={() => openCamera('selesai')}
                    className="camera-btn"
                  >
                    <IonIcon icon={cameraOutline} slot="start" />
                    Ambil Foto Selesai
                  </IonButton>
                )}
              </div>

              <IonButton
                expand="block"
                onClick={submitLemburSelesai}
                disabled={loading || !jamSelesai || !fotoSelesai}
                className="submit-btn finish"
              >
                {loading ? <IonSpinner name="crescent" /> : 'Selesai Lembur'}
              </IonButton>
            </IonCardContent>
          </IonCard>
        )}

        {/* Riwayat Lembur */}
        {riwayatLembur.length > 0 && (
          <IonCard>
            <IonCardContent>
              <div className="form-header">
                <IonIcon icon={calendarOutline} className="form-icon" />
                <h2>Riwayat Lembur</h2>
              </div>

              <IonList>
                {riwayatLembur.map((item, index) => (
                  <IonItem key={index} className="history-item">
                    <div className="history-content">
                      <div className="history-header">
                        <h3>{formatDate(item.tanggal_lembur)}</h3>
                        <IonBadge color="success">{item.status}</IonBadge>
                      </div>
                      <p><strong>Keterangan:</strong> {item.keterangan}</p>
                      <div className="time-info">
                        <span>Mulai: {formatTime(item.jam_mulai)}</span>
                        <span>Selesai: {formatTime(item.jam_selesai || '')}</span>
                        <span>Durasi: {calculateDuration(item.jam_mulai, item.jam_selesai || '')}</span>
                      </div>
                    </div>
                  </IonItem>
                ))}
              </IonList>
            </IonCardContent>
          </IonCard>
        )}

        {/* Camera Modal */}
        <IonModal isOpen={isCameraOpen} onDidDismiss={closeCamera}>
          <IonHeader>
            <IonToolbar>
              <IonTitle>Ambil Foto {cameraType === 'mulai' ? 'Mulai' : 'Selesai'}</IonTitle>
              <IonButtons slot="end">
                <IonButton fill="clear" onClick={closeCamera}>
                  <IonIcon icon={closeOutline} />
                </IonButton>
              </IonButtons>
            </IonToolbar>
          </IonHeader>
          <IonContent>
            <div className="camera-container">
              <video
                ref={videoRef}
                autoPlay
                playsInline
                className="camera-video"
              />
              <canvas
                ref={canvasRef}
                style={{ display: 'none' }}
              />
              <div className="camera-controls">
                <IonButton
                  expand="block"
                  onClick={capturePhoto}
                  className="capture-btn"
                >
                  <IonIcon icon={cameraOutline} slot="start" />
                  Ambil Foto
                </IonButton>
              </div>
            </div>
          </IonContent>
        </IonModal>

        {/* Toast */}
        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={3000}
          color={toastColor}
        />

        {/* Alert */}
        <IonAlert
          isOpen={showAlert}
          onDidDismiss={() => setShowAlert(false)}
          header="Informasi"
          message={alertMessage}
          buttons={['OK']}
        />
      </IonContent>
    </IonPage>
  );
};

export default Lembur;
