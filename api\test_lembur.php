<?php
// File untuk test API lembur
header('Content-Type: text/html; charset=utf-8');

// Include database config
require_once '../config/database.php';

echo "<h1>Test API Lembur</h1>";

// Test 1: Database Connection
echo "<h2>1. Test Database Connection</h2>";
try {
    $database = new Database();
    $conn = $database->getConnection();
    if ($conn) {
        echo "✅ Database connection: <strong style='color: green;'>SUCCESS</strong><br>";
        
        // Test apakah tabel lembur ada
        $stmt = $conn->prepare("SHOW TABLES LIKE 'lembur'");
        $stmt->execute();
        if ($stmt->rowCount() > 0) {
            echo "✅ Table 'lembur': <strong style='color: green;'>EXISTS</strong><br>";
            
            // Test struktur tabel
            $stmt = $conn->prepare("DESCRIBE lembur");
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "✅ Table structure: <strong style='color: green;'>OK</strong><br>";
            echo "<details><summary>Show columns</summary><pre>";
            foreach ($columns as $column) {
                echo $column['Field'] . " - " . $column['Type'] . "\n";
            }
            echo "</pre></details>";
        } else {
            echo "❌ Table 'lembur': <strong style='color: red;'>NOT EXISTS</strong><br>";
            echo "<p>Jalankan script SQL berikut:</p>";
            echo "<textarea rows='10' cols='80' readonly>";
            echo "CREATE TABLE IF NOT EXISTS lembur (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama_karyawan VARCHAR(100) NOT NULL,
    keterangan TEXT,
    tanggal_lembur DATE NOT NULL,
    jam_mulai TIME NOT NULL,
    foto_mulai VARCHAR(255),
    jam_selesai TIME,
    foto_selesai VARCHAR(255),
    status ENUM('Menunggu', 'Disetujui', 'Ditolak') DEFAULT 'Menunggu',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);";
            echo "</textarea>";
        }
    } else {
        echo "❌ Database connection: <strong style='color: red;'>FAILED</strong><br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: <strong style='color: red;'>" . $e->getMessage() . "</strong><br>";
}

// Test 2: Folder Uploads
echo "<h2>2. Test Upload Folder</h2>";
$upload_dir = '../uploads/';
if (is_dir($upload_dir)) {
    echo "✅ Upload folder: <strong style='color: green;'>EXISTS</strong><br>";
    if (is_writable($upload_dir)) {
        echo "✅ Upload folder: <strong style='color: green;'>WRITABLE</strong><br>";
    } else {
        echo "❌ Upload folder: <strong style='color: red;'>NOT WRITABLE</strong><br>";
        echo "<p>Jalankan: <code>chmod 755 uploads/</code></p>";
    }
} else {
    echo "❌ Upload folder: <strong style='color: red;'>NOT EXISTS</strong><br>";
    echo "<p>Jalankan: <code>mkdir -p uploads && chmod 755 uploads</code></p>";
}

// Test 3: API Endpoints
echo "<h2>3. Test API Endpoints</h2>";

$base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]" . dirname($_SERVER['REQUEST_URI']) . "/lembur.php";

echo "<h3>GET Request Test</h3>";
echo "<button onclick=\"testGet()\">Test GET</button>";
echo "<div id='get-result'></div>";

echo "<h3>POST Request Test</h3>";
echo "<button onclick=\"testPost()\">Test POST</button>";
echo "<div id='post-result'></div>";

echo "<script>
function testGet() {
    fetch('$base_url', {
        method: 'GET',
        headers: {
            'x-api-key': 'absensiku_api_key_2023',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('get-result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
    })
    .catch(error => {
        document.getElementById('get-result').innerHTML = '<pre style=\"color: red;\">Error: ' + error + '</pre>';
    });
}

function testPost() {
    const testData = {
        nama_karyawan: 'Test User',
        keterangan: 'Test lembur dari API test',
        tanggal_lembur: new Date().toISOString().split('T')[0],
        jam_mulai: '18:00',
        foto_mulai: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        status: 'Menunggu'
    };
    
    fetch('$base_url', {
        method: 'POST',
        headers: {
            'x-api-key': 'absensiku_api_key_2023',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('post-result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
    })
    .catch(error => {
        document.getElementById('post-result').innerHTML = '<pre style=\"color: red;\">Error: ' + error + '</pre>';
    });
}
</script>";

// Test 4: PHP Configuration
echo "<h2>4. PHP Configuration</h2>";
echo "PHP Version: <strong>" . phpversion() . "</strong><br>";
echo "PDO MySQL: " . (extension_loaded('pdo_mysql') ? "✅ <strong style='color: green;'>ENABLED</strong>" : "❌ <strong style='color: red;'>DISABLED</strong>") . "<br>";
echo "JSON: " . (extension_loaded('json') ? "✅ <strong style='color: green;'>ENABLED</strong>" : "❌ <strong style='color: red;'>DISABLED</strong>") . "<br>";
echo "GD: " . (extension_loaded('gd') ? "✅ <strong style='color: green;'>ENABLED</strong>" : "❌ <strong style='color: red;'>DISABLED</strong>") . "<br>";

// Test 5: Error Log
echo "<h2>5. Error Log</h2>";
$log_file = '../logs/lembur_error.log';
if (file_exists($log_file)) {
    echo "Error log file: <strong style='color: green;'>EXISTS</strong><br>";
    $log_content = file_get_contents($log_file);
    if (strlen($log_content) > 0) {
        echo "<details><summary>Show recent errors</summary><pre>" . htmlspecialchars(substr($log_content, -1000)) . "</pre></details>";
    } else {
        echo "No errors logged yet.<br>";
    }
} else {
    echo "Error log file: <strong style='color: orange;'>NOT EXISTS</strong> (will be created automatically)<br>";
}

echo "<hr>";
echo "<p><strong>Catatan:</strong> Pastikan untuk mengubah konfigurasi database di <code>config/database.php</code> sesuai dengan setup Anda.</p>";
?>
