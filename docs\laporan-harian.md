# Fitur Laporan Harian

## Deskripsi
Fitur laporan harian memungkinkan karyawan untuk membuat, melihat, dan mengelola laporan kegiatan harian mereka. Fitur ini terdiri dari 2 tab utama: Riwayat Laporan dan <PERSON> La<PERSON>an.

## Struktur File

### Pages
- `src/pages/LaporanHarian.tsx` - Halaman utama dengan segment control

### Components
- `src/components/RiwayatLaporan.tsx` - Komponen untuk melihat riwayat laporan
- `src/components/TambahLaporan.tsx` - Komponen untuk menambah laporan baru

## API Integration

### Endpoint
```
https://absensiku.trunois.my.id/api/laporan_harian.php?api_key=absensiku_api_key_2023
```

### Methods Supported
- **GET**: Mengambil data laporan
- **POST**: Menambah laporan baru
- **PUT**: Update laporan (belum diimplementasi di UI)
- **DELETE**: <PERSON>pus laporan

### Request Format

#### GET - <PERSON><PERSON>
```javascript
// Query parameters
?api_key=absensiku_api_key_2023&nama_karyawan=John%20Doe&periode=Januari%202024
```

#### POST - Tambah Laporan
```javascript
{
  "api_key": "absensiku_api_key_2023",
  "nama_karyawan": "John Doe",
  "periode": "Januari 2024",
  "tanggal": "2024-01-15",
  "keterangan": "Melakukan inspeksi rutin...",
  "foto": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
}
```

#### DELETE - Hapus Laporan
```javascript
{
  "api_key": "absensiku_api_key_2023",
  "id": "123"
}
```

## Fitur Utama

### 1. Riwayat Laporan
- **List View**: Menampilkan semua laporan dalam bentuk card
- **Search**: Pencarian berdasarkan keterangan dan periode bulan
- **Filter**: Filter berdasarkan periode bulan
- **Detail View**: Modal untuk melihat detail lengkap laporan
- **Delete**: Hapus laporan yang sudah dibuat
- **Pull to Refresh**: Refresh data dengan gesture pull

### 2. Tambah Laporan
- **Form Input**: Periode bulan, tanggal, dan keterangan
- **Camera Integration**: Ambil foto kegiatan (opsional)
- **Validation**: Validasi form sebelum submit
- **Auto Reset**: Form reset setelah berhasil submit dengan periode bulan saat ini

## UI Components

### Segment Control
```jsx
<IonSegment value={selectedSegment} onIonChange={e => setSelectedSegment(e.detail.value)}>
  <IonSegmentButton value="riwayat">
    <IonLabel>📋 Riwayat</IonLabel>
  </IonSegmentButton>
  <IonSegmentButton value="tambah">
    <IonLabel>➕ Tambah</IonLabel>
  </IonSegmentButton>
</IonSegment>
```

### Laporan Card
```jsx
<IonCard>
  <IonCardHeader>
    <IonCardTitle>{laporan.periode}</IonCardTitle>
    <IonText color="medium">{formatTanggal(laporan.tanggal)}</IonText>
  </IonCardHeader>
  <IonCardContent>
    <p>{laporan.keterangan}</p>
    <div style={{ display: 'flex', gap: '8px' }}>
      <IonButton size="small" onClick={() => handleViewDetail(laporan)}>
        Detail
      </IonButton>
      <IonButton size="small" color="danger" onClick={() => handleDelete(laporan.id)}>
        Hapus
      </IonButton>
    </div>
  </IonCardContent>
</IonCard>
```

### Camera Component
```jsx
<video ref={videoRef} autoPlay playsInline style={{ width: '100%', height: '250px' }} />
<canvas ref={canvasRef} style={{ display: 'none' }} />
<IonButton onClick={takePhoto}>Ambil Foto</IonButton>
```

## Data Structure

### Laporan Harian Object
```typescript
interface LaporanHarian {
  id: string;
  nama_karyawan: string;
  periode: string;
  tanggal: string;
  keterangan: string;
  foto: string | null;
  created_at: string;
  updated_at: string;
}
```

### Form Data
```typescript
interface FormData {
  periode: string;
  tanggal: string;
  keterangan: string;
}
```

## Periode Options (Bulan)
- `Januari 2024`
- `Februari 2024`
- `Maret 2024`
- `April 2024`
- `Mei 2024`
- `Juni 2024`
- `Juli 2024`
- `Agustus 2024`
- `September 2024`
- `Oktober 2024`
- `November 2024`
- `Desember 2024`

*Note: Tahun akan otomatis menyesuaikan dengan tahun saat ini*

## Features Detail

### Search & Filter
- **Search**: Real-time search dalam keterangan dan periode
- **Filter Periode**: Dropdown untuk filter berdasarkan periode
- **Auto Filter**: Filter otomatis saat user mengetik atau memilih

### Camera & File Upload Integration
- **Smart Camera**: Otomatis mencoba kamera belakang, front camera, atau kamera apapun yang tersedia
- **File Upload**: Upload manual dari galeri/penyimpanan device
- **Dual Options**: Tombol kamera dan upload file tersedia bersamaan
- **Error Handling**: Fallback ke upload file jika kamera bermasalah
- **Preview**: Preview foto/file sebelum submit
- **Multiple Actions**: Ganti dengan kamera, upload file baru, atau hapus
- **File Validation**: Validasi tipe file (hanya gambar) dan ukuran (max 5MB)
- **Quality Compression**: Kompresi otomatis untuk foto dari kamera
- **Optional**: Foto bersifat opsional, tidak wajib

### Form Restrictions & Validation (Updated)

#### Pembatasan Form Baru
- **Periode Bulan**: Read-only, otomatis sesuai bulan saat ini (tidak bisa diubah)
- **Tanggal**: Hanya bisa memilih tanggal di bulan ini (tidak bisa pilih bulan lalu/depan)

#### Validasi Input
- **Required Fields**: Periode, tanggal, dan keterangan wajib diisi
- **Minimum Length**: Keterangan minimal 10 karakter
- **Date Range**: Tanggal harus dalam range bulan ini
- **Toast Messages**: Feedback untuk setiap aksi

#### Keuntungan Pembatasan
- ✅ **Data Consistency**: Laporan selalu sesuai dengan bulan periode
- ✅ **Prevent Backdating**: Tidak bisa buat laporan untuk bulan lalu
- ✅ **Prevent Future Dating**: Tidak bisa buat laporan untuk bulan depan
- ✅ **User Guidance**: Helper text yang jelas dan informatif

### Error Handling
- **Network Error**: Handling untuk error koneksi
- **API Error**: Handling untuk error dari server
- **Camera Error**: Handling untuk error akses kamera
- **Form Validation**: Validasi input user

## Navigation

### Menu Access
Laporan Harian dapat diakses dari:
1. **Home Menu**: Menu "Laporan Harian" di halaman home
2. **Direct URL**: `/laporan-harian`

### Route Configuration
```jsx
<PrivateRoute exact path="/laporan-harian">
  <LaporanHarian />
</PrivateRoute>
```

## State Management

### Local State
- `selectedSegment`: Tab yang aktif (riwayat/tambah)
- `laporanList`: Array semua laporan
- `filteredLaporan`: Array laporan yang sudah difilter
- `formData`: Data form untuk tambah laporan
- `foto`: Base64 string foto yang diambil

### User Data
```javascript
const user = JSON.parse(localStorage.getItem('user') || '{}');
// Menggunakan user.nama untuk nama_karyawan
```

## Performance Optimization

### Lazy Loading
- Components di-load sesuai kebutuhan
- Image loading dengan error handling

### Efficient Filtering
- Filter dilakukan di client-side untuk responsivitas
- Debouncing untuk search input

### Memory Management
- Camera stream di-stop setelah foto diambil
- Canvas element di-clear setelah digunakan

## Future Enhancements

### Planned Features
1. **Edit Laporan**: Update laporan yang sudah dibuat
2. **Export PDF**: Export laporan ke format PDF
3. **Bulk Actions**: Hapus multiple laporan sekaligus
4. **Offline Support**: Simpan laporan saat offline
5. **Rich Text Editor**: Editor yang lebih advanced untuk keterangan
6. **Multiple Photos**: Support multiple foto per laporan
7. **Template**: Template keterangan untuk periode tertentu

### Technical Improvements
1. **Infinite Scroll**: Untuk laporan yang banyak
2. **Image Compression**: Kompres foto sebelum upload
3. **Caching**: Cache data laporan untuk performa
4. **Push Notifications**: Reminder untuk buat laporan
5. **Analytics**: Track usage dan performance

## Troubleshooting

### Masalah Kamera Tidak Berfungsi

#### Gejala
- Kamera tidak muncul atau layar hitam
- Error "Camera not supported"
- Video tidak loading
- Pop-up permission tidak muncul

#### Penyebab & Solusi

1. **Permission Denied / Pop-up Tidak Muncul**
   - **Penyebab**: Browser memblokir pop-up permission atau user sudah menolak sebelumnya
   - **Solusi**:
     - Klik tombol "🔓 Minta Izin Kamera" jika muncul
     - Buka pengaturan browser → Site Settings → Camera → Allow
     - Refresh halaman setelah mengubah permission
     - Coba di browser lain (Chrome, Firefox, Safari)

2. **HTTPS Required**
   - **Penyebab**: Camera API memerlukan HTTPS di production
   - **Solusi**:
     - Pastikan URL dimulai dengan `https://`
     - Localhost tetap bisa menggunakan HTTP

3. **Browser Compatibility**
   - **Penyebab**: Browser lama tidak support getUserMedia API
   - **Solusi**:
     - Update browser ke versi terbaru
     - Gunakan Chrome 53+, Firefox 36+, Safari 11+
     - Fallback ke upload file

4. **Camera Hardware Issues**
   - **Penyebab**: Device tidak memiliki kamera atau driver bermasalah
   - **Solusi**:
     - Test kamera di aplikasi lain
     - Restart device
     - Gunakan upload file sebagai alternatif

5. **Multiple Camera Constraints**
   - **Penyebab**: Kamera belakang tidak tersedia
   - **Solusi**:
     - Sistem otomatis fallback ke kamera depan
     - Coba tombol "🔄 Coba Lagi" jika error

#### Debug Steps untuk Kamera

**Step 1: Check Browser Console**
1. Buka Developer Tools (F12)
2. Lihat tab Console
3. Cari error messages:
   ```
   Starting camera...
   Camera permission status: granted/denied/prompt
   Trying camera constraint 1: {...}
   Camera stream obtained: MediaStream
   ```

**Step 2: Test Permission**
1. Klik "Ambil Foto"
2. Jika tidak ada pop-up permission → Browser sudah memblokir
3. Klik "🔓 Minta Izin Kamera" jika tersedia
4. Atau manual: Browser Settings → Site Settings → Camera

**Step 3: Test Different Browsers**
- Chrome: Biasanya paling kompatibel
- Firefox: Good fallback option
- Safari: Untuk iOS devices
- Edge: Untuk Windows

**Step 4: Check URL Protocol**
- Production: Harus HTTPS
- Development: HTTP localhost OK
- IP Address: Perlu HTTPS

#### Enhanced Features

**Progressive Camera Fallback:**
1. Back camera high quality (1280x720)
2. Back camera lower quality (640x480)
3. Front camera (640x480)
4. Any camera with basic constraints
5. Any camera (last resort)

**Smart Error Handling:**
- Permission-specific messages
- Hardware-specific solutions
- Browser-specific recommendations
- Automatic retry mechanisms

#### Fallback Options
- ✅ **Upload File**: Selalu tersedia sebagai alternatif
- ✅ **Multiple Camera**: Coba back camera → front camera → any camera
- ✅ **Error Messages**: Pesan error yang informatif
- ✅ **Graceful Degradation**: App tetap berfungsi tanpa kamera
- ✅ **Permission Request**: Tombol eksplisit untuk minta izin
- ✅ **Retry Mechanism**: Tombol coba lagi untuk setiap error

### File Upload Issues

#### Validasi File
- **Tipe File**: Hanya gambar (jpg, png, gif, etc.)
- **Ukuran Max**: 5MB per file
- **Format Support**: Semua format gambar yang didukung browser

#### Error Handling
- File terlalu besar → Pesan error + saran kompres
- Tipe file salah → Pesan error + info tipe yang didukung
- Gagal baca file → Pesan error + saran coba lagi

### API Error Troubleshooting

#### Error 500 (Internal Server Error)

**Gejala:**
```
POST https://absensiku.trunois.my.id/api/laporan_harian.php 500 (Internal Server Error)
SyntaxError: Failed to execute 'json' on 'Response': Unexpected end of JSON input
```

**Penyebab & Solusi:**

1. **Server Database Error**
   - **Penyebab**: Database connection issue atau SQL error
   - **Solusi**: Cek database server dan table structure

2. **PHP Error di Backend**
   - **Penyebab**: PHP syntax error atau missing dependencies
   - **Solusi**: Cek PHP error logs di server

3. **Large Image Upload**
   - **Penyebab**: Foto terlalu besar melebihi PHP upload limit
   - **Solusi**: Kompres foto atau tingkatkan PHP limits

4. **Invalid Data Format**
   - **Penyebab**: Data yang dikirim tidak sesuai format yang diharapkan
   - **Solusi**: Validasi data sebelum kirim

**Debug Steps:**
1. **Test API Connection**: Gunakan tombol "🔧 Test Koneksi API"
2. **Check Console**: Lihat console browser untuk detail error
3. **Try Without Photo**: Coba kirim laporan tanpa foto
4. **Check Network**: Pastikan koneksi internet stabil

#### Enhanced Error Handling

**Improved Features:**
- ✅ **Detailed Logging**: Console logs untuk debugging
- ✅ **Response Validation**: Cek response status dan format
- ✅ **Graceful Fallback**: Kirim tanpa foto jika ada masalah
- ✅ **User-Friendly Messages**: Error messages yang informatif
- ✅ **API Test Function**: Test koneksi API secara terpisah

**Error Recovery:**
- Auto-retry mechanism (planned)
- Offline storage untuk retry nanti (planned)
- Data validation sebelum kirim

### Performance Tips
1. **Kompres Foto**: Foto dari kamera otomatis dikompres (quality 0.8)
2. **File Size**: Batasi ukuran file untuk performa upload
3. **Error Recovery**: Selalu ada fallback option
4. **API Testing**: Gunakan tombol test untuk debug koneksi
