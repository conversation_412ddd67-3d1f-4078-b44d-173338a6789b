# Sistem Peringatan GPS untuk Absensi

## Deskripsi
Sistem peringatan GPS membantu mencegah karyawan lupa mengaktifkan lokasi/GPS sebelum melakukan absensi. Sistem ini mendeteksi status GPS secara real-time dan memberikan peringatan visual serta pop-up jika GPS tidak aktif.

## Fitur Utama

### 1. Deteksi Status GPS Real-time
- **Auto Check**: Pengecekan otomatis saat halaman dimuat
- **Periodic Check**: Pengecekan berkala setiap 30 detik
- **Visibility Check**: Pengecekan saat user kembali ke aplikasi
- **Permission Check**: Validasi izin akses lokasi

### 2. Visual Indicators
- **Status Bar**: Indikator status GPS di halaman home
- **Color Coding**: 
  - 🟢 Hijau: GPS aktif dan berfungsi
  - 🔴 Merah: GPS tidak aktif atau tidak dapat diakses
  - 🟡 Kuning: Sedang mengecek status GPS

### 3. Interactive Elements
- **Clickable Status**: Tap pada status GPS untuk membuka pengaturan
- **Smart Button**: Floating button berubah warna dan fungsi berdasarkan status GPS
- **Pop-up Alert**: Peringatan dengan opsi untuk mengaktifkan GPS

## Implementasi Teknis

### State Management
```typescript
const [gpsStatus, setGpsStatus] = useState<'checking' | 'enabled' | 'disabled'>('checking');
const [showGpsAlert, setShowGpsAlert] = useState(false);
```

### Fungsi Deteksi GPS
```typescript
const checkGpsStatus = async () => {
  try {
    setGpsStatus('checking');
    
    // Cek permission GPS
    const permissions = await Geolocation.checkPermissions();
    if (permissions.location === 'denied') {
      setGpsStatus('disabled');
      setShowGpsAlert(true);
      return;
    }
    
    // Test GPS dengan timeout
    const position = await Geolocation.getCurrentPosition({
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 60000
    });
    
    setGpsStatus('enabled');
  } catch (error) {
    // Analisis error untuk menentukan status
    setGpsStatus('disabled');
    setShowGpsAlert(true);
  }
};
```

### Error Handling
- **PERMISSION_DENIED**: Izin lokasi ditolak
- **POSITION_UNAVAILABLE**: GPS tidak tersedia/tidak aktif
- **TIMEOUT**: GPS tidak merespons dalam waktu yang ditentukan
- **Network Error**: Masalah jaringan (dianggap GPS aktif)

## UI Components

### Status Indicator
```jsx
<div style={{
  background: gpsStatus === 'enabled' ? '#e8f5e8' : '#ffe0e0',
  color: gpsStatus === 'enabled' ? '#2e7d32' : '#d32f2f',
  padding: '8px 12px',
  borderRadius: '8px',
  textAlign: 'center',
  cursor: gpsStatus === 'disabled' ? 'pointer' : 'default'
}}>
  {gpsStatus === 'enabled' ? '🟢 GPS Aktif' : '🔴 GPS Tidak Aktif'}
</div>
```

### Smart Floating Button
```jsx
<button
  onClick={() => {
    if (gpsStatus === 'disabled') {
      setShowGpsAlert(true);
    } else {
      history.push('/absensi');
    }
  }}
  style={{
    background: gpsStatus === 'disabled' ? 
      'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)' : 
      'linear-gradient(135deg, #1880ff 0%, #005be7 100%)'
  }}
>
  <IonIcon icon={gpsStatus === 'disabled' ? warningOutline : cameraOutline} />
</button>
```

### Alert Dialog
```jsx
<IonAlert
  isOpen={showGpsAlert}
  header="🔴 GPS Tidak Aktif"
  subHeader="Diperlukan untuk Absensi"
  message="GPS/Lokasi tidak aktif. Silakan aktifkan untuk melakukan absensi."
  buttons={[
    {
      text: 'Coba Lagi',
      handler: () => checkGpsStatus()
    },
    {
      text: 'Buka Pengaturan',
      handler: () => openGpsSettings()
    }
  ]}
/>
```

## Timing dan Intervals

### Pengecekan Berkala
- **Initial Check**: Saat halaman dimuat
- **Periodic Check**: Setiap 30 detik
- **Visibility Check**: Saat app menjadi visible
- **Timeout**: 10 detik untuk setiap pengecekan GPS

### Cache dan Performance
- **Maximum Age**: 60 detik untuk cache posisi
- **High Accuracy**: Enabled untuk hasil yang akurat
- **Debouncing**: Mencegah multiple check bersamaan

## User Experience

### Skenario Normal (GPS Aktif)
1. User membuka aplikasi
2. Status GPS dicek → "🟢 GPS Aktif"
3. Floating button berwarna biru normal
4. User dapat melakukan absensi tanpa hambatan

### Skenario GPS Tidak Aktif
1. User membuka aplikasi
2. Status GPS dicek → "🔴 GPS Tidak Aktif"
3. Floating button berubah warna orange dengan icon warning
4. User tap floating button → Pop-up peringatan muncul
5. User dapat memilih "Buka Pengaturan" atau "Coba Lagi"

### Skenario Permission Denied
1. User menolak izin lokasi
2. Status langsung "🔴 GPS Tidak Aktif"
3. Pop-up muncul dengan instruksi mengaktifkan permission
4. User diarahkan ke pengaturan aplikasi

## Konfigurasi

### Timeout Settings
```typescript
const GPS_CHECK_TIMEOUT = 10000; // 10 detik
const GPS_CHECK_INTERVAL = 30000; // 30 detik
const GPS_CACHE_MAX_AGE = 60000; // 60 detik
```

### Error Codes
```typescript
const GPS_ERROR_CODES = {
  PERMISSION_DENIED: 1,
  POSITION_UNAVAILABLE: 2,
  TIMEOUT: 3
};
```

## Platform Compatibility

### Android
- Menggunakan Capacitor Geolocation API
- Support untuk membuka pengaturan lokasi
- Deteksi GPS hardware dan software

### iOS
- Menggunakan Core Location framework
- Support untuk location permission
- Deteksi location services status

### Web/PWA
- Menggunakan Web Geolocation API
- Fallback untuk browser yang tidak support
- Limited settings access

## Troubleshooting

### Masalah: GPS Mati tapi Status Masih "Aktif"
**Gejala**: GPS dimatikan di pengaturan tapi aplikasi masih menampilkan "GPS Aktif"
**Penyebab**:
- Cache position dari `maximumAge` parameter
- Browser/WebView cache location data
- Permission masih granted tapi service disabled

**Solusi**:
1. **Gunakan `maximumAge: 0`** untuk disable cache:
   ```typescript
   const position = await Geolocation.getCurrentPosition({
     enableHighAccuracy: true,
     timeout: 8000,
     maximumAge: 0 // PENTING: Tidak menggunakan cache
   });
   ```

2. **Implementasi Multiple Checks**:
   ```typescript
   // Check dengan low accuracy dulu (cepat)
   const quickCheck = await Geolocation.getCurrentPosition({
     enableHighAccuracy: false,
     timeout: 3000,
     maximumAge: 0
   });

   // Lalu check dengan high accuracy
   const accurateCheck = await Geolocation.getCurrentPosition({
     enableHighAccuracy: true,
     timeout: 5000,
     maximumAge: 0
   });
   ```

3. **Reduce Check Interval** untuk deteksi real-time:
   ```typescript
   setInterval(() => checkGpsStatus(), 15000); // 15 detik
   ```

### Masalah: Pop-up Tidak Muncul
**Gejala**: GPS mati tapi tidak ada peringatan pop-up
**Solusi**:
1. Cek state management: `setShowGpsAlert(true)`
2. Pastikan error handling menangkap semua kasus
3. Debug dengan console.log untuk trace error
4. Test dengan tombol "Debug Info"

### GPS Tidak Terdeteksi
**Gejala**: Status selalu "checking" atau "disabled"
**Solusi**:
1. Cek permission aplikasi di pengaturan device
2. Pastikan GPS/Location services aktif di sistem
3. Restart aplikasi
4. Clear cache aplikasi
5. Test di lokasi outdoor dengan sinyal GPS baik

### False Positive (GPS Aktif tapi Error)
**Gejala**: GPS aktif tapi status "disabled"
**Solusi**:
1. Increase timeout value ke 10-15 detik
2. Cek network connectivity
3. Test di lokasi dengan sinyal GPS yang baik
4. Disable high accuracy untuk test

### Performance Issues
**Gejala**: Aplikasi lambat karena GPS check
**Solusi**:
1. Increase check interval ke 30-60 detik
2. Reduce timeout value ke 5 detik
3. Implement debouncing
4. Use low accuracy untuk quick checks

### Debug Tools
**Tombol Debug yang Tersedia**:
1. **"🔄 Test GPS"**: Manual trigger GPS check
2. **"🐛 Debug Info"**: Tampilkan permission dan position info
3. **Console Logs**: Cek browser console untuk detail error

## Customization

### Mengubah Interval Check
```typescript
// Di useEffect GPS check
const gpsCheckInterval = setInterval(() => {
  checkGpsStatus();
}, 60000); // Ubah ke 60 detik
```

### Mengubah Timeout
```typescript
const position = await Geolocation.getCurrentPosition({
  enableHighAccuracy: true,
  timeout: 15000, // Ubah ke 15 detik
  maximumAge: 60000
});
```

### Mengubah Warna Status
```typescript
const getGpsStatusColor = (status) => {
  switch (status) {
    case 'enabled': return '#4caf50'; // Hijau
    case 'disabled': return '#f44336'; // Merah
    case 'checking': return '#ff9800'; // Orange
  }
};
```

## Future Enhancements

### Smart Detection
- Deteksi indoor/outdoor untuk akurasi GPS
- Machine learning untuk prediksi GPS issues
- Integration dengan network location

### Advanced Notifications
- Push notification untuk reminder GPS
- Background GPS monitoring
- Scheduled GPS health check

### Analytics
- Track GPS failure rates
- Monitor user behavior patterns
- Performance metrics collection
