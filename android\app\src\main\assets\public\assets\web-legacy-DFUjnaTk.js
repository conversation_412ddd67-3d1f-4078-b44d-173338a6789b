System.register(["./index-legacy-Coshiszm.js"],function(t,e){"use strict";var r,n,o,i,a,s,u,c,f,h,l,d,p,g,y,w,v,_,m;return{setters:[t=>{r=t.I,n=t.n,o=t.L,i=t.D,a=t.A,s=t.H,u=t.B,c=t.N,f=t.C,h=t.F,l=t.o,d=t.M,p=t.q,g=t.t,y=t.P,w=t.Q,v=t.u,_=t.W,m=t.v}],execute:function(){const e="back";var C,A=(C=function(t,e){return C=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},C(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}C(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),E=function(t){function e(r){var n=t.call(this,r.width,r.height)||this;return n.canvas=r,n.tempCanvasElement=null,n.buffer=e.makeBufferFromCanvasImageData(r),n}return A(e,t),e.makeBufferFromCanvasImageData=function(t){var r;try{r=t.getContext("2d",{willReadFrequently:!0})}catch(o){r=t.getContext("2d")}if(!r)throw new Error("Couldn't get canvas context.");var n=r.getImageData(0,0,t.width,t.height);return e.toGrayscaleBuffer(n.data,t.width,t.height)},e.toGrayscaleBuffer=function(t,e,r){for(var n=new Uint8ClampedArray(e*r),o=0,i=0,a=t.length;o<a;o+=4,i++){var s=void 0;s=0===t[o+3]?255:306*t[o]+601*t[o+1]+117*t[o+2]+512>>10,n[i]=s}return n},e.prototype.getRow=function(t,e){if(t<0||t>=this.getHeight())throw new r("Requested row is outside the image: "+t);var n=this.getWidth(),o=t*n;return null===e?e=this.buffer.slice(o,o+n):(e.length<n&&(e=new Uint8ClampedArray(n)),e.set(this.buffer.slice(o,o+n))),e},e.prototype.getMatrix=function(){return this.buffer},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(e,r,n,o){return t.prototype.crop.call(this,e,r,n,o),this},e.prototype.isRotateSupported=function(){return!0},e.prototype.rotateCounterClockwise=function(){return this.rotate(-90),this},e.prototype.rotateCounterClockwise45=function(){return this.rotate(-45),this},e.prototype.invert=function(){return new n(this)},e.prototype.getTempCanvasElement=function(){if(null===this.tempCanvasElement){var t=this.canvas.ownerDocument.createElement("canvas");t.width=this.canvas.width,t.height=this.canvas.height,this.tempCanvasElement=t}return this.tempCanvasElement},e.prototype.rotate=function(t){var r=this.getTempCanvasElement();if(!r)throw new Error("Could not create a Canvas element.");var n=t*e.DEGREE_TO_RADIANS,o=this.canvas.width,i=this.canvas.height,a=Math.ceil(Math.abs(Math.cos(n))*o+Math.abs(Math.sin(n))*i),s=Math.ceil(Math.abs(Math.sin(n))*o+Math.abs(Math.cos(n))*i);r.width=a,r.height=s;var u=r.getContext("2d");if(!u)throw new Error("Could not create a Canvas Context element.");return u.translate(a/2,s/2),u.rotate(n),u.drawImage(this.canvas,o/-2,i/-2),this.buffer=e.makeBufferFromCanvasImageData(r),this},e.DEGREE_TO_RADIANS=Math.PI/180,e}(o);function I(){return"undefined"!=typeof navigator}function S(){return!!(I()&&navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices)}var b=function(){return b=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},b.apply(this,arguments)},T=function(t,e,r,n){return new(r||(r=Promise))(function(o,i){function a(t){try{u(n.next(t))}catch(e){i(e)}}function s(t){try{u(n.throw(t))}catch(e){i(e)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof r?e:new r(function(t){t(e)})).then(a,s)}u((n=n.apply(t,e||[])).next())})},O=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(s){i=[6,s],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},R=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},N={delayBetweenScanAttempts:500,delayBetweenScanSuccess:500,tryPlayVideoTimeout:5e3},D=function(){function t(t,e,r){void 0===e&&(e=new Map),void 0===r&&(r={}),this.reader=t,this.hints=e,this.options=b(b({},N),r)}return Object.defineProperty(t.prototype,"possibleFormats",{set:function(t){this.hints.set(i.POSSIBLE_FORMATS,t)},enumerable:!1,configurable:!0}),t.addVideoSource=function(t,e){try{t.srcObject=e}catch(r){console.error("got interrupted by new loading request")}},t.mediaStreamSetTorch=function(t,e){return T(this,void 0,void 0,function(){return O(this,function(r){switch(r.label){case 0:return[4,t.applyConstraints({advanced:[{fillLightMode:e?"flash":"off",torch:!!e}]})];case 1:return r.sent(),[2]}})})},t.mediaStreamIsTorchCompatible=function(e){var r,n,o=e.getVideoTracks();try{for(var i=R(o),a=i.next();!a.done;a=i.next()){var s=a.value;if(t.mediaStreamIsTorchCompatibleTrack(s))return!0}}catch(u){r={error:u}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}return!1},t.mediaStreamIsTorchCompatibleTrack=function(t){try{return"torch"in t.getCapabilities()}catch(e){return console.error(e),console.warn("Your browser may be not fully compatible with WebRTC and/or ImageCapture specs. Torch will not be available."),!1}},t.isVideoPlaying=function(t){return t.currentTime>0&&!t.paused&&t.readyState>2},t.getMediaElement=function(t,e){var r=document.getElementById(t);if(!r)throw new a("element with id '".concat(t,"' not found"));if(r.nodeName.toLowerCase()!==e.toLowerCase())throw new a("element with id '".concat(t,"' must be an ").concat(e," element"));return r},t.createVideoElement=function(e){if(e instanceof HTMLVideoElement)return e;if("string"==typeof e)return t.getMediaElement(e,"video");if(!e&&"undefined"!=typeof document){var r=document.createElement("video");return r.width=200,r.height=200,r}throw new Error("Couldn't get videoElement from videoSource!")},t.prepareImageElement=function(e){if(e instanceof HTMLImageElement)return e;if("string"==typeof e)return t.getMediaElement(e,"img");if(void 0===e){var r=document.createElement("img");return r.width=200,r.height=200,r}throw new Error("Couldn't get imageElement from imageSource!")},t.prepareVideoElement=function(e){var r=t.createVideoElement(e);return r.setAttribute("autoplay","true"),r.setAttribute("muted","true"),r.setAttribute("playsinline","true"),r},t.isImageLoaded=function(t){return!!t.complete&&0!==t.naturalWidth},t.createBinaryBitmapFromCanvas=function(t){var e=new E(t),r=new s(e);return new u(r)},t.drawImageOnCanvas=function(t,e){t.drawImage(e,0,0)},t.getMediaElementDimensions=function(t){if(t instanceof HTMLVideoElement)return{height:t.videoHeight,width:t.videoWidth};if(t instanceof HTMLImageElement)return{height:t.naturalHeight||t.height,width:t.naturalWidth||t.width};throw new Error("Couldn't find the Source's dimensions!")},t.createCaptureCanvas=function(e){if(!e)throw new a("Cannot create a capture canvas without a media element.");if("undefined"==typeof document)throw new Error('The page "Document" is undefined, make sure you\'re running in a browser.');var r=document.createElement("canvas"),n=t.getMediaElementDimensions(e),o=n.width,i=n.height;return r.style.width=o+"px",r.style.height=i+"px",r.width=o,r.height=i,r},t.tryPlayVideo=function(e){return T(this,void 0,void 0,function(){var r;return O(this,function(n){switch(n.label){case 0:if(null==e?void 0:e.ended)return console.error("Trying to play video that has ended."),[2,!1];if(t.isVideoPlaying(e))return console.warn("Trying to play video that is already playing."),[2,!0];n.label=1;case 1:return n.trys.push([1,3,,4]),[4,e.play()];case 2:return n.sent(),[2,!0];case 3:return r=n.sent(),console.warn("It was not possible to play the video.",r),[2,!1];case 4:return[2]}})})},t.createCanvasFromMediaElement=function(e){var r=t.createCaptureCanvas(e),n=r.getContext("2d");if(!n)throw new Error("Couldn't find Canvas 2D Context.");return t.drawImageOnCanvas(n,e),r},t.createBinaryBitmapFromMediaElem=function(e){var r=t.createCanvasFromMediaElement(e);return t.createBinaryBitmapFromCanvas(r)},t.destroyImageElement=function(t){t.src="",t.removeAttribute("src"),t=void 0},t.listVideoInputDevices=function(){return T(this,void 0,void 0,function(){var t,e,r,n,o,i,a,s,u,c,f,h;return O(this,function(l){switch(l.label){case 0:if(!I())throw new Error("Can't enumerate devices, navigator is not present.");if(!S())throw new Error("Can't enumerate devices, method not supported.");return[4,navigator.mediaDevices.enumerateDevices()];case 1:t=l.sent(),e=[];try{for(r=R(t),n=r.next();!n.done;n=r.next())o=n.value,"videoinput"===(i="video"===o.kind?"videoinput":o.kind)&&(a=o.deviceId||o.id,s=o.label||"Video device ".concat(e.length+1),u=o.groupId,c={deviceId:a,label:s,kind:i,groupId:u},e.push(c))}catch(d){f={error:d}}finally{try{n&&!n.done&&(h=r.return)&&h.call(r)}finally{if(f)throw f.error}}return[2,e]}})})},t.findDeviceById=function(e){return T(this,void 0,void 0,function(){var r;return O(this,function(n){switch(n.label){case 0:return[4,t.listVideoInputDevices()];case 1:return(r=n.sent())?[2,r.find(function(t){return t.deviceId===e})]:[2]}})})},t.cleanVideoSource=function(t){if(t){try{t.srcObject=null}catch(e){t.src=""}t&&t.removeAttribute("src")}},t.releaseAllStreams=function(){0!==t.streamTracker.length&&t.streamTracker.forEach(function(t){t.getTracks().forEach(function(t){return t.stop()})}),t.streamTracker=[]},t.playVideoOnLoadAsync=function(e,r){return T(this,void 0,void 0,function(){return O(this,function(n){switch(n.label){case 0:return[4,t.tryPlayVideo(e)];case 1:return n.sent()?[2,!0]:[2,new Promise(function(n,o){var i=setTimeout(function(){t.isVideoPlaying(e)||(o(!1),e.removeEventListener("canplay",a))},r),a=function(){t.tryPlayVideo(e).then(function(t){clearTimeout(i),e.removeEventListener("canplay",a),n(t)})};e.addEventListener("canplay",a)})]}})})},t.attachStreamToVideo=function(e,r,n){return void 0===n&&(n=5e3),T(this,void 0,void 0,function(){var o;return O(this,function(i){switch(i.label){case 0:return o=t.prepareVideoElement(r),t.addVideoSource(o,e),[4,t.playVideoOnLoadAsync(o,n)];case 1:return i.sent(),[2,o]}})})},t._waitImageLoad=function(e){return new Promise(function(r,n){var o=setTimeout(function(){t.isImageLoaded(e)||(e.removeEventListener("load",i),n())},1e4),i=function(){clearTimeout(o),e.removeEventListener("load",i),r()};e.addEventListener("load",i)})},t.checkCallbackFnOrThrow=function(t){if(!t)throw new a("`callbackFn` is a required parameter, you cannot capture results without it.")},t.disposeMediaStream=function(t){t.getVideoTracks().forEach(function(t){return t.stop()}),t=void 0},t.prototype.decode=function(e){var r=t.createCanvasFromMediaElement(e);return this.decodeFromCanvas(r)},t.prototype.decodeBitmap=function(t){return this.reader.decode(t,this.hints)},t.prototype.decodeFromCanvas=function(e){var r=t.createBinaryBitmapFromCanvas(e);return this.decodeBitmap(r)},t.prototype.decodeFromImageElement=function(e){return T(this,void 0,void 0,function(){var r;return O(this,function(n){switch(n.label){case 0:if(!e)throw new a("An image element must be provided.");return r=t.prepareImageElement(e),[4,this._decodeOnLoadImage(r)];case 1:return[2,n.sent()]}})})},t.prototype.decodeFromImageUrl=function(e){return T(this,void 0,void 0,function(){var r;return O(this,function(n){switch(n.label){case 0:if(!e)throw new a("An URL must be provided.");(r=t.prepareImageElement()).src=e,n.label=1;case 1:return n.trys.push([1,,3,4]),[4,this.decodeFromImageElement(r)];case 2:return[2,n.sent()];case 3:return t.destroyImageElement(r),[7];case 4:return[2]}})})},t.prototype.decodeFromConstraints=function(e,r,n){return T(this,void 0,void 0,function(){var o,i;return O(this,function(a){switch(a.label){case 0:return t.checkCallbackFnOrThrow(n),[4,this.getUserMedia(e)];case 1:o=a.sent(),a.label=2;case 2:return a.trys.push([2,4,,5]),[4,this.decodeFromStream(o,r,n)];case 3:return[2,a.sent()];case 4:throw i=a.sent(),t.disposeMediaStream(o),i;case 5:return[2]}})})},t.prototype.decodeFromStream=function(e,r,n){return T(this,void 0,void 0,function(){var o,i,a,s,u,c,f,h,l=this;return O(this,function(d){switch(d.label){case 0:return t.checkCallbackFnOrThrow(n),o=this.options.tryPlayVideoTimeout,[4,t.attachStreamToVideo(e,r,o)];case 1:return i=d.sent(),a=function(){t.disposeMediaStream(e),t.cleanVideoSource(i)},s=this.scan(i,n,a),u=e.getVideoTracks(),c=b(b({},s),{stop:function(){s.stop()},streamVideoConstraintsApply:function(t,e){return T(this,void 0,void 0,function(){var r,n,o,i,a,s;return O(this,function(c){switch(c.label){case 0:r=e?u.filter(e):u,c.label=1;case 1:c.trys.push([1,6,7,8]),n=R(r),o=n.next(),c.label=2;case 2:return o.done?[3,5]:[4,o.value.applyConstraints(t)];case 3:c.sent(),c.label=4;case 4:return o=n.next(),[3,2];case 5:return[3,8];case 6:return i=c.sent(),a={error:i},[3,8];case 7:try{o&&!o.done&&(s=n.return)&&s.call(n)}finally{if(a)throw a.error}return[7];case 8:return[2]}})})},streamVideoConstraintsGet:function(t){return u.find(t).getConstraints()},streamVideoSettingsGet:function(t){return u.find(t).getSettings()},streamVideoCapabilitiesGet:function(t){return u.find(t).getCapabilities()}}),t.mediaStreamIsTorchCompatible(e)&&(f=null==u?void 0:u.find(function(e){return t.mediaStreamIsTorchCompatibleTrack(e)}),h=function(e){return T(l,void 0,void 0,function(){return O(this,function(r){switch(r.label){case 0:return[4,t.mediaStreamSetTorch(f,e)];case 1:return r.sent(),[2]}})})},c.switchTorch=h,c.stop=function(){return T(l,void 0,void 0,function(){return O(this,function(t){switch(t.label){case 0:return s.stop(),[4,h(!1)];case 1:return t.sent(),[2]}})})}),[2,c]}})})},t.prototype.decodeFromVideoDevice=function(e,r,n){return T(this,void 0,void 0,function(){var o;return O(this,function(i){switch(i.label){case 0:return t.checkCallbackFnOrThrow(n),o={video:e?{deviceId:{exact:e}}:{facingMode:"environment"}},[4,this.decodeFromConstraints(o,r,n)];case 1:return[2,i.sent()]}})})},t.prototype.decodeFromVideoElement=function(e,r){return T(this,void 0,void 0,function(){var n,o;return O(this,function(i){switch(i.label){case 0:if(t.checkCallbackFnOrThrow(r),!e)throw new a("A video element must be provided.");return n=t.prepareVideoElement(e),o=this.options.tryPlayVideoTimeout,[4,t.playVideoOnLoadAsync(n,o)];case 1:return i.sent(),[2,this.scan(n,r)]}})})},t.prototype.decodeFromVideoUrl=function(e,r){return T(this,void 0,void 0,function(){var n,o,i;return O(this,function(s){switch(s.label){case 0:if(t.checkCallbackFnOrThrow(r),!e)throw new a("An URL must be provided.");return(n=t.prepareVideoElement()).src=e,o=function(){t.cleanVideoSource(n)},i=this.options.tryPlayVideoTimeout,[4,t.playVideoOnLoadAsync(n,i)];case 1:return s.sent(),[2,this.scan(n,r,o)]}})})},t.prototype.decodeOnceFromConstraints=function(t,e){return T(this,void 0,void 0,function(){var r;return O(this,function(n){switch(n.label){case 0:return[4,this.getUserMedia(t)];case 1:return r=n.sent(),[4,this.decodeOnceFromStream(r,e)];case 2:return[2,n.sent()]}})})},t.prototype.decodeOnceFromStream=function(e,r){return T(this,void 0,void 0,function(){var n,o;return O(this,function(i){switch(i.label){case 0:return n=Boolean(r),[4,t.attachStreamToVideo(e,r)];case 1:o=i.sent(),i.label=2;case 2:return i.trys.push([2,,4,5]),[4,this.scanOneResult(o)];case 3:return[2,i.sent()];case 4:return n||t.cleanVideoSource(o),[7];case 5:return[2]}})})},t.prototype.decodeOnceFromVideoDevice=function(t,e){return T(this,void 0,void 0,function(){var r;return O(this,function(n){switch(n.label){case 0:return r={video:t?{deviceId:{exact:t}}:{facingMode:"environment"}},[4,this.decodeOnceFromConstraints(r,e)];case 1:return[2,n.sent()]}})})},t.prototype.decodeOnceFromVideoElement=function(e){return T(this,void 0,void 0,function(){var r,n;return O(this,function(o){switch(o.label){case 0:if(!e)throw new a("A video element must be provided.");return r=t.prepareVideoElement(e),n=this.options.tryPlayVideoTimeout,[4,t.playVideoOnLoadAsync(r,n)];case 1:return o.sent(),[4,this.scanOneResult(r)];case 2:return[2,o.sent()]}})})},t.prototype.decodeOnceFromVideoUrl=function(e){return T(this,void 0,void 0,function(){var r,n;return O(this,function(o){switch(o.label){case 0:if(!e)throw new a("An URL must be provided.");(r=t.prepareVideoElement()).src=e,n=this.decodeOnceFromVideoElement(r),o.label=1;case 1:return o.trys.push([1,,3,4]),[4,n];case 2:return[2,o.sent()];case 3:return t.cleanVideoSource(r),[7];case 4:return[2]}})})},t.prototype.scanOneResult=function(t,e,r,n){var o=this;return void 0===e&&(e=!0),void 0===r&&(r=!0),void 0===n&&(n=!0),new Promise(function(i,a){o.scan(t,function(t,o,s){if(t)return i(t),void s.stop();if(o){if(o instanceof c&&e)return;if(o instanceof f&&r)return;if(o instanceof h&&n)return;s.stop(),a(o)}})})},t.prototype.scan=function(e,r,n){var o=this;t.checkCallbackFnOrThrow(r);var i,a=t.createCaptureCanvas(e);try{i=a.getContext("2d",{willReadFrequently:!0})}catch(g){i=a.getContext("2d")}if(!i)throw new Error("Couldn't create canvas for visual element scan.");var s,u=function(){i=void 0,a=void 0},l=!1,d={stop:function(){l=!0,clearTimeout(s),u(),n&&n()}},p=function(){if(!l)try{t.drawImageOnCanvas(i,e);var g=o.decodeFromCanvas(a);r(g,void 0,d),s=setTimeout(p,o.options.delayBetweenScanSuccess)}catch(y){if(r(void 0,y,d),y instanceof f||y instanceof h||y instanceof c)return void(s=setTimeout(p,o.options.delayBetweenScanAttempts));u(),n&&n(y)}};return p(),d},t.prototype._decodeOnLoadImage=function(e){return T(this,void 0,void 0,function(){return O(this,function(r){switch(r.label){case 0:return t.isImageLoaded(e)?[3,2]:[4,t._waitImageLoad(e)];case 1:r.sent(),r.label=2;case 2:return[2,this.decode(e)]}})})},t.prototype.getUserMedia=function(e){return T(this,void 0,void 0,function(){var r;return O(this,function(n){switch(n.label){case 0:return[4,navigator.mediaDevices.getUserMedia(e)];case 1:return r=n.sent(),t.streamTracker.push(r),[2,r]}})})},t.streamTracker=[],t}(),M=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();!function(t){function e(e,r){return t.call(this,new l,e,r)||this}M(e,t)}(D);var P=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();!function(t){function e(e,r){return t.call(this,new d(e),e,r)||this}P(e,t)}(D);var B=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();!function(t){function e(e,r){return t.call(this,new p,e,r)||this}B(e,t)}(D);var L=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();!function(t){function e(e,r){var n=this,o=new g;return o.setHints(e),(n=t.call(this,o,e,r)||this).reader=o,n}L(e,t),Object.defineProperty(e.prototype,"possibleFormats",{set:function(t){this.hints.set(i.POSSIBLE_FORMATS,t),this.reader.setHints(this.hints)},enumerable:!1,configurable:!0}),e.prototype.decodeBitmap=function(t){return this.reader.decodeWithState(t)},e.prototype.setHints=function(t){this.hints=t,this.reader.setHints(this.hints)}}(D);var F=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();!function(t){function e(e,r){return t.call(this,new y,e,r)||this}F(e,t)}(D);var k,x=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),V=function(t){function e(e,r){return t.call(this,new w,e,r)||this}return x(e,t),e}(D),U=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),H=function(t){function e(e){void 0===e&&(e=void 0);var r=t.call(this,e)||this;return r.message=e,r}return U(e,t),e.prototype.getKind=function(){return this.constructor.kind},e.kind="Exception",e}(v),G=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),X=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return G(e,t),e.kind="ArgumentException",e}(H),W=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),j=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return W(e,t),e.kind="IllegalArgumentException",e}(H),z=function(){function t(t){if(this.binarizer=t,null===t)throw new j("Binarizer must be non-null.")}return t.prototype.getWidth=function(){return this.binarizer.getWidth()},t.prototype.getHeight=function(){return this.binarizer.getHeight()},t.prototype.getBlackRow=function(t,e){return this.binarizer.getBlackRow(t,e)},t.prototype.getBlackMatrix=function(){return null!==this.matrix&&void 0!==this.matrix||(this.matrix=this.binarizer.getBlackMatrix()),this.matrix},t.prototype.isCropSupported=function(){return this.binarizer.getLuminanceSource().isCropSupported()},t.prototype.crop=function(e,r,n,o){var i=this.binarizer.getLuminanceSource().crop(e,r,n,o);return new t(this.binarizer.createBinarizer(i))},t.prototype.isRotateSupported=function(){return this.binarizer.getLuminanceSource().isRotateSupported()},t.prototype.rotateCounterClockwise=function(){var e=this.binarizer.getLuminanceSource().rotateCounterClockwise();return new t(this.binarizer.createBinarizer(e))},t.prototype.rotateCounterClockwise45=function(){var e=this.binarizer.getLuminanceSource().rotateCounterClockwise45();return new t(this.binarizer.createBinarizer(e))},t.prototype.toString=function(){try{return this.getBlackMatrix().toString()}catch(t){return""}},t}(),Y=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Z=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return Y(e,t),e.getChecksumInstance=function(){return new e},e.kind="ChecksumException",e}(H),K=function(){function t(t){this.source=t}return t.prototype.getLuminanceSource=function(){return this.source},t.prototype.getWidth=function(){return this.source.getWidth()},t.prototype.getHeight=function(){return this.source.getHeight()},t}(),q=function(){function t(){}return t.arraycopy=function(t,e,r,n,o){for(;o--;)r[n++]=t[e++]},t.currentTimeMillis=function(){return Date.now()},t}(),Q=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),J=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return Q(e,t),e.kind="IndexOutOfBoundsException",e}(H),$=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),tt=function(t){function e(e,r){void 0===e&&(e=void 0),void 0===r&&(r=void 0);var n=t.call(this,r)||this;return n.index=e,n.message=r,n}return $(e,t),e.kind="ArrayIndexOutOfBoundsException",e}(J),et=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},rt=function(){function t(){}return t.fill=function(t,e){for(var r=0,n=t.length;r<n;r++)t[r]=e},t.fillWithin=function(e,r,n,o){t.rangeCheck(e.length,r,n);for(var i=r;i<n;i++)e[i]=o},t.rangeCheck=function(t,e,r){if(e>r)throw new j("fromIndex("+e+") > toIndex("+r+")");if(e<0)throw new tt(e);if(r>t)throw new tt(r)},t.asList=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return t},t.create=function(t,e,r){return Array.from({length:t}).map(function(t){return Array.from({length:e}).fill(r)})},t.createInt32Array=function(t,e,r){return Array.from({length:t}).map(function(t){return Int32Array.from({length:e}).fill(r)})},t.equals=function(t,e){if(!t)return!1;if(!e)return!1;if(!t.length)return!1;if(!e.length)return!1;if(t.length!==e.length)return!1;for(var r=0,n=t.length;r<n;r++)if(t[r]!==e[r])return!1;return!0},t.hashCode=function(t){var e,r;if(null===t)return 0;var n=1;try{for(var o=et(t),i=o.next();!i.done;i=o.next())n=31*n+i.value}catch(a){e={error:a}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n},t.fillUint8Array=function(t,e){for(var r=0;r!==t.length;r++)t[r]=e},t.copyOf=function(t,e){return t.slice(0,e)},t.copyOfUint8Array=function(t,e){if(t.length<=e){var r=new Uint8Array(e);return r.set(t),r}return t.slice(0,e)},t.copyOfRange=function(t,e,r){var n=r-e,o=new Int32Array(n);return q.arraycopy(t,e,o,0,n),o},t.binarySearch=function(e,r,n){void 0===n&&(n=t.numberComparator);for(var o=0,i=e.length-1;o<=i;){var a=i+o>>1,s=n(r,e[a]);if(s>0)o=a+1;else{if(!(s<0))return a;i=a-1}}return-o-1},t.numberComparator=function(t,e){return t-e},t}(),nt=function(){function t(){}return t.numberOfTrailingZeros=function(t){var e;if(0===t)return 32;var r=31;return 0!=(e=t<<16)&&(r-=16,t=e),0!=(e=t<<8)&&(r-=8,t=e),0!=(e=t<<4)&&(r-=4,t=e),0!=(e=t<<2)&&(r-=2,t=e),r-(t<<1>>>31)},t.numberOfLeadingZeros=function(t){if(0===t)return 32;var e=1;return t>>>16==0&&(e+=16,t<<=16),t>>>24==0&&(e+=8,t<<=8),t>>>28==0&&(e+=4,t<<=4),t>>>30==0&&(e+=2,t<<=2),e-=t>>>31},t.toHexString=function(t){return t.toString(16)},t.toBinaryString=function(t){return String(parseInt(String(t),2))},t.bitCount=function(t){return t=(t=(858993459&(t-=t>>>1&1431655765))+(t>>>2&858993459))+(t>>>4)&252645135,t+=t>>>8,63&(t+=t>>>16)},t.truncDivision=function(t,e){return Math.trunc(t/e)},t.parseInt=function(t,e){return void 0===e&&(e=void 0),parseInt(t,e)},t.MIN_VALUE_32_BITS=-2147483648,t.MAX_VALUE=Number.MAX_SAFE_INTEGER,t}(),ot=function(){function t(e,r){void 0===e?(this.size=0,this.bits=new Int32Array(1)):(this.size=e,this.bits=null==r?t.makeArray(e):r)}return t.prototype.getSize=function(){return this.size},t.prototype.getSizeInBytes=function(){return Math.floor((this.size+7)/8)},t.prototype.ensureCapacity=function(e){if(e>32*this.bits.length){var r=t.makeArray(e);q.arraycopy(this.bits,0,r,0,this.bits.length),this.bits=r}},t.prototype.get=function(t){return!!(this.bits[Math.floor(t/32)]&1<<(31&t))},t.prototype.set=function(t){this.bits[Math.floor(t/32)]|=1<<(31&t)},t.prototype.flip=function(t){this.bits[Math.floor(t/32)]^=1<<(31&t)},t.prototype.getNextSet=function(t){var e=this.size;if(t>=e)return e;var r=this.bits,n=Math.floor(t/32),o=r[n];o&=~((1<<(31&t))-1);for(var i=r.length;0===o;){if(++n===i)return e;o=r[n]}var a=32*n+nt.numberOfTrailingZeros(o);return a>e?e:a},t.prototype.getNextUnset=function(t){var e=this.size;if(t>=e)return e;var r=this.bits,n=Math.floor(t/32),o=~r[n];o&=~((1<<(31&t))-1);for(var i=r.length;0===o;){if(++n===i)return e;o=~r[n]}var a=32*n+nt.numberOfTrailingZeros(o);return a>e?e:a},t.prototype.setBulk=function(t,e){this.bits[Math.floor(t/32)]=e},t.prototype.setRange=function(t,e){if(e<t||t<0||e>this.size)throw new j;if(e!==t){e--;for(var r=Math.floor(t/32),n=Math.floor(e/32),o=this.bits,i=r;i<=n;i++){var a=(2<<(i<n?31:31&e))-(1<<(i>r?0:31&t));o[i]|=a}}},t.prototype.clear=function(){for(var t=this.bits.length,e=this.bits,r=0;r<t;r++)e[r]=0},t.prototype.isRange=function(t,e,r){if(e<t||t<0||e>this.size)throw new j;if(e===t)return!0;e--;for(var n=Math.floor(t/32),o=Math.floor(e/32),i=this.bits,a=n;a<=o;a++){var s=(2<<(a<o?31:31&e))-(1<<(a>n?0:31&t))&4294967295;if((i[a]&s)!==(r?s:0))return!1}return!0},t.prototype.appendBit=function(t){this.ensureCapacity(this.size+1),t&&(this.bits[Math.floor(this.size/32)]|=1<<(31&this.size)),this.size++},t.prototype.appendBits=function(t,e){if(e<0||e>32)throw new j("Num bits must be between 0 and 32");this.ensureCapacity(this.size+e);for(var r=e;r>0;r--)this.appendBit(1==(t>>r-1&1))},t.prototype.appendBitArray=function(t){var e=t.size;this.ensureCapacity(this.size+e);for(var r=0;r<e;r++)this.appendBit(t.get(r))},t.prototype.xor=function(t){if(this.size!==t.size)throw new j("Sizes don't match");for(var e=this.bits,r=0,n=e.length;r<n;r++)e[r]^=t.bits[r]},t.prototype.toBytes=function(t,e,r,n){for(var o=0;o<n;o++){for(var i=0,a=0;a<8;a++)this.get(t)&&(i|=1<<7-a),t++;e[r+o]=i}},t.prototype.getBitArray=function(){return this.bits},t.prototype.reverse=function(){for(var t=new Int32Array(this.bits.length),e=Math.floor((this.size-1)/32),r=e+1,n=this.bits,o=0;o<r;o++){var i=n[o];i=(i=(i=(i=(i=i>>1&1431655765|(1431655765&i)<<1)>>2&858993459|(858993459&i)<<2)>>4&252645135|(252645135&i)<<4)>>8&16711935|(16711935&i)<<8)>>16&65535|(65535&i)<<16,t[e-o]=i}if(this.size!==32*r){var a=32*r-this.size,s=t[0]>>>a;for(o=1;o<r;o++){var u=t[o];s|=u<<32-a,t[o-1]=s,s=u>>>a}t[r-1]=s}this.bits=t},t.makeArray=function(t){return new Int32Array(Math.floor((t+31)/32))},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;return this.size===r.size&&rt.equals(this.bits,r.bits)},t.prototype.hashCode=function(){return 31*this.size+rt.hashCode(this.bits)},t.prototype.toString=function(){for(var t="",e=0,r=this.size;e<r;e++)7&e||(t+=" "),t+=this.get(e)?"X":".";return t},t.prototype.clone=function(){return new t(this.size,this.bits.slice())},t.prototype.toArray=function(){for(var t=[],e=0,r=this.size;e<r;e++)t.push(this.get(e));return t},t}();!function(t){t[t.OTHER=0]="OTHER",t[t.PURE_BARCODE=1]="PURE_BARCODE",t[t.POSSIBLE_FORMATS=2]="POSSIBLE_FORMATS",t[t.TRY_HARDER=3]="TRY_HARDER",t[t.CHARACTER_SET=4]="CHARACTER_SET",t[t.ALLOWED_LENGTHS=5]="ALLOWED_LENGTHS",t[t.ASSUME_CODE_39_CHECK_DIGIT=6]="ASSUME_CODE_39_CHECK_DIGIT",t[t.ASSUME_GS1=7]="ASSUME_GS1",t[t.RETURN_CODABAR_START_END=8]="RETURN_CODABAR_START_END",t[t.NEED_RESULT_POINT_CALLBACK=9]="NEED_RESULT_POINT_CALLBACK",t[t.ALLOWED_EAN_EXTENSIONS=10]="ALLOWED_EAN_EXTENSIONS"}(k||(k={}));var it,at=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),st=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return at(e,t),e.getFormatInstance=function(){return new e},e.kind="FormatException",e}(H),ut=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};!function(t){t[t.Cp437=0]="Cp437",t[t.ISO8859_1=1]="ISO8859_1",t[t.ISO8859_2=2]="ISO8859_2",t[t.ISO8859_3=3]="ISO8859_3",t[t.ISO8859_4=4]="ISO8859_4",t[t.ISO8859_5=5]="ISO8859_5",t[t.ISO8859_6=6]="ISO8859_6",t[t.ISO8859_7=7]="ISO8859_7",t[t.ISO8859_8=8]="ISO8859_8",t[t.ISO8859_9=9]="ISO8859_9",t[t.ISO8859_10=10]="ISO8859_10",t[t.ISO8859_11=11]="ISO8859_11",t[t.ISO8859_13=12]="ISO8859_13",t[t.ISO8859_14=13]="ISO8859_14",t[t.ISO8859_15=14]="ISO8859_15",t[t.ISO8859_16=15]="ISO8859_16",t[t.SJIS=16]="SJIS",t[t.Cp1250=17]="Cp1250",t[t.Cp1251=18]="Cp1251",t[t.Cp1252=19]="Cp1252",t[t.Cp1256=20]="Cp1256",t[t.UnicodeBigUnmarked=21]="UnicodeBigUnmarked",t[t.UTF8=22]="UTF8",t[t.ASCII=23]="ASCII",t[t.Big5=24]="Big5",t[t.GB18030=25]="GB18030",t[t.EUC_KR=26]="EUC_KR"}(it||(it={}));var ct,ft=function(){function t(e,r,n){for(var o,i,a=[],s=3;s<arguments.length;s++)a[s-3]=arguments[s];this.valueIdentifier=e,this.name=n,this.values="number"==typeof r?Int32Array.from([r]):r,this.otherEncodingNames=a,t.VALUE_IDENTIFIER_TO_ECI.set(e,this),t.NAME_TO_ECI.set(n,this);for(var u=this.values,c=0,f=u.length;c!==f;c++){var h=u[c];t.VALUES_TO_ECI.set(h,this)}try{for(var l=ut(a),d=l.next();!d.done;d=l.next()){var p=d.value;t.NAME_TO_ECI.set(p,this)}}catch(g){o={error:g}}finally{try{d&&!d.done&&(i=l.return)&&i.call(l)}finally{if(o)throw o.error}}}return t.prototype.getValueIdentifier=function(){return this.valueIdentifier},t.prototype.getName=function(){return this.name},t.prototype.getValue=function(){return this.values[0]},t.getCharacterSetECIByValue=function(e){if(e<0||e>=900)throw new st("incorect value");var r=t.VALUES_TO_ECI.get(e);if(void 0===r)throw new st("incorect value");return r},t.getCharacterSetECIByName=function(e){var r=t.NAME_TO_ECI.get(e);if(void 0===r)throw new st("incorect value");return r},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;return this.getName()===r.getName()},t.VALUE_IDENTIFIER_TO_ECI=new Map,t.VALUES_TO_ECI=new Map,t.NAME_TO_ECI=new Map,t.Cp437=new t(it.Cp437,Int32Array.from([0,2]),"Cp437"),t.ISO8859_1=new t(it.ISO8859_1,Int32Array.from([1,3]),"ISO-8859-1","ISO88591","ISO8859_1"),t.ISO8859_2=new t(it.ISO8859_2,4,"ISO-8859-2","ISO88592","ISO8859_2"),t.ISO8859_3=new t(it.ISO8859_3,5,"ISO-8859-3","ISO88593","ISO8859_3"),t.ISO8859_4=new t(it.ISO8859_4,6,"ISO-8859-4","ISO88594","ISO8859_4"),t.ISO8859_5=new t(it.ISO8859_5,7,"ISO-8859-5","ISO88595","ISO8859_5"),t.ISO8859_6=new t(it.ISO8859_6,8,"ISO-8859-6","ISO88596","ISO8859_6"),t.ISO8859_7=new t(it.ISO8859_7,9,"ISO-8859-7","ISO88597","ISO8859_7"),t.ISO8859_8=new t(it.ISO8859_8,10,"ISO-8859-8","ISO88598","ISO8859_8"),t.ISO8859_9=new t(it.ISO8859_9,11,"ISO-8859-9","ISO88599","ISO8859_9"),t.ISO8859_10=new t(it.ISO8859_10,12,"ISO-8859-10","ISO885910","ISO8859_10"),t.ISO8859_11=new t(it.ISO8859_11,13,"ISO-8859-11","ISO885911","ISO8859_11"),t.ISO8859_13=new t(it.ISO8859_13,15,"ISO-8859-13","ISO885913","ISO8859_13"),t.ISO8859_14=new t(it.ISO8859_14,16,"ISO-8859-14","ISO885914","ISO8859_14"),t.ISO8859_15=new t(it.ISO8859_15,17,"ISO-8859-15","ISO885915","ISO8859_15"),t.ISO8859_16=new t(it.ISO8859_16,18,"ISO-8859-16","ISO885916","ISO8859_16"),t.SJIS=new t(it.SJIS,20,"SJIS","Shift_JIS"),t.Cp1250=new t(it.Cp1250,21,"Cp1250","windows-1250"),t.Cp1251=new t(it.Cp1251,22,"Cp1251","windows-1251"),t.Cp1252=new t(it.Cp1252,23,"Cp1252","windows-1252"),t.Cp1256=new t(it.Cp1256,24,"Cp1256","windows-1256"),t.UnicodeBigUnmarked=new t(it.UnicodeBigUnmarked,25,"UnicodeBigUnmarked","UTF-16BE","UnicodeBig"),t.UTF8=new t(it.UTF8,26,"UTF8","UTF-8"),t.ASCII=new t(it.ASCII,Int32Array.from([27,170]),"ASCII","US-ASCII"),t.Big5=new t(it.Big5,28,"Big5"),t.GB18030=new t(it.GB18030,29,"GB18030","GB2312","EUC_CN","GBK"),t.EUC_KR=new t(it.EUC_KR,30,"EUC_KR","EUC-KR"),t}(),ht=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),lt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ht(e,t),e.kind="UnsupportedOperationException",e}(H),dt=function(){function t(){}return t.decode=function(t,e){var r=this.encodingName(e);return this.customDecoder?this.customDecoder(t,r):"undefined"==typeof TextDecoder||this.shouldDecodeOnFallback(r)?this.decodeFallback(t,r):new TextDecoder(r).decode(t)},t.shouldDecodeOnFallback=function(e){return!t.isBrowser()&&"ISO-8859-1"===e},t.encode=function(t,e){var r=this.encodingName(e);return this.customEncoder?this.customEncoder(t,r):"undefined"==typeof TextEncoder?this.encodeFallback(t):(new TextEncoder).encode(t)},t.isBrowser=function(){return"undefined"!=typeof window&&"[object Window]"==={}.toString.call(window)},t.encodingName=function(t){return"string"==typeof t?t:t.getName()},t.encodingCharacterSet=function(t){return t instanceof ft?t:ft.getCharacterSetECIByName(t)},t.decodeFallback=function(e,r){var n=this.encodingCharacterSet(r);if(t.isDecodeFallbackSupported(n)){for(var o="",i=0,a=e.length;i<a;i++){var s=e[i].toString(16);s.length<2&&(s="0"+s),o+="%"+s}return decodeURIComponent(o)}if(n.equals(ft.UnicodeBigUnmarked))return String.fromCharCode.apply(null,new Uint16Array(e.buffer));throw new lt("Encoding "+this.encodingName(r)+" not supported by fallback.")},t.isDecodeFallbackSupported=function(t){return t.equals(ft.UTF8)||t.equals(ft.ISO8859_1)||t.equals(ft.ASCII)},t.encodeFallback=function(t){for(var e=btoa(unescape(encodeURIComponent(t))).split(""),r=[],n=0;n<e.length;n++)r.push(e[n].charCodeAt(0));return new Uint8Array(r)},t}(),pt=function(){function t(){}return t.castAsNonUtf8Char=function(t,e){void 0===e&&(e=null);var r=e?e.getName():this.ISO88591;return dt.decode(new Uint8Array([t]),r)},t.guessEncoding=function(e,r){if(null!=r&&void 0!==r.get(k.CHARACTER_SET))return r.get(k.CHARACTER_SET).toString();for(var n=e.length,o=!0,i=!0,a=!0,s=0,u=0,c=0,f=0,h=0,l=0,d=0,p=0,g=0,y=0,w=0,v=e.length>3&&239===e[0]&&187===e[1]&&191===e[2],_=0;_<n&&(o||i||a);_++){var m=255&e[_];a&&(s>0?128&m?s--:a=!1:128&m&&(64&m?(s++,32&m?(s++,16&m?(s++,8&m?a=!1:f++):c++):u++):a=!1)),o&&(m>127&&m<160?o=!1:m>159&&(m<192||215===m||247===m)&&w++),i&&(h>0?m<64||127===m||m>252?i=!1:h--:128===m||160===m||m>239?i=!1:m>160&&m<224?(l++,p=0,++d>g&&(g=d)):m>127?(h++,d=0,++p>y&&(y=p)):(d=0,p=0))}return a&&s>0&&(a=!1),i&&h>0&&(i=!1),a&&(v||u+c+f>0)?t.UTF8:i&&(t.ASSUME_SHIFT_JIS||g>=3||y>=3)?t.SHIFT_JIS:o&&i?2===g&&2===l||10*w>=n?t.SHIFT_JIS:t.ISO88591:o?t.ISO88591:i?t.SHIFT_JIS:a?t.UTF8:t.PLATFORM_DEFAULT_ENCODING},t.format=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var n=-1;return t.replace(/%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g,function(t,r,o,i,a,s){if("%%"===t)return"%";if(void 0!==e[++n]){t=i?parseInt(i.substr(1)):void 0;var u,c=a?parseInt(a.substr(1)):void 0;switch(s){case"s":u=e[n];break;case"c":u=e[n][0];break;case"f":u=parseFloat(e[n]).toFixed(t);break;case"p":u=parseFloat(e[n]).toPrecision(t);break;case"e":u=parseFloat(e[n]).toExponential(t);break;case"x":u=parseInt(e[n]).toString(c||16);break;case"d":u=parseFloat(parseInt(e[n],c||10).toPrecision(t)).toFixed(0)}u="object"==typeof u?JSON.stringify(u):(+u).toString(c);for(var f=parseInt(o),h=o&&o[0]+""=="0"?"0":" ";u.length<f;)u=void 0!==r?u+h:h+u;return u}})},t.getBytes=function(t,e){return dt.encode(t,e)},t.getCharCode=function(t,e){return void 0===e&&(e=0),t.charCodeAt(e)},t.getCharAt=function(t){return String.fromCharCode(t)},t.SHIFT_JIS=ft.SJIS.getName(),t.GB2312="GB2312",t.ISO88591=ft.ISO8859_1.getName(),t.EUC_JP="EUC_JP",t.UTF8=ft.UTF8.getName(),t.PLATFORM_DEFAULT_ENCODING=t.UTF8,t.ASSUME_SHIFT_JIS=!1,t}(),gt=function(){function t(t){void 0===t&&(t=""),this.value=t}return t.prototype.enableDecoding=function(t){return this.encoding=t,this},t.prototype.append=function(t){return"string"==typeof t?this.value+=t.toString():this.encoding?this.value+=pt.castAsNonUtf8Char(t,this.encoding):this.value+=String.fromCharCode(t),this},t.prototype.appendChars=function(t,e,r){for(var n=e;e<e+r;n++)this.append(t[n]);return this},t.prototype.length=function(){return this.value.length},t.prototype.charAt=function(t){return this.value.charAt(t)},t.prototype.deleteCharAt=function(t){this.value=this.value.substr(0,t)+this.value.substring(t+1)},t.prototype.setCharAt=function(t,e){this.value=this.value.substr(0,t)+e+this.value.substr(t+1)},t.prototype.substring=function(t,e){return this.value.substring(t,e)},t.prototype.setLengthToZero=function(){this.value=""},t.prototype.toString=function(){return this.value},t.prototype.insert=function(t,e){this.value=this.value.substring(0,t)+e+this.value.substring(t)},t}(),yt=function(){function t(t,e,r,n){if(this.width=t,this.height=e,this.rowSize=r,this.bits=n,null==e&&(e=t),this.height=e,t<1||e<1)throw new j("Both dimensions must be greater than 0");null==r&&(r=Math.floor((t+31)/32)),this.rowSize=r,null==n&&(this.bits=new Int32Array(this.rowSize*this.height))}return t.parseFromBooleanArray=function(e){for(var r=e.length,n=e[0].length,o=new t(n,r),i=0;i<r;i++)for(var a=e[i],s=0;s<n;s++)a[s]&&o.set(s,i);return o},t.parseFromString=function(e,r,n){if(null===e)throw new j("stringRepresentation cannot be null");for(var o=new Array(e.length),i=0,a=0,s=-1,u=0,c=0;c<e.length;)if("\n"===e.charAt(c)||"\r"===e.charAt(c)){if(i>a){if(-1===s)s=i-a;else if(i-a!==s)throw new j("row lengths do not match");a=i,u++}c++}else if(e.substring(c,c+r.length)===r)c+=r.length,o[i]=!0,i++;else{if(e.substring(c,c+n.length)!==n)throw new j("illegal character encountered: "+e.substring(c));c+=n.length,o[i]=!1,i++}if(i>a){if(-1===s)s=i-a;else if(i-a!==s)throw new j("row lengths do not match");u++}for(var f=new t(s,u),h=0;h<i;h++)o[h]&&f.set(Math.floor(h%s),Math.floor(h/s));return f},t.prototype.get=function(t,e){var r=e*this.rowSize+Math.floor(t/32);return!!(this.bits[r]>>>(31&t)&1)},t.prototype.set=function(t,e){var r=e*this.rowSize+Math.floor(t/32);this.bits[r]|=1<<(31&t)&4294967295},t.prototype.unset=function(t,e){var r=e*this.rowSize+Math.floor(t/32);this.bits[r]&=~(1<<(31&t)&4294967295)},t.prototype.flip=function(t,e){var r=e*this.rowSize+Math.floor(t/32);this.bits[r]^=1<<(31&t)&4294967295},t.prototype.xor=function(t){if(this.width!==t.getWidth()||this.height!==t.getHeight()||this.rowSize!==t.getRowSize())throw new j("input matrix dimensions do not match");for(var e=new ot(Math.floor(this.width/32)+1),r=this.rowSize,n=this.bits,o=0,i=this.height;o<i;o++)for(var a=o*r,s=t.getRow(o,e).getBitArray(),u=0;u<r;u++)n[a+u]^=s[u]},t.prototype.clear=function(){for(var t=this.bits,e=t.length,r=0;r<e;r++)t[r]=0},t.prototype.setRegion=function(t,e,r,n){if(e<0||t<0)throw new j("Left and top must be nonnegative");if(n<1||r<1)throw new j("Height and width must be at least 1");var o=t+r,i=e+n;if(i>this.height||o>this.width)throw new j("The region must fit inside the matrix");for(var a=this.rowSize,s=this.bits,u=e;u<i;u++)for(var c=u*a,f=t;f<o;f++)s[c+Math.floor(f/32)]|=1<<(31&f)&4294967295},t.prototype.getRow=function(t,e){null==e||e.getSize()<this.width?e=new ot(this.width):e.clear();for(var r=this.rowSize,n=this.bits,o=t*r,i=0;i<r;i++)e.setBulk(32*i,n[o+i]);return e},t.prototype.setRow=function(t,e){q.arraycopy(e.getBitArray(),0,this.bits,t*this.rowSize,this.rowSize)},t.prototype.rotate180=function(){for(var t=this.getWidth(),e=this.getHeight(),r=new ot(t),n=new ot(t),o=0,i=Math.floor((e+1)/2);o<i;o++)r=this.getRow(o,r),n=this.getRow(e-1-o,n),r.reverse(),n.reverse(),this.setRow(o,n),this.setRow(e-1-o,r)},t.prototype.getEnclosingRectangle=function(){for(var t=this.width,e=this.height,r=this.rowSize,n=this.bits,o=t,i=e,a=-1,s=-1,u=0;u<e;u++)for(var c=0;c<r;c++){var f=n[u*r+c];if(0!==f){if(u<i&&(i=u),u>s&&(s=u),32*c<o){for(var h=0;!(f<<31-h&4294967295);)h++;32*c+h<o&&(o=32*c+h)}if(32*c+31>a){for(h=31;f>>>h===0;)h--;32*c+h>a&&(a=32*c+h)}}}return a<o||s<i?null:Int32Array.from([o,i,a-o+1,s-i+1])},t.prototype.getTopLeftOnBit=function(){for(var t=this.rowSize,e=this.bits,r=0;r<e.length&&0===e[r];)r++;if(r===e.length)return null;for(var n=r/t,o=r%t*32,i=e[r],a=0;!(i<<31-a&4294967295);)a++;return o+=a,Int32Array.from([o,n])},t.prototype.getBottomRightOnBit=function(){for(var t=this.rowSize,e=this.bits,r=e.length-1;r>=0&&0===e[r];)r--;if(r<0)return null;for(var n=Math.floor(r/t),o=32*Math.floor(r%t),i=e[r],a=31;i>>>a===0;)a--;return o+=a,Int32Array.from([o,n])},t.prototype.getWidth=function(){return this.width},t.prototype.getHeight=function(){return this.height},t.prototype.getRowSize=function(){return this.rowSize},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;return this.width===r.width&&this.height===r.height&&this.rowSize===r.rowSize&&rt.equals(this.bits,r.bits)},t.prototype.hashCode=function(){var t=this.width;return t=31*(t=31*(t=31*(t=31*t+this.width)+this.height)+this.rowSize)+rt.hashCode(this.bits)},t.prototype.toString=function(t,e,r){return void 0===t&&(t="X "),void 0===e&&(e="  "),void 0===r&&(r="\n"),this.buildToString(t,e,r)},t.prototype.buildToString=function(t,e,r){for(var n=new gt,o=0,i=this.height;o<i;o++){for(var a=0,s=this.width;a<s;a++)n.append(this.get(a,o)?t:e);n.append(r)}return n.toString()},t.prototype.clone=function(){return new t(this.width,this.height,this.rowSize,this.bits.slice())},t}(),wt=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),vt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return wt(e,t),e.getNotFoundInstance=function(){return new e},e.kind="NotFoundException",e}(H),_t=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),mt=function(t){function e(r){var n=t.call(this,r)||this;return n.luminances=e.EMPTY,n.buckets=new Int32Array(e.LUMINANCE_BUCKETS),n}return _t(e,t),e.prototype.getBlackRow=function(t,r){var n=this.getLuminanceSource(),o=n.getWidth();null==r||r.getSize()<o?r=new ot(o):r.clear(),this.initArrays(o);for(var i=n.getRow(t,this.luminances),a=this.buckets,s=0;s<o;s++)a[(255&i[s])>>e.LUMINANCE_SHIFT]++;var u=e.estimateBlackPoint(a);if(o<3)for(s=0;s<o;s++)(255&i[s])<u&&r.set(s);else{var c=255&i[0],f=255&i[1];for(s=1;s<o-1;s++){var h=255&i[s+1];(4*f-c-h)/2<u&&r.set(s),c=f,f=h}}return r},e.prototype.getBlackMatrix=function(){var t=this.getLuminanceSource(),r=t.getWidth(),n=t.getHeight(),o=new yt(r,n);this.initArrays(r);for(var i=this.buckets,a=1;a<5;a++)for(var s=Math.floor(n*a/5),u=t.getRow(s,this.luminances),c=Math.floor(4*r/5),f=Math.floor(r/5);f<c;f++)i[(255&u[f])>>e.LUMINANCE_SHIFT]++;var h=e.estimateBlackPoint(i),l=t.getMatrix();for(a=0;a<n;a++){var d=a*r;for(f=0;f<r;f++)(255&l[d+f])<h&&o.set(f,a)}return o},e.prototype.createBinarizer=function(t){return new e(t)},e.prototype.initArrays=function(t){this.luminances.length<t&&(this.luminances=new Uint8ClampedArray(t));for(var r=this.buckets,n=0;n<e.LUMINANCE_BUCKETS;n++)r[n]=0},e.estimateBlackPoint=function(t){for(var r=t.length,n=0,o=0,i=0,a=0;a<r;a++)t[a]>i&&(o=a,i=t[a]),t[a]>n&&(n=t[a]);var s=0,u=0;for(a=0;a<r;a++){var c=a-o;(d=t[a]*c*c)>u&&(s=a,u=d)}if(o>s){var f=o;o=s,s=f}if(s-o<=r/16)throw new vt;var h=s-1,l=-1;for(a=s-1;a>o;a--){var d,p=a-o;(d=p*p*(s-a)*(n-t[a]))>l&&(h=a,l=d)}return h<<e.LUMINANCE_SHIFT},e.LUMINANCE_BITS=5,e.LUMINANCE_SHIFT=8-e.LUMINANCE_BITS,e.LUMINANCE_BUCKETS=1<<e.LUMINANCE_BITS,e.EMPTY=Uint8ClampedArray.from([0]),e}(K),Ct=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),At=function(t){function e(e){var r=t.call(this,e)||this;return r.matrix=null,r}return Ct(e,t),e.prototype.getBlackMatrix=function(){if(null!==this.matrix)return this.matrix;var r=this.getLuminanceSource(),n=r.getWidth(),o=r.getHeight();if(n>=e.MINIMUM_DIMENSION&&o>=e.MINIMUM_DIMENSION){var i=r.getMatrix(),a=n>>e.BLOCK_SIZE_POWER;0!==(n&e.BLOCK_SIZE_MASK)&&a++;var s=o>>e.BLOCK_SIZE_POWER;0!==(o&e.BLOCK_SIZE_MASK)&&s++;var u=e.calculateBlackPoints(i,a,s,n,o),c=new yt(n,o);e.calculateThresholdForBlock(i,a,s,n,o,u,c),this.matrix=c}else this.matrix=t.prototype.getBlackMatrix.call(this);return this.matrix},e.prototype.createBinarizer=function(t){return new e(t)},e.calculateThresholdForBlock=function(t,r,n,o,i,a,s){for(var u=i-e.BLOCK_SIZE,c=o-e.BLOCK_SIZE,f=0;f<n;f++){var h=f<<e.BLOCK_SIZE_POWER;h>u&&(h=u);for(var l=e.cap(f,2,n-3),d=0;d<r;d++){var p=d<<e.BLOCK_SIZE_POWER;p>c&&(p=c);for(var g=e.cap(d,2,r-3),y=0,w=-2;w<=2;w++){var v=a[l+w];y+=v[g-2]+v[g-1]+v[g]+v[g+1]+v[g+2]}var _=y/25;e.thresholdBlock(t,p,h,_,o,s)}}},e.cap=function(t,e,r){return t<e?e:t>r?r:t},e.thresholdBlock=function(t,r,n,o,i,a){for(var s=0,u=n*i+r;s<e.BLOCK_SIZE;s++,u+=i)for(var c=0;c<e.BLOCK_SIZE;c++)(255&t[u+c])<=o&&a.set(r+c,n+s)},e.calculateBlackPoints=function(t,r,n,o,i){for(var a=i-e.BLOCK_SIZE,s=o-e.BLOCK_SIZE,u=new Array(n),c=0;c<n;c++){u[c]=new Int32Array(r);var f=c<<e.BLOCK_SIZE_POWER;f>a&&(f=a);for(var h=0;h<r;h++){var l=h<<e.BLOCK_SIZE_POWER;l>s&&(l=s);for(var d=0,p=255,g=0,y=0,w=f*o+l;y<e.BLOCK_SIZE;y++,w+=o){for(var v=0;v<e.BLOCK_SIZE;v++){var _=255&t[w+v];d+=_,_<p&&(p=_),_>g&&(g=_)}if(g-p>e.MIN_DYNAMIC_RANGE)for(y++,w+=o;y<e.BLOCK_SIZE;y++,w+=o)for(v=0;v<e.BLOCK_SIZE;v++)d+=255&t[w+v]}var m=d>>2*e.BLOCK_SIZE_POWER;if(g-p<=e.MIN_DYNAMIC_RANGE&&(m=p/2,c>0&&h>0)){var C=(u[c-1][h]+2*u[c][h-1]+u[c-1][h-1])/4;p<C&&(m=C)}u[c][h]=m}}return u},e.BLOCK_SIZE_POWER=3,e.BLOCK_SIZE=1<<e.BLOCK_SIZE_POWER,e.BLOCK_SIZE_MASK=e.BLOCK_SIZE-1,e.MINIMUM_DIMENSION=5*e.BLOCK_SIZE,e.MIN_DYNAMIC_RANGE=24,e}(mt),Et=function(){function t(t,e){this.width=t,this.height=e}return t.prototype.getWidth=function(){return this.width},t.prototype.getHeight=function(){return this.height},t.prototype.isCropSupported=function(){return!1},t.prototype.crop=function(t,e,r,n){throw new lt("This luminance source does not support cropping.")},t.prototype.isRotateSupported=function(){return!1},t.prototype.rotateCounterClockwise=function(){throw new lt("This luminance source does not support rotation by 90 degrees.")},t.prototype.rotateCounterClockwise45=function(){throw new lt("This luminance source does not support rotation by 45 degrees.")},t.prototype.toString=function(){for(var t=new Uint8ClampedArray(this.width),e=new gt,r=0;r<this.height;r++){for(var n=this.getRow(r,t),o=0;o<this.width;o++){var i=255&n[o],a=void 0;a=i<64?"#":i<128?"+":i<192?".":" ",e.append(a)}e.append("\n")}return e.toString()},t}(),It=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),St=function(t){function e(e){var r=t.call(this,e.getWidth(),e.getHeight())||this;return r.delegate=e,r}return It(e,t),e.prototype.getRow=function(t,e){for(var r=this.delegate.getRow(t,e),n=this.getWidth(),o=0;o<n;o++)r[o]=255-(255&r[o]);return r},e.prototype.getMatrix=function(){for(var t=this.delegate.getMatrix(),e=this.getWidth()*this.getHeight(),r=new Uint8ClampedArray(e),n=0;n<e;n++)r[n]=255-(255&t[n]);return r},e.prototype.isCropSupported=function(){return this.delegate.isCropSupported()},e.prototype.crop=function(t,r,n,o){return new e(this.delegate.crop(t,r,n,o))},e.prototype.isRotateSupported=function(){return this.delegate.isRotateSupported()},e.prototype.invert=function(){return this.delegate},e.prototype.rotateCounterClockwise=function(){return new e(this.delegate.rotateCounterClockwise())},e.prototype.rotateCounterClockwise45=function(){return new e(this.delegate.rotateCounterClockwise45())},e}(Et),bt=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Tt=function(t){function e(r){var n=t.call(this,r.width,r.height)||this;return n.canvas=r,n.tempCanvasElement=null,n.buffer=e.makeBufferFromCanvasImageData(r),n}return bt(e,t),e.makeBufferFromCanvasImageData=function(t){var r=t.getContext("2d").getImageData(0,0,t.width,t.height);return e.toGrayscaleBuffer(r.data,t.width,t.height)},e.toGrayscaleBuffer=function(t,r,n){var o=new Uint8ClampedArray(r*n);if(e.FRAME_INDEX=!e.FRAME_INDEX,e.FRAME_INDEX)for(var i=0,a=0,s=t.length;i<s;i+=4,a++){var u=void 0;u=0===t[i+3]?255:306*t[i]+601*t[i+1]+117*t[i+2]+512>>10,o[a]=u}else{i=0,a=0;for(var c=t.length;i<c;i+=4,a++)u=void 0,u=0===t[i+3]?255:306*t[i]+601*t[i+1]+117*t[i+2]+512>>10,o[a]=255-u}return o},e.prototype.getRow=function(t,e){if(t<0||t>=this.getHeight())throw new j("Requested row is outside the image: "+t);var r=this.getWidth(),n=t*r;return null===e?e=this.buffer.slice(n,n+r):(e.length<r&&(e=new Uint8ClampedArray(r)),e.set(this.buffer.slice(n,n+r))),e},e.prototype.getMatrix=function(){return this.buffer},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(e,r,n,o){return t.prototype.crop.call(this,e,r,n,o),this},e.prototype.isRotateSupported=function(){return!0},e.prototype.rotateCounterClockwise=function(){return this.rotate(-90),this},e.prototype.rotateCounterClockwise45=function(){return this.rotate(-45),this},e.prototype.getTempCanvasElement=function(){if(null===this.tempCanvasElement){var t=this.canvas.ownerDocument.createElement("canvas");t.width=this.canvas.width,t.height=this.canvas.height,this.tempCanvasElement=t}return this.tempCanvasElement},e.prototype.rotate=function(t){var r=this.getTempCanvasElement(),n=r.getContext("2d"),o=t*e.DEGREE_TO_RADIANS,i=this.canvas.width,a=this.canvas.height,s=Math.ceil(Math.abs(Math.cos(o))*i+Math.abs(Math.sin(o))*a),u=Math.ceil(Math.abs(Math.sin(o))*i+Math.abs(Math.cos(o))*a);return r.width=s,r.height=u,n.translate(s/2,u/2),n.rotate(o),n.drawImage(this.canvas,i/-2,a/-2),this.buffer=e.makeBufferFromCanvasImageData(r),this},e.prototype.invert=function(){return new St(this)},e.DEGREE_TO_RADIANS=Math.PI/180,e.FRAME_INDEX=!0,e}(Et),Ot=function(){function t(t,e,r){this.deviceId=t,this.label=e,this.kind="videoinput",this.groupId=r||void 0}return t.prototype.toJSON=function(){return{kind:this.kind,groupId:this.groupId,deviceId:this.deviceId,label:this.label}},t}(),Rt=function(t,e,r,n){return new(r||(r=Promise))(function(o,i){function a(t){try{u(n.next(t))}catch(e){i(e)}}function s(t){try{u(n.throw(t))}catch(e){i(e)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof r?e:new r(function(t){t(e)})).then(a,s)}u((n=n.apply(t,e||[])).next())})},Nt=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(s){i=[6,s],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},Dt=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Mt=function(){function t(t,e,r){void 0===e&&(e=500),this.reader=t,this.timeBetweenScansMillis=e,this._hints=r,this._stopContinuousDecode=!1,this._stopAsyncDecode=!1,this._timeBetweenDecodingAttempts=0}return Object.defineProperty(t.prototype,"hasNavigator",{get:function(){return"undefined"!=typeof navigator},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isMediaDevicesSuported",{get:function(){return this.hasNavigator&&!!navigator.mediaDevices},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"canEnumerateDevices",{get:function(){return!(!this.isMediaDevicesSuported||!navigator.mediaDevices.enumerateDevices)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"timeBetweenDecodingAttempts",{get:function(){return this._timeBetweenDecodingAttempts},set:function(t){this._timeBetweenDecodingAttempts=t<0?0:t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"hints",{get:function(){return this._hints},set:function(t){this._hints=t||null},enumerable:!1,configurable:!0}),t.prototype.listVideoInputDevices=function(){return Rt(this,void 0,void 0,function(){var t,e,r,n,o,i,a,s,u,c,f,h;return Nt(this,function(l){switch(l.label){case 0:if(!this.hasNavigator)throw new Error("Can't enumerate devices, navigator is not present.");if(!this.canEnumerateDevices)throw new Error("Can't enumerate devices, method not supported.");return[4,navigator.mediaDevices.enumerateDevices()];case 1:t=l.sent(),e=[];try{for(r=Dt(t),n=r.next();!n.done;n=r.next())o=n.value,"videoinput"===(i="video"===o.kind?"videoinput":o.kind)&&(a=o.deviceId||o.id,s=o.label||"Video device "+(e.length+1),u=o.groupId,c={deviceId:a,label:s,kind:i,groupId:u},e.push(c))}catch(d){f={error:d}}finally{try{n&&!n.done&&(h=r.return)&&h.call(r)}finally{if(f)throw f.error}}return[2,e]}})})},t.prototype.getVideoInputDevices=function(){return Rt(this,void 0,void 0,function(){return Nt(this,function(t){switch(t.label){case 0:return[4,this.listVideoInputDevices()];case 1:return[2,t.sent().map(function(t){return new Ot(t.deviceId,t.label)})]}})})},t.prototype.findDeviceById=function(t){return Rt(this,void 0,void 0,function(){var e;return Nt(this,function(r){switch(r.label){case 0:return[4,this.listVideoInputDevices()];case 1:return(e=r.sent())?[2,e.find(function(e){return e.deviceId===t})]:[2,null]}})})},t.prototype.decodeFromInputVideoDevice=function(t,e){return Rt(this,void 0,void 0,function(){return Nt(this,function(r){switch(r.label){case 0:return[4,this.decodeOnceFromVideoDevice(t,e)];case 1:return[2,r.sent()]}})})},t.prototype.decodeOnceFromVideoDevice=function(t,e){return Rt(this,void 0,void 0,function(){var r;return Nt(this,function(n){switch(n.label){case 0:return this.reset(),r={video:t?{deviceId:{exact:t}}:{facingMode:"environment"}},[4,this.decodeOnceFromConstraints(r,e)];case 1:return[2,n.sent()]}})})},t.prototype.decodeOnceFromConstraints=function(t,e){return Rt(this,void 0,void 0,function(){var r;return Nt(this,function(n){switch(n.label){case 0:return[4,navigator.mediaDevices.getUserMedia(t)];case 1:return r=n.sent(),[4,this.decodeOnceFromStream(r,e)];case 2:return[2,n.sent()]}})})},t.prototype.decodeOnceFromStream=function(t,e){return Rt(this,void 0,void 0,function(){var r;return Nt(this,function(n){switch(n.label){case 0:return this.reset(),[4,this.attachStreamToVideo(t,e)];case 1:return r=n.sent(),[4,this.decodeOnce(r)];case 2:return[2,n.sent()]}})})},t.prototype.decodeFromInputVideoDeviceContinuously=function(t,e,r){return Rt(this,void 0,void 0,function(){return Nt(this,function(n){switch(n.label){case 0:return[4,this.decodeFromVideoDevice(t,e,r)];case 1:return[2,n.sent()]}})})},t.prototype.decodeFromVideoDevice=function(t,e,r){return Rt(this,void 0,void 0,function(){var n;return Nt(this,function(o){switch(o.label){case 0:return n={video:t?{deviceId:{exact:t}}:{facingMode:"environment"}},[4,this.decodeFromConstraints(n,e,r)];case 1:return[2,o.sent()]}})})},t.prototype.decodeFromConstraints=function(t,e,r){return Rt(this,void 0,void 0,function(){var n;return Nt(this,function(o){switch(o.label){case 0:return[4,navigator.mediaDevices.getUserMedia(t)];case 1:return n=o.sent(),[4,this.decodeFromStream(n,e,r)];case 2:return[2,o.sent()]}})})},t.prototype.decodeFromStream=function(t,e,r){return Rt(this,void 0,void 0,function(){var n;return Nt(this,function(o){switch(o.label){case 0:return this.reset(),[4,this.attachStreamToVideo(t,e)];case 1:return n=o.sent(),[4,this.decodeContinuously(n,r)];case 2:return[2,o.sent()]}})})},t.prototype.stopAsyncDecode=function(){this._stopAsyncDecode=!0},t.prototype.stopContinuousDecode=function(){this._stopContinuousDecode=!0},t.prototype.attachStreamToVideo=function(t,e){return Rt(this,void 0,void 0,function(){var r;return Nt(this,function(n){switch(n.label){case 0:return r=this.prepareVideoElement(e),this.addVideoSource(r,t),this.videoElement=r,this.stream=t,[4,this.playVideoOnLoadAsync(r)];case 1:return n.sent(),[2,r]}})})},t.prototype.playVideoOnLoadAsync=function(t){var e=this;return new Promise(function(r,n){return e.playVideoOnLoad(t,function(){return r()})})},t.prototype.playVideoOnLoad=function(t,e){var r=this;this.videoEndedListener=function(){return r.stopStreams()},this.videoCanPlayListener=function(){return r.tryPlayVideo(t)},t.addEventListener("ended",this.videoEndedListener),t.addEventListener("canplay",this.videoCanPlayListener),t.addEventListener("playing",e),this.tryPlayVideo(t)},t.prototype.isVideoPlaying=function(t){return t.currentTime>0&&!t.paused&&!t.ended&&t.readyState>2},t.prototype.tryPlayVideo=function(t){return Rt(this,void 0,void 0,function(){return Nt(this,function(e){switch(e.label){case 0:if(this.isVideoPlaying(t))return console.warn("Trying to play video that is already playing."),[2];e.label=1;case 1:return e.trys.push([1,3,,4]),[4,t.play()];case 2:return e.sent(),[3,4];case 3:return e.sent(),console.warn("It was not possible to play the video."),[3,4];case 4:return[2]}})})},t.prototype.getMediaElement=function(t,e){var r=document.getElementById(t);if(!r)throw new X("element with id '"+t+"' not found");if(r.nodeName.toLowerCase()!==e.toLowerCase())throw new X("element with id '"+t+"' must be an "+e+" element");return r},t.prototype.decodeFromImage=function(t,e){if(!t&&!e)throw new X("either imageElement with a src set or an url must be provided");return e&&!t?this.decodeFromImageUrl(e):this.decodeFromImageElement(t)},t.prototype.decodeFromVideo=function(t,e){if(!t&&!e)throw new X("Either an element with a src set or an URL must be provided");return e&&!t?this.decodeFromVideoUrl(e):this.decodeFromVideoElement(t)},t.prototype.decodeFromVideoContinuously=function(t,e,r){if(void 0===t&&void 0===e)throw new X("Either an element with a src set or an URL must be provided");return e&&!t?this.decodeFromVideoUrlContinuously(e,r):this.decodeFromVideoElementContinuously(t,r)},t.prototype.decodeFromImageElement=function(t){if(!t)throw new X("An image element must be provided.");this.reset();var e=this.prepareImageElement(t);return this.imageElement=e,this.isImageLoaded(e)?this.decodeOnce(e,!1,!0):this._decodeOnLoadImage(e)},t.prototype.decodeFromVideoElement=function(t){var e=this._decodeFromVideoElementSetup(t);return this._decodeOnLoadVideo(e)},t.prototype.decodeFromVideoElementContinuously=function(t,e){var r=this._decodeFromVideoElementSetup(t);return this._decodeOnLoadVideoContinuously(r,e)},t.prototype._decodeFromVideoElementSetup=function(t){if(!t)throw new X("A video element must be provided.");this.reset();var e=this.prepareVideoElement(t);return this.videoElement=e,e},t.prototype.decodeFromImageUrl=function(t){if(!t)throw new X("An URL must be provided.");this.reset();var e=this.prepareImageElement();this.imageElement=e;var r=this._decodeOnLoadImage(e);return e.src=t,r},t.prototype.decodeFromVideoUrl=function(t){if(!t)throw new X("An URL must be provided.");this.reset();var e=this.prepareVideoElement(),r=this.decodeFromVideoElement(e);return e.src=t,r},t.prototype.decodeFromVideoUrlContinuously=function(t,e){if(!t)throw new X("An URL must be provided.");this.reset();var r=this.prepareVideoElement(),n=this.decodeFromVideoElementContinuously(r,e);return r.src=t,n},t.prototype._decodeOnLoadImage=function(t){var e=this;return new Promise(function(r,n){e.imageLoadedListener=function(){return e.decodeOnce(t,!1,!0).then(r,n)},t.addEventListener("load",e.imageLoadedListener)})},t.prototype._decodeOnLoadVideo=function(t){return Rt(this,void 0,void 0,function(){return Nt(this,function(e){switch(e.label){case 0:return[4,this.playVideoOnLoadAsync(t)];case 1:return e.sent(),[4,this.decodeOnce(t)];case 2:return[2,e.sent()]}})})},t.prototype._decodeOnLoadVideoContinuously=function(t,e){return Rt(this,void 0,void 0,function(){return Nt(this,function(r){switch(r.label){case 0:return[4,this.playVideoOnLoadAsync(t)];case 1:return r.sent(),this.decodeContinuously(t,e),[2]}})})},t.prototype.isImageLoaded=function(t){return!!t.complete&&0!==t.naturalWidth},t.prototype.prepareImageElement=function(t){var e;return void 0===t&&((e=document.createElement("img")).width=200,e.height=200),"string"==typeof t&&(e=this.getMediaElement(t,"img")),t instanceof HTMLImageElement&&(e=t),e},t.prototype.prepareVideoElement=function(t){var e;return t||"undefined"==typeof document||((e=document.createElement("video")).width=200,e.height=200),"string"==typeof t&&(e=this.getMediaElement(t,"video")),t instanceof HTMLVideoElement&&(e=t),e.setAttribute("autoplay","true"),e.setAttribute("muted","true"),e.setAttribute("playsinline","true"),e},t.prototype.decodeOnce=function(t,e,r){var n=this;void 0===e&&(e=!0),void 0===r&&(r=!0),this._stopAsyncDecode=!1;var o=function(i,a){if(n._stopAsyncDecode)return a(new vt("Video stream has ended before any code could be detected.")),void(n._stopAsyncDecode=void 0);try{i(n.decode(t))}catch(s){if(e&&s instanceof vt||(s instanceof Z||s instanceof st)&&r)return setTimeout(o,n._timeBetweenDecodingAttempts,i,a);a(s)}};return new Promise(function(t,e){return o(t,e)})},t.prototype.decodeContinuously=function(t,e){var r=this;this._stopContinuousDecode=!1;var n=function(){if(r._stopContinuousDecode)r._stopContinuousDecode=void 0;else try{var o=r.decode(t);e(o,null),setTimeout(n,r.timeBetweenScansMillis)}catch(i){e(null,i),(i instanceof Z||i instanceof st||i instanceof vt)&&setTimeout(n,r._timeBetweenDecodingAttempts)}};n()},t.prototype.decode=function(t){var e=this.createBinaryBitmap(t);return this.decodeBitmap(e)},t.prototype.createBinaryBitmap=function(t){this.getCaptureCanvasContext(t),t instanceof HTMLVideoElement?this.drawFrameOnCanvas(t):this.drawImageOnCanvas(t);var e=this.getCaptureCanvas(t),r=new Tt(e),n=new At(r);return new z(n)},t.prototype.getCaptureCanvasContext=function(t){if(!this.captureCanvasContext){var e=this.getCaptureCanvas(t),r=void 0;try{r=e.getContext("2d",{willReadFrequently:!0})}catch(n){r=e.getContext("2d")}this.captureCanvasContext=r}return this.captureCanvasContext},t.prototype.getCaptureCanvas=function(t){if(!this.captureCanvas){var e=this.createCaptureCanvas(t);this.captureCanvas=e}return this.captureCanvas},t.prototype.drawFrameOnCanvas=function(t,e,r){void 0===e&&(e={sx:0,sy:0,sWidth:t.videoWidth,sHeight:t.videoHeight,dx:0,dy:0,dWidth:t.videoWidth,dHeight:t.videoHeight}),void 0===r&&(r=this.captureCanvasContext),r.drawImage(t,e.sx,e.sy,e.sWidth,e.sHeight,e.dx,e.dy,e.dWidth,e.dHeight)},t.prototype.drawImageOnCanvas=function(t,e,r){void 0===e&&(e={sx:0,sy:0,sWidth:t.naturalWidth,sHeight:t.naturalHeight,dx:0,dy:0,dWidth:t.naturalWidth,dHeight:t.naturalHeight}),void 0===r&&(r=this.captureCanvasContext),r.drawImage(t,e.sx,e.sy,e.sWidth,e.sHeight,e.dx,e.dy,e.dWidth,e.dHeight)},t.prototype.decodeBitmap=function(t){return this.reader.decode(t,this._hints)},t.prototype.createCaptureCanvas=function(t){if("undefined"==typeof document)return this._destroyCaptureCanvas(),null;var e,r,n=document.createElement("canvas");return void 0!==t&&(t instanceof HTMLVideoElement?(e=t.videoWidth,r=t.videoHeight):t instanceof HTMLImageElement&&(e=t.naturalWidth||t.width,r=t.naturalHeight||t.height)),n.style.width=e+"px",n.style.height=r+"px",n.width=e,n.height=r,n},t.prototype.stopStreams=function(){this.stream&&(this.stream.getVideoTracks().forEach(function(t){return t.stop()}),this.stream=void 0),!1===this._stopAsyncDecode&&this.stopAsyncDecode(),!1===this._stopContinuousDecode&&this.stopContinuousDecode()},t.prototype.reset=function(){this.stopStreams(),this._destroyVideoElement(),this._destroyImageElement(),this._destroyCaptureCanvas()},t.prototype._destroyVideoElement=function(){this.videoElement&&(void 0!==this.videoEndedListener&&this.videoElement.removeEventListener("ended",this.videoEndedListener),void 0!==this.videoPlayingEventListener&&this.videoElement.removeEventListener("playing",this.videoPlayingEventListener),void 0!==this.videoCanPlayListener&&this.videoElement.removeEventListener("loadedmetadata",this.videoCanPlayListener),this.cleanVideoSource(this.videoElement),this.videoElement=void 0)},t.prototype._destroyImageElement=function(){this.imageElement&&(void 0!==this.imageLoadedListener&&this.imageElement.removeEventListener("load",this.imageLoadedListener),this.imageElement.src=void 0,this.imageElement.removeAttribute("src"),this.imageElement=void 0)},t.prototype._destroyCaptureCanvas=function(){this.captureCanvasContext=void 0,this.captureCanvas=void 0},t.prototype.addVideoSource=function(t,e){try{t.srcObject=e}catch(r){t.src=URL.createObjectURL(e)}},t.prototype.cleanVideoSource=function(t){try{t.srcObject=null}catch(e){t.src=""}this.videoElement.removeAttribute("src")},t}(),Pt=function(){function t(t,e,r,n,o,i){void 0===r&&(r=null==e?0:8*e.length),void 0===i&&(i=q.currentTimeMillis()),this.text=t,this.rawBytes=e,this.numBits=r,this.resultPoints=n,this.format=o,this.timestamp=i,this.text=t,this.rawBytes=e,this.numBits=null==r?null==e?0:8*e.length:r,this.resultPoints=n,this.format=o,this.resultMetadata=null,this.timestamp=null==i?q.currentTimeMillis():i}return t.prototype.getText=function(){return this.text},t.prototype.getRawBytes=function(){return this.rawBytes},t.prototype.getNumBits=function(){return this.numBits},t.prototype.getResultPoints=function(){return this.resultPoints},t.prototype.getBarcodeFormat=function(){return this.format},t.prototype.getResultMetadata=function(){return this.resultMetadata},t.prototype.putMetadata=function(t,e){null===this.resultMetadata&&(this.resultMetadata=new Map),this.resultMetadata.set(t,e)},t.prototype.putAllMetadata=function(t){null!==t&&(null===this.resultMetadata?this.resultMetadata=t:this.resultMetadata=new Map(t))},t.prototype.addResultPoints=function(t){var e=this.resultPoints;if(null===e)this.resultPoints=t;else if(null!==t&&t.length>0){var r=new Array(e.length+t.length);q.arraycopy(e,0,r,0,e.length),q.arraycopy(t,0,r,e.length,t.length),this.resultPoints=r}},t.prototype.getTimestamp=function(){return this.timestamp},t.prototype.toString=function(){return this.text},t}();!function(t){t[t.AZTEC=0]="AZTEC",t[t.CODABAR=1]="CODABAR",t[t.CODE_39=2]="CODE_39",t[t.CODE_93=3]="CODE_93",t[t.CODE_128=4]="CODE_128",t[t.DATA_MATRIX=5]="DATA_MATRIX",t[t.EAN_8=6]="EAN_8",t[t.EAN_13=7]="EAN_13",t[t.ITF=8]="ITF",t[t.MAXICODE=9]="MAXICODE",t[t.PDF_417=10]="PDF_417",t[t.QR_CODE=11]="QR_CODE",t[t.RSS_14=12]="RSS_14",t[t.RSS_EXPANDED=13]="RSS_EXPANDED",t[t.UPC_A=14]="UPC_A",t[t.UPC_E=15]="UPC_E",t[t.UPC_EAN_EXTENSION=16]="UPC_EAN_EXTENSION"}(ct||(ct={}));const Bt=ct;var Lt;!function(t){t[t.OTHER=0]="OTHER",t[t.ORIENTATION=1]="ORIENTATION",t[t.BYTE_SEGMENTS=2]="BYTE_SEGMENTS",t[t.ERROR_CORRECTION_LEVEL=3]="ERROR_CORRECTION_LEVEL",t[t.ISSUE_NUMBER=4]="ISSUE_NUMBER",t[t.SUGGESTED_PRICE=5]="SUGGESTED_PRICE",t[t.POSSIBLE_COUNTRY=6]="POSSIBLE_COUNTRY",t[t.UPC_EAN_EXTENSION=7]="UPC_EAN_EXTENSION",t[t.PDF417_EXTRA_METADATA=8]="PDF417_EXTRA_METADATA",t[t.STRUCTURED_APPEND_SEQUENCE=9]="STRUCTURED_APPEND_SEQUENCE",t[t.STRUCTURED_APPEND_PARITY=10]="STRUCTURED_APPEND_PARITY"}(Lt||(Lt={}));const Ft=Lt;var kt,xt=function(){function t(t,e,r,n,o,i){void 0===o&&(o=-1),void 0===i&&(i=-1),this.rawBytes=t,this.text=e,this.byteSegments=r,this.ecLevel=n,this.structuredAppendSequenceNumber=o,this.structuredAppendParity=i,this.numBits=null==t?0:8*t.length}return t.prototype.getRawBytes=function(){return this.rawBytes},t.prototype.getNumBits=function(){return this.numBits},t.prototype.setNumBits=function(t){this.numBits=t},t.prototype.getText=function(){return this.text},t.prototype.getByteSegments=function(){return this.byteSegments},t.prototype.getECLevel=function(){return this.ecLevel},t.prototype.getErrorsCorrected=function(){return this.errorsCorrected},t.prototype.setErrorsCorrected=function(t){this.errorsCorrected=t},t.prototype.getErasures=function(){return this.erasures},t.prototype.setErasures=function(t){this.erasures=t},t.prototype.getOther=function(){return this.other},t.prototype.setOther=function(t){this.other=t},t.prototype.hasStructuredAppend=function(){return this.structuredAppendParity>=0&&this.structuredAppendSequenceNumber>=0},t.prototype.getStructuredAppendParity=function(){return this.structuredAppendParity},t.prototype.getStructuredAppendSequenceNumber=function(){return this.structuredAppendSequenceNumber},t}(),Vt=function(){function t(){}return t.prototype.exp=function(t){return this.expTable[t]},t.prototype.log=function(t){if(0===t)throw new j;return this.logTable[t]},t.addOrSubtract=function(t,e){return t^e},t}(),Ut=function(){function t(t,e){if(0===e.length)throw new j;this.field=t;var r=e.length;if(r>1&&0===e[0]){for(var n=1;n<r&&0===e[n];)n++;n===r?this.coefficients=Int32Array.from([0]):(this.coefficients=new Int32Array(r-n),q.arraycopy(e,n,this.coefficients,0,this.coefficients.length))}else this.coefficients=e}return t.prototype.getCoefficients=function(){return this.coefficients},t.prototype.getDegree=function(){return this.coefficients.length-1},t.prototype.isZero=function(){return 0===this.coefficients[0]},t.prototype.getCoefficient=function(t){return this.coefficients[this.coefficients.length-1-t]},t.prototype.evaluateAt=function(t){if(0===t)return this.getCoefficient(0);var e,r=this.coefficients;if(1===t){e=0;for(var n=0,o=r.length;n!==o;n++){var i=r[n];e=Vt.addOrSubtract(e,i)}return e}e=r[0];var a=r.length,s=this.field;for(n=1;n<a;n++)e=Vt.addOrSubtract(s.multiply(t,e),r[n]);return e},t.prototype.addOrSubtract=function(e){if(!this.field.equals(e.field))throw new j("GenericGFPolys do not have same GenericGF field");if(this.isZero())return e;if(e.isZero())return this;var r=this.coefficients,n=e.coefficients;if(r.length>n.length){var o=r;r=n,n=o}var i=new Int32Array(n.length),a=n.length-r.length;q.arraycopy(n,0,i,0,a);for(var s=a;s<n.length;s++)i[s]=Vt.addOrSubtract(r[s-a],n[s]);return new t(this.field,i)},t.prototype.multiply=function(e){if(!this.field.equals(e.field))throw new j("GenericGFPolys do not have same GenericGF field");if(this.isZero()||e.isZero())return this.field.getZero();for(var r=this.coefficients,n=r.length,o=e.coefficients,i=o.length,a=new Int32Array(n+i-1),s=this.field,u=0;u<n;u++)for(var c=r[u],f=0;f<i;f++)a[u+f]=Vt.addOrSubtract(a[u+f],s.multiply(c,o[f]));return new t(s,a)},t.prototype.multiplyScalar=function(e){if(0===e)return this.field.getZero();if(1===e)return this;for(var r=this.coefficients.length,n=this.field,o=new Int32Array(r),i=this.coefficients,a=0;a<r;a++)o[a]=n.multiply(i[a],e);return new t(n,o)},t.prototype.multiplyByMonomial=function(e,r){if(e<0)throw new j;if(0===r)return this.field.getZero();for(var n=this.coefficients,o=n.length,i=new Int32Array(o+e),a=this.field,s=0;s<o;s++)i[s]=a.multiply(n[s],r);return new t(a,i)},t.prototype.divide=function(t){if(!this.field.equals(t.field))throw new j("GenericGFPolys do not have same GenericGF field");if(t.isZero())throw new j("Divide by 0");for(var e=this.field,r=e.getZero(),n=this,o=t.getCoefficient(t.getDegree()),i=e.inverse(o);n.getDegree()>=t.getDegree()&&!n.isZero();){var a=n.getDegree()-t.getDegree(),s=e.multiply(n.getCoefficient(n.getDegree()),i),u=t.multiplyByMonomial(a,s),c=e.buildMonomial(a,s);r=r.addOrSubtract(c),n=n.addOrSubtract(u)}return[r,n]},t.prototype.toString=function(){for(var t="",e=this.getDegree();e>=0;e--){var r=this.getCoefficient(e);if(0!==r){if(r<0?(t+=" - ",r=-r):t.length>0&&(t+=" + "),0===e||1!==r){var n=this.field.log(r);0===n?t+="1":1===n?t+="a":(t+="a^",t+=n)}0!==e&&(1===e?t+="x":(t+="x^",t+=e))}}return t},t}(),Ht=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Gt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return Ht(e,t),e.kind="ArithmeticException",e}(H),Xt=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Wt=function(t){function e(e,r,n){var o=t.call(this)||this;o.primitive=e,o.size=r,o.generatorBase=n;for(var i=new Int32Array(r),a=1,s=0;s<r;s++)i[s]=a,(a*=2)>=r&&(a^=e,a&=r-1);o.expTable=i;var u=new Int32Array(r);for(s=0;s<r-1;s++)u[i[s]]=s;return o.logTable=u,o.zero=new Ut(o,Int32Array.from([0])),o.one=new Ut(o,Int32Array.from([1])),o}return Xt(e,t),e.prototype.getZero=function(){return this.zero},e.prototype.getOne=function(){return this.one},e.prototype.buildMonomial=function(t,e){if(t<0)throw new j;if(0===e)return this.zero;var r=new Int32Array(t+1);return r[0]=e,new Ut(this,r)},e.prototype.inverse=function(t){if(0===t)throw new Gt;return this.expTable[this.size-this.logTable[t]-1]},e.prototype.multiply=function(t,e){return 0===t||0===e?0:this.expTable[(this.logTable[t]+this.logTable[e])%(this.size-1)]},e.prototype.getSize=function(){return this.size},e.prototype.getGeneratorBase=function(){return this.generatorBase},e.prototype.toString=function(){return"GF(0x"+nt.toHexString(this.primitive)+","+this.size+")"},e.prototype.equals=function(t){return t===this},e.AZTEC_DATA_12=new e(4201,4096,1),e.AZTEC_DATA_10=new e(1033,1024,1),e.AZTEC_DATA_6=new e(67,64,1),e.AZTEC_PARAM=new e(19,16,1),e.QR_CODE_FIELD_256=new e(285,256,0),e.DATA_MATRIX_FIELD_256=new e(301,256,1),e.AZTEC_DATA_8=e.DATA_MATRIX_FIELD_256,e.MAXICODE_FIELD_64=e.AZTEC_DATA_6,e}(Vt),jt=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),zt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return jt(e,t),e.kind="ReedSolomonException",e}(H),Yt=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Zt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return Yt(e,t),e.kind="IllegalStateException",e}(H),Kt=function(){function t(t){this.field=t}return t.prototype.decode=function(t,e){for(var r=this.field,n=new Ut(r,t),o=new Int32Array(e),i=!0,a=0;a<e;a++){var s=n.evaluateAt(r.exp(a+r.getGeneratorBase()));o[o.length-1-a]=s,0!==s&&(i=!1)}if(!i){var u=new Ut(r,o),c=this.runEuclideanAlgorithm(r.buildMonomial(e,1),u,e),f=c[0],h=c[1],l=this.findErrorLocations(f),d=this.findErrorMagnitudes(h,l);for(a=0;a<l.length;a++){var p=t.length-1-r.log(l[a]);if(p<0)throw new zt("Bad error location");t[p]=Wt.addOrSubtract(t[p],d[a])}}},t.prototype.runEuclideanAlgorithm=function(t,e,r){if(t.getDegree()<e.getDegree()){var n=t;t=e,e=n}for(var o=this.field,i=t,a=e,s=o.getZero(),u=o.getOne();a.getDegree()>=(r/2|0);){var c=i,f=s;if(s=u,(i=a).isZero())throw new zt("r_{i-1} was zero");a=c;for(var h=o.getZero(),l=i.getCoefficient(i.getDegree()),d=o.inverse(l);a.getDegree()>=i.getDegree()&&!a.isZero();){var p=a.getDegree()-i.getDegree(),g=o.multiply(a.getCoefficient(a.getDegree()),d);h=h.addOrSubtract(o.buildMonomial(p,g)),a=a.addOrSubtract(i.multiplyByMonomial(p,g))}if(u=h.multiply(s).addOrSubtract(f),a.getDegree()>=i.getDegree())throw new Zt("Division algorithm failed to reduce polynomial?")}var y=u.getCoefficient(0);if(0===y)throw new zt("sigmaTilde(0) was zero");var w=o.inverse(y);return[u.multiplyScalar(w),a.multiplyScalar(w)]},t.prototype.findErrorLocations=function(t){var e=t.getDegree();if(1===e)return Int32Array.from([t.getCoefficient(1)]);for(var r=new Int32Array(e),n=0,o=this.field,i=1;i<o.getSize()&&n<e;i++)0===t.evaluateAt(i)&&(r[n]=o.inverse(i),n++);if(n!==e)throw new zt("Error locator degree does not match number of roots");return r},t.prototype.findErrorMagnitudes=function(t,e){for(var r=e.length,n=new Int32Array(r),o=this.field,i=0;i<r;i++){for(var a=o.inverse(e[i]),s=1,u=0;u<r;u++)if(i!==u){var c=o.multiply(e[u],a),f=1&c?-2&c:1|c;s=o.multiply(s,f)}n[i]=o.multiply(t.evaluateAt(a),o.inverse(s)),0!==o.getGeneratorBase()&&(n[i]=o.multiply(n[i],a))}return n},t}();!function(t){t[t.UPPER=0]="UPPER",t[t.LOWER=1]="LOWER",t[t.MIXED=2]="MIXED",t[t.DIGIT=3]="DIGIT",t[t.PUNCT=4]="PUNCT",t[t.BINARY=5]="BINARY"}(kt||(kt={}));var qt=function(){function t(){}return t.prototype.decode=function(e){this.ddata=e;var r=e.getBits(),n=this.extractBits(r),o=this.correctBits(n),i=t.convertBoolArrayToByteArray(o),a=t.getEncodedData(o),s=new xt(i,a,null,null);return s.setNumBits(o.length),s},t.highLevelDecode=function(t){return this.getEncodedData(t)},t.getEncodedData=function(e){for(var r=e.length,n=kt.UPPER,o=kt.UPPER,i="",a=0;a<r;)if(o===kt.BINARY){if(r-a<5)break;var s=t.readCode(e,a,5);if(a+=5,0===s){if(r-a<11)break;s=t.readCode(e,a,11)+31,a+=11}for(var u=0;u<s;u++){if(r-a<8){a=r;break}var c=t.readCode(e,a,8);i+=pt.castAsNonUtf8Char(c),a+=8}o=n}else{var f=o===kt.DIGIT?4:5;if(r-a<f)break;c=t.readCode(e,a,f),a+=f;var h=t.getCharacter(o,c);h.startsWith("CTRL_")?(n=o,o=t.getTable(h.charAt(5)),"L"===h.charAt(6)&&(n=o)):(i+=h,o=n)}return i},t.getTable=function(t){switch(t){case"L":return kt.LOWER;case"P":return kt.PUNCT;case"M":return kt.MIXED;case"D":return kt.DIGIT;case"B":return kt.BINARY;default:return kt.UPPER}},t.getCharacter=function(e,r){switch(e){case kt.UPPER:return t.UPPER_TABLE[r];case kt.LOWER:return t.LOWER_TABLE[r];case kt.MIXED:return t.MIXED_TABLE[r];case kt.PUNCT:return t.PUNCT_TABLE[r];case kt.DIGIT:return t.DIGIT_TABLE[r];default:throw new Zt("Bad table")}},t.prototype.correctBits=function(e){var r,n;this.ddata.getNbLayers()<=2?(n=6,r=Wt.AZTEC_DATA_6):this.ddata.getNbLayers()<=8?(n=8,r=Wt.AZTEC_DATA_8):this.ddata.getNbLayers()<=22?(n=10,r=Wt.AZTEC_DATA_10):(n=12,r=Wt.AZTEC_DATA_12);var o=this.ddata.getNbDatablocks(),i=e.length/n;if(i<o)throw new st;for(var a=e.length%n,s=new Int32Array(i),u=0;u<i;u++,a+=n)s[u]=t.readCode(e,a,n);try{new Kt(r).decode(s,i-o)}catch(g){throw new st(g)}var c=(1<<n)-1,f=0;for(u=0;u<o;u++){if(0===(d=s[u])||d===c)throw new st;1!==d&&d!==c-1||f++}var h=new Array(o*n-f),l=0;for(u=0;u<o;u++){var d;if(1===(d=s[u])||d===c-1)h.fill(d>1,l,l+n-1),l+=n-1;else for(var p=n-1;p>=0;--p)h[l++]=!!(d&1<<p)}return h},t.prototype.extractBits=function(t){var e=this.ddata.isCompact(),r=this.ddata.getNbLayers(),n=(e?11:14)+4*r,o=new Int32Array(n),i=new Array(this.totalBitsInLayer(r,e));if(e)for(var a=0;a<o.length;a++)o[a]=a;else{var s=n+1+2*nt.truncDivision(nt.truncDivision(n,2)-1,15),u=n/2,c=nt.truncDivision(s,2);for(a=0;a<u;a++){var f=a+nt.truncDivision(a,15);o[u-a-1]=c-f-1,o[u+a]=c+f+1}}a=0;for(var h=0;a<r;a++){for(var l=4*(r-a)+(e?9:12),d=2*a,p=n-1-d,g=0;g<l;g++)for(var y=2*g,w=0;w<2;w++)i[h+y+w]=t.get(o[d+w],o[d+g]),i[h+2*l+y+w]=t.get(o[d+g],o[p-w]),i[h+4*l+y+w]=t.get(o[p-w],o[p-g]),i[h+6*l+y+w]=t.get(o[p-g],o[d+w]);h+=8*l}return i},t.readCode=function(t,e,r){for(var n=0,o=e;o<e+r;o++)n<<=1,t[o]&&(n|=1);return n},t.readByte=function(e,r){var n=e.length-r;return n>=8?t.readCode(e,r,8):t.readCode(e,r,n)<<8-n},t.convertBoolArrayToByteArray=function(e){for(var r=new Uint8Array((e.length+7)/8),n=0;n<r.length;n++)r[n]=t.readByte(e,8*n);return r},t.prototype.totalBitsInLayer=function(t,e){return((e?88:112)+16*t)*t},t.UPPER_TABLE=["CTRL_PS"," ","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","CTRL_LL","CTRL_ML","CTRL_DL","CTRL_BS"],t.LOWER_TABLE=["CTRL_PS"," ","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","CTRL_US","CTRL_ML","CTRL_DL","CTRL_BS"],t.MIXED_TABLE=["CTRL_PS"," ","\\1","\\2","\\3","\\4","\\5","\\6","\\7","\b","\t","\n","\\13","\f","\r","\\33","\\34","\\35","\\36","\\37","@","\\","^","_","`","|","~","\\177","CTRL_LL","CTRL_UL","CTRL_PL","CTRL_BS"],t.PUNCT_TABLE=["","\r","\r\n",". ",", ",": ","!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","[","]","{","}","CTRL_UL"],t.DIGIT_TABLE=["CTRL_PS"," ","0","1","2","3","4","5","6","7","8","9",",",".","CTRL_UL","CTRL_US"],t}(),Qt=function(){function t(){}return t.round=function(t){return isNaN(t)?0:t<=Number.MIN_SAFE_INTEGER?Number.MIN_SAFE_INTEGER:t>=Number.MAX_SAFE_INTEGER?Number.MAX_SAFE_INTEGER:t+(t<0?-.5:.5)|0},t.distance=function(t,e,r,n){var o=t-r,i=e-n;return Math.sqrt(o*o+i*i)},t.sum=function(t){for(var e=0,r=0,n=t.length;r!==n;r++)e+=t[r];return e},t}(),Jt=function(){function t(){}return t.floatToIntBits=function(t){return t},t.MAX_VALUE=Number.MAX_SAFE_INTEGER,t}(),$t=function(){function t(t,e){this.x=t,this.y=e}return t.prototype.getX=function(){return this.x},t.prototype.getY=function(){return this.y},t.prototype.equals=function(e){if(e instanceof t){var r=e;return this.x===r.x&&this.y===r.y}return!1},t.prototype.hashCode=function(){return 31*Jt.floatToIntBits(this.x)+Jt.floatToIntBits(this.y)},t.prototype.toString=function(){return"("+this.x+","+this.y+")"},t.orderBestPatterns=function(t){var e,r,n,o=this.distance(t[0],t[1]),i=this.distance(t[1],t[2]),a=this.distance(t[0],t[2]);if(i>=o&&i>=a?(r=t[0],e=t[1],n=t[2]):a>=i&&a>=o?(r=t[1],e=t[0],n=t[2]):(r=t[2],e=t[0],n=t[1]),this.crossProductZ(e,r,n)<0){var s=e;e=n,n=s}t[0]=e,t[1]=r,t[2]=n},t.distance=function(t,e){return Qt.distance(t.x,t.y,e.x,e.y)},t.crossProductZ=function(t,e,r){var n=e.x,o=e.y;return(r.x-n)*(t.y-o)-(r.y-o)*(t.x-n)},t}(),te=function(){function t(t,e){this.bits=t,this.points=e}return t.prototype.getBits=function(){return this.bits},t.prototype.getPoints=function(){return this.points},t}(),ee=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),re=function(t){function e(e,r,n,o,i){var a=t.call(this,e,r)||this;return a.compact=n,a.nbDatablocks=o,a.nbLayers=i,a}return ee(e,t),e.prototype.getNbLayers=function(){return this.nbLayers},e.prototype.getNbDatablocks=function(){return this.nbDatablocks},e.prototype.isCompact=function(){return this.compact},e}(te),ne=function(){function t(e,r,n,o){this.image=e,this.height=e.getHeight(),this.width=e.getWidth(),null==r&&(r=t.INIT_SIZE),null==n&&(n=e.getWidth()/2|0),null==o&&(o=e.getHeight()/2|0);var i=r/2|0;if(this.leftInit=n-i,this.rightInit=n+i,this.upInit=o-i,this.downInit=o+i,this.upInit<0||this.leftInit<0||this.downInit>=this.height||this.rightInit>=this.width)throw new vt}return t.prototype.detect=function(){for(var t=this.leftInit,e=this.rightInit,r=this.upInit,n=this.downInit,o=!1,i=!0,a=!1,s=!1,u=!1,c=!1,f=!1,h=this.width,l=this.height;i;){i=!1;for(var d=!0;(d||!s)&&e<h;)(d=this.containsBlackPoint(r,n,e,!1))?(e++,i=!0,s=!0):s||e++;if(e>=h){o=!0;break}for(var p=!0;(p||!u)&&n<l;)(p=this.containsBlackPoint(t,e,n,!0))?(n++,i=!0,u=!0):u||n++;if(n>=l){o=!0;break}for(var g=!0;(g||!c)&&t>=0;)(g=this.containsBlackPoint(r,n,t,!1))?(t--,i=!0,c=!0):c||t--;if(t<0){o=!0;break}for(var y=!0;(y||!f)&&r>=0;)(y=this.containsBlackPoint(t,e,r,!0))?(r--,i=!0,f=!0):f||r--;if(r<0){o=!0;break}i&&(a=!0)}if(!o&&a){for(var w=e-t,v=null,_=1;null===v&&_<w;_++)v=this.getBlackPointOnSegment(t,n-_,t+_,n);if(null==v)throw new vt;var m=null;for(_=1;null===m&&_<w;_++)m=this.getBlackPointOnSegment(t,r+_,t+_,r);if(null==m)throw new vt;var C=null;for(_=1;null===C&&_<w;_++)C=this.getBlackPointOnSegment(e,r+_,e-_,r);if(null==C)throw new vt;var A=null;for(_=1;null===A&&_<w;_++)A=this.getBlackPointOnSegment(e,n-_,e-_,n);if(null==A)throw new vt;return this.centerEdges(A,v,C,m)}throw new vt},t.prototype.getBlackPointOnSegment=function(t,e,r,n){for(var o=Qt.round(Qt.distance(t,e,r,n)),i=(r-t)/o,a=(n-e)/o,s=this.image,u=0;u<o;u++){var c=Qt.round(t+u*i),f=Qt.round(e+u*a);if(s.get(c,f))return new $t(c,f)}return null},t.prototype.centerEdges=function(e,r,n,o){var i=e.getX(),a=e.getY(),s=r.getX(),u=r.getY(),c=n.getX(),f=n.getY(),h=o.getX(),l=o.getY(),d=t.CORR;return i<this.width/2?[new $t(h-d,l+d),new $t(s+d,u+d),new $t(c-d,f-d),new $t(i+d,a-d)]:[new $t(h+d,l+d),new $t(s+d,u-d),new $t(c-d,f+d),new $t(i-d,a-d)]},t.prototype.containsBlackPoint=function(t,e,r,n){var o=this.image;if(n){for(var i=t;i<=e;i++)if(o.get(i,r))return!0}else for(var a=t;a<=e;a++)if(o.get(r,a))return!0;return!1},t.INIT_SIZE=10,t.CORR=1,t}(),oe=function(){function t(){}return t.checkAndNudgePoints=function(t,e){for(var r=t.getWidth(),n=t.getHeight(),o=!0,i=0;i<e.length&&o;i+=2){var a=Math.floor(e[i]),s=Math.floor(e[i+1]);if(a<-1||a>r||s<-1||s>n)throw new vt;o=!1,-1===a?(e[i]=0,o=!0):a===r&&(e[i]=r-1,o=!0),-1===s?(e[i+1]=0,o=!0):s===n&&(e[i+1]=n-1,o=!0)}for(o=!0,i=e.length-2;i>=0&&o;i-=2){if(a=Math.floor(e[i]),s=Math.floor(e[i+1]),a<-1||a>r||s<-1||s>n)throw new vt;o=!1,-1===a?(e[i]=0,o=!0):a===r&&(e[i]=r-1,o=!0),-1===s?(e[i+1]=0,o=!0):s===n&&(e[i+1]=n-1,o=!0)}},t}(),ie=function(){function t(t,e,r,n,o,i,a,s,u){this.a11=t,this.a21=e,this.a31=r,this.a12=n,this.a22=o,this.a32=i,this.a13=a,this.a23=s,this.a33=u}return t.quadrilateralToQuadrilateral=function(e,r,n,o,i,a,s,u,c,f,h,l,d,p,g,y){var w=t.quadrilateralToSquare(e,r,n,o,i,a,s,u);return t.squareToQuadrilateral(c,f,h,l,d,p,g,y).times(w)},t.prototype.transformPoints=function(t){for(var e=t.length,r=this.a11,n=this.a12,o=this.a13,i=this.a21,a=this.a22,s=this.a23,u=this.a31,c=this.a32,f=this.a33,h=0;h<e;h+=2){var l=t[h],d=t[h+1],p=o*l+s*d+f;t[h]=(r*l+i*d+u)/p,t[h+1]=(n*l+a*d+c)/p}},t.prototype.transformPointsWithValues=function(t,e){for(var r=this.a11,n=this.a12,o=this.a13,i=this.a21,a=this.a22,s=this.a23,u=this.a31,c=this.a32,f=this.a33,h=t.length,l=0;l<h;l++){var d=t[l],p=e[l],g=o*d+s*p+f;t[l]=(r*d+i*p+u)/g,e[l]=(n*d+a*p+c)/g}},t.squareToQuadrilateral=function(e,r,n,o,i,a,s,u){var c=e-n+i-s,f=r-o+a-u;if(0===c&&0===f)return new t(n-e,i-n,e,o-r,a-o,r,0,0,1);var h=n-i,l=s-i,d=o-a,p=u-a,g=h*p-l*d,y=(c*p-l*f)/g,w=(h*f-c*d)/g;return new t(n-e+y*n,s-e+w*s,e,o-r+y*o,u-r+w*u,r,y,w,1)},t.quadrilateralToSquare=function(e,r,n,o,i,a,s,u){return t.squareToQuadrilateral(e,r,n,o,i,a,s,u).buildAdjoint()},t.prototype.buildAdjoint=function(){return new t(this.a22*this.a33-this.a23*this.a32,this.a23*this.a31-this.a21*this.a33,this.a21*this.a32-this.a22*this.a31,this.a13*this.a32-this.a12*this.a33,this.a11*this.a33-this.a13*this.a31,this.a12*this.a31-this.a11*this.a32,this.a12*this.a23-this.a13*this.a22,this.a13*this.a21-this.a11*this.a23,this.a11*this.a22-this.a12*this.a21)},t.prototype.times=function(e){return new t(this.a11*e.a11+this.a21*e.a12+this.a31*e.a13,this.a11*e.a21+this.a21*e.a22+this.a31*e.a23,this.a11*e.a31+this.a21*e.a32+this.a31*e.a33,this.a12*e.a11+this.a22*e.a12+this.a32*e.a13,this.a12*e.a21+this.a22*e.a22+this.a32*e.a23,this.a12*e.a31+this.a22*e.a32+this.a32*e.a33,this.a13*e.a11+this.a23*e.a12+this.a33*e.a13,this.a13*e.a21+this.a23*e.a22+this.a33*e.a23,this.a13*e.a31+this.a23*e.a32+this.a33*e.a33)},t}(),ae=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),se=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ae(e,t),e.prototype.sampleGrid=function(t,e,r,n,o,i,a,s,u,c,f,h,l,d,p,g,y,w,v){var _=ie.quadrilateralToQuadrilateral(n,o,i,a,s,u,c,f,h,l,d,p,g,y,w,v);return this.sampleGridWithTransform(t,e,r,_)},e.prototype.sampleGridWithTransform=function(t,e,r,n){if(e<=0||r<=0)throw new vt;for(var o=new yt(e,r),i=new Float32Array(2*e),a=0;a<r;a++){for(var s=i.length,u=a+.5,c=0;c<s;c+=2)i[c]=c/2+.5,i[c+1]=u;n.transformPoints(i),oe.checkAndNudgePoints(t,i);try{for(c=0;c<s;c+=2)t.get(Math.floor(i[c]),Math.floor(i[c+1]))&&o.set(c/2,a)}catch(f){throw new vt}}return o},e}(oe),ue=function(){function t(){}return t.setGridSampler=function(e){t.gridSampler=e},t.getInstance=function(){return t.gridSampler},t.gridSampler=new se,t}(),ce=function(){function t(t,e){this.x=t,this.y=e}return t.prototype.toResultPoint=function(){return new $t(this.getX(),this.getY())},t.prototype.getX=function(){return this.x},t.prototype.getY=function(){return this.y},t}(),fe=function(){function t(t){this.EXPECTED_CORNER_BITS=new Int32Array([3808,476,2107,1799]),this.image=t}return t.prototype.detect=function(){return this.detectMirror(!1)},t.prototype.detectMirror=function(t){var e=this.getMatrixCenter(),r=this.getBullsEyeCorners(e);if(t){var n=r[0];r[0]=r[2],r[2]=n}this.extractParameters(r);var o=this.sampleGrid(this.image,r[this.shift%4],r[(this.shift+1)%4],r[(this.shift+2)%4],r[(this.shift+3)%4]),i=this.getMatrixCornerPoints(r);return new re(o,i,this.compact,this.nbDataBlocks,this.nbLayers)},t.prototype.extractParameters=function(t){if(!(this.isValidPoint(t[0])&&this.isValidPoint(t[1])&&this.isValidPoint(t[2])&&this.isValidPoint(t[3])))throw new vt;var e=2*this.nbCenterLayers,r=new Int32Array([this.sampleLine(t[0],t[1],e),this.sampleLine(t[1],t[2],e),this.sampleLine(t[2],t[3],e),this.sampleLine(t[3],t[0],e)]);this.shift=this.getRotation(r,e);for(var n=0,o=0;o<4;o++){var i=r[(this.shift+o)%4];this.compact?(n<<=7,n+=i>>1&127):(n<<=10,n+=(i>>2&992)+(i>>1&31))}var a=this.getCorrectedParameterData(n,this.compact);this.compact?(this.nbLayers=1+(a>>6),this.nbDataBlocks=1+(63&a)):(this.nbLayers=1+(a>>11),this.nbDataBlocks=1+(2047&a))},t.prototype.getRotation=function(t,e){var r=0;t.forEach(function(t,n,o){r=(t>>e-2<<1)+(1&t)+(r<<3)}),r=((1&r)<<11)+(r>>1);for(var n=0;n<4;n++)if(nt.bitCount(r^this.EXPECTED_CORNER_BITS[n])<=2)return n;throw new vt},t.prototype.getCorrectedParameterData=function(t,e){var r,n;e?(r=7,n=2):(r=10,n=4);for(var o=r-n,i=new Int32Array(r),a=r-1;a>=0;--a)i[a]=15&t,t>>=4;try{new Kt(Wt.AZTEC_PARAM).decode(i,o)}catch(u){throw new vt}var s=0;for(a=0;a<n;a++)s=(s<<4)+i[a];return s},t.prototype.getBullsEyeCorners=function(t){var e=t,r=t,n=t,o=t,i=!0;for(this.nbCenterLayers=1;this.nbCenterLayers<9;this.nbCenterLayers++){var a=this.getFirstDifferent(e,i,1,-1),s=this.getFirstDifferent(r,i,1,1),u=this.getFirstDifferent(n,i,-1,1),c=this.getFirstDifferent(o,i,-1,-1);if(this.nbCenterLayers>2){var f=this.distancePoint(c,a)*this.nbCenterLayers/(this.distancePoint(o,e)*(this.nbCenterLayers+2));if(f<.75||f>1.25||!this.isWhiteOrBlackRectangle(a,s,u,c))break}e=a,r=s,n=u,o=c,i=!i}if(5!==this.nbCenterLayers&&7!==this.nbCenterLayers)throw new vt;this.compact=5===this.nbCenterLayers;var h=new $t(e.getX()+.5,e.getY()-.5),l=new $t(r.getX()+.5,r.getY()+.5),d=new $t(n.getX()-.5,n.getY()+.5),p=new $t(o.getX()-.5,o.getY()-.5);return this.expandSquare([h,l,d,p],2*this.nbCenterLayers-3,2*this.nbCenterLayers)},t.prototype.getMatrixCenter=function(){var t,e,r,n;try{t=(u=new ne(this.image).detect())[0],e=u[1],r=u[2],n=u[3]}catch(c){var o=this.image.getWidth()/2,i=this.image.getHeight()/2;t=this.getFirstDifferent(new ce(o+7,i-7),!1,1,-1).toResultPoint(),e=this.getFirstDifferent(new ce(o+7,i+7),!1,1,1).toResultPoint(),r=this.getFirstDifferent(new ce(o-7,i+7),!1,-1,1).toResultPoint(),n=this.getFirstDifferent(new ce(o-7,i-7),!1,-1,-1).toResultPoint()}var a=Qt.round((t.getX()+n.getX()+e.getX()+r.getX())/4),s=Qt.round((t.getY()+n.getY()+e.getY()+r.getY())/4);try{var u;t=(u=new ne(this.image,15,a,s).detect())[0],e=u[1],r=u[2],n=u[3]}catch(c){t=this.getFirstDifferent(new ce(a+7,s-7),!1,1,-1).toResultPoint(),e=this.getFirstDifferent(new ce(a+7,s+7),!1,1,1).toResultPoint(),r=this.getFirstDifferent(new ce(a-7,s+7),!1,-1,1).toResultPoint(),n=this.getFirstDifferent(new ce(a-7,s-7),!1,-1,-1).toResultPoint()}return a=Qt.round((t.getX()+n.getX()+e.getX()+r.getX())/4),s=Qt.round((t.getY()+n.getY()+e.getY()+r.getY())/4),new ce(a,s)},t.prototype.getMatrixCornerPoints=function(t){return this.expandSquare(t,2*this.nbCenterLayers,this.getDimension())},t.prototype.sampleGrid=function(t,e,r,n,o){var i=ue.getInstance(),a=this.getDimension(),s=a/2-this.nbCenterLayers,u=a/2+this.nbCenterLayers;return i.sampleGrid(t,a,a,s,s,u,s,u,u,s,u,e.getX(),e.getY(),r.getX(),r.getY(),n.getX(),n.getY(),o.getX(),o.getY())},t.prototype.sampleLine=function(t,e,r){for(var n=0,o=this.distanceResultPoint(t,e),i=o/r,a=t.getX(),s=t.getY(),u=i*(e.getX()-t.getX())/o,c=i*(e.getY()-t.getY())/o,f=0;f<r;f++)this.image.get(Qt.round(a+f*u),Qt.round(s+f*c))&&(n|=1<<r-f-1);return n},t.prototype.isWhiteOrBlackRectangle=function(t,e,r,n){t=new ce(t.getX()-3,t.getY()+3),e=new ce(e.getX()-3,e.getY()-3),r=new ce(r.getX()+3,r.getY()-3),n=new ce(n.getX()+3,n.getY()+3);var o=this.getColor(n,t);if(0===o)return!1;var i=this.getColor(t,e);return i===o&&(i=this.getColor(e,r))===o&&(i=this.getColor(r,n))===o},t.prototype.getColor=function(t,e){for(var r=this.distancePoint(t,e),n=(e.getX()-t.getX())/r,o=(e.getY()-t.getY())/r,i=0,a=t.getX(),s=t.getY(),u=this.image.get(t.getX(),t.getY()),c=Math.ceil(r),f=0;f<c;f++)a+=n,s+=o,this.image.get(Qt.round(a),Qt.round(s))!==u&&i++;var h=i/r;return h>.1&&h<.9?0:h<=.1===u?1:-1},t.prototype.getFirstDifferent=function(t,e,r,n){for(var o=t.getX()+r,i=t.getY()+n;this.isValid(o,i)&&this.image.get(o,i)===e;)o+=r,i+=n;for(o-=r,i-=n;this.isValid(o,i)&&this.image.get(o,i)===e;)o+=r;for(o-=r;this.isValid(o,i)&&this.image.get(o,i)===e;)i+=n;return new ce(o,i-=n)},t.prototype.expandSquare=function(t,e,r){var n=r/(2*e),o=t[0].getX()-t[2].getX(),i=t[0].getY()-t[2].getY(),a=(t[0].getX()+t[2].getX())/2,s=(t[0].getY()+t[2].getY())/2,u=new $t(a+n*o,s+n*i),c=new $t(a-n*o,s-n*i);return o=t[1].getX()-t[3].getX(),i=t[1].getY()-t[3].getY(),a=(t[1].getX()+t[3].getX())/2,s=(t[1].getY()+t[3].getY())/2,[u,new $t(a+n*o,s+n*i),c,new $t(a-n*o,s-n*i)]},t.prototype.isValid=function(t,e){return t>=0&&t<this.image.getWidth()&&e>0&&e<this.image.getHeight()},t.prototype.isValidPoint=function(t){var e=Qt.round(t.getX()),r=Qt.round(t.getY());return this.isValid(e,r)},t.prototype.distancePoint=function(t,e){return Qt.distance(t.getX(),t.getY(),e.getX(),e.getY())},t.prototype.distanceResultPoint=function(t,e){return Qt.distance(t.getX(),t.getY(),e.getX(),e.getY())},t.prototype.getDimension=function(){return this.compact?4*this.nbLayers+11:this.nbLayers<=4?4*this.nbLayers+15:4*this.nbLayers+2*(nt.truncDivision(this.nbLayers-4,8)+1)+15},t}(),he=function(){function t(){}return t.prototype.decode=function(t,e){void 0===e&&(e=null);var r=null,n=new fe(t.getBlackMatrix()),o=null,i=null;try{o=(a=n.detectMirror(!1)).getPoints(),this.reportFoundResultPoints(e,o),i=(new qt).decode(a)}catch(f){r=f}if(null==i)try{var a;o=(a=n.detectMirror(!0)).getPoints(),this.reportFoundResultPoints(e,o),i=(new qt).decode(a)}catch(f){if(null!=r)throw r;throw f}var s=new Pt(i.getText(),i.getRawBytes(),i.getNumBits(),o,Bt.AZTEC,q.currentTimeMillis()),u=i.getByteSegments();null!=u&&s.putMetadata(Ft.BYTE_SEGMENTS,u);var c=i.getECLevel();return null!=c&&s.putMetadata(Ft.ERROR_CORRECTION_LEVEL,c),s},t.prototype.reportFoundResultPoints=function(t,e){if(null!=t){var r=t.get(k.NEED_RESULT_POINT_CALLBACK);null!=r&&e.forEach(function(t,e,n){r.foundPossibleResultPoint(t)})}},t.prototype.reset=function(){},t}(),le=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();!function(t){function e(e){return void 0===e&&(e=500),t.call(this,new he,e)||this}le(e,t)}(Mt);var de=function(){function t(){}return t.prototype.decode=function(t,e){try{return this.doDecode(t,e)}catch(c){if(e&&!0===e.get(k.TRY_HARDER)&&t.isRotateSupported()){var r=t.rotateCounterClockwise(),n=this.doDecode(r,e),o=n.getResultMetadata(),i=270;null!==o&&!0===o.get(Ft.ORIENTATION)&&(i+=o.get(Ft.ORIENTATION)%360),n.putMetadata(Ft.ORIENTATION,i);var a=n.getResultPoints();if(null!==a)for(var s=r.getHeight(),u=0;u<a.length;u++)a[u]=new $t(s-a[u].getY()-1,a[u].getX());return n}throw new vt}},t.prototype.reset=function(){},t.prototype.doDecode=function(t,e){var r,n=t.getWidth(),o=t.getHeight(),i=new ot(n),a=e&&!0===e.get(k.TRY_HARDER),s=Math.max(1,o>>(a?8:5));r=a?o:15;for(var u=Math.trunc(o/2),c=0;c<r;c++){var f=Math.trunc((c+1)/2),h=u+s*(1&c?-f:f);if(h<0||h>=o)break;try{i=t.getBlackRow(h,i)}catch(y){continue}for(var l=function(t){if(1===t&&(i.reverse(),e&&!0===e.get(k.NEED_RESULT_POINT_CALLBACK))){var r=new Map;e.forEach(function(t,e){return r.set(e,t)}),r.delete(k.NEED_RESULT_POINT_CALLBACK),e=r}try{var o=d.decodeRow(h,i,e);if(1===t){o.putMetadata(Ft.ORIENTATION,180);var a=o.getResultPoints();null!==a&&(a[0]=new $t(n-a[0].getX()-1,a[0].getY()),a[1]=new $t(n-a[1].getX()-1,a[1].getY()))}return{value:o}}catch(s){}},d=this,p=0;p<2;p++){var g=l(p);if("object"==typeof g)return g.value}}throw new vt},t.recordPattern=function(t,e,r){for(var n=r.length,o=0;o<n;o++)r[o]=0;var i=t.getSize();if(e>=i)throw new vt;for(var a=!t.get(e),s=0,u=e;u<i;){if(t.get(u)!==a)r[s]++;else{if(++s===n)break;r[s]=1,a=!a}u++}if(s!==n&&(s!==n-1||u!==i))throw new vt},t.recordPatternInReverse=function(e,r,n){for(var o=n.length,i=e.get(r);r>0&&o>=0;)e.get(--r)!==i&&(o--,i=!i);if(o>=0)throw new vt;t.recordPattern(e,r+1,n)},t.patternMatchVariance=function(t,e,r){for(var n=t.length,o=0,i=0,a=0;a<n;a++)o+=t[a],i+=e[a];if(o<i)return Number.POSITIVE_INFINITY;var s=o/i;r*=s;for(var u=0,c=0;c<n;c++){var f=t[c],h=e[c]*s,l=f>h?f-h:h-f;if(l>r)return Number.POSITIVE_INFINITY;u+=l}return u/o},t}(),pe=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),ge=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return pe(e,t),e.findStartPattern=function(t){for(var r=t.getSize(),n=t.getNextSet(0),o=0,i=Int32Array.from([0,0,0,0,0,0]),a=n,s=!1,u=n;u<r;u++)if(t.get(u)!==s)i[o]++;else{if(5===o){for(var c=e.MAX_AVG_VARIANCE,f=-1,h=e.CODE_START_A;h<=e.CODE_START_C;h++){var l=de.patternMatchVariance(i,e.CODE_PATTERNS[h],e.MAX_INDIVIDUAL_VARIANCE);l<c&&(c=l,f=h)}if(f>=0&&t.isRange(Math.max(0,a-(u-a)/2),a,!1))return Int32Array.from([a,u,f]);a+=i[0]+i[1],(i=i.slice(2,i.length-1))[o-1]=0,i[o]=0,o--}else o++;i[o]=1,s=!s}throw new vt},e.decodeCode=function(t,r,n){de.recordPattern(t,n,r);for(var o=e.MAX_AVG_VARIANCE,i=-1,a=0;a<e.CODE_PATTERNS.length;a++){var s=e.CODE_PATTERNS[a],u=this.patternMatchVariance(r,s,e.MAX_INDIVIDUAL_VARIANCE);u<o&&(o=u,i=a)}if(i>=0)return i;throw new vt},e.prototype.decodeRow=function(t,r,n){var o,i=n&&!0===n.get(k.ASSUME_GS1),a=e.findStartPattern(r),s=a[2],u=0,c=new Uint8Array(20);switch(c[u++]=s,s){case e.CODE_START_A:o=e.CODE_CODE_A;break;case e.CODE_START_B:o=e.CODE_CODE_B;break;case e.CODE_START_C:o=e.CODE_CODE_C;break;default:throw new st}for(var f=!1,h=!1,l="",d=a[0],p=a[1],g=Int32Array.from([0,0,0,0,0,0]),y=0,w=0,v=s,_=0,m=!0,C=!1,A=!1;!f;){var E=h;switch(h=!1,y=w,w=e.decodeCode(r,g,p),c[u++]=w,w!==e.CODE_STOP&&(m=!0),w!==e.CODE_STOP&&(v+=++_*w),d=p,p+=g.reduce(function(t,e){return t+e},0),w){case e.CODE_START_A:case e.CODE_START_B:case e.CODE_START_C:throw new st}switch(o){case e.CODE_CODE_A:if(w<64)l+=A===C?String.fromCharCode(" ".charCodeAt(0)+w):String.fromCharCode(" ".charCodeAt(0)+w+128),A=!1;else if(w<96)l+=A===C?String.fromCharCode(w-64):String.fromCharCode(w+64),A=!1;else switch(w!==e.CODE_STOP&&(m=!1),w){case e.CODE_FNC_1:i&&(0===l.length?l+="]C1":l+=String.fromCharCode(29));break;case e.CODE_FNC_2:case e.CODE_FNC_3:break;case e.CODE_FNC_4_A:!C&&A?(C=!0,A=!1):C&&A?(C=!1,A=!1):A=!0;break;case e.CODE_SHIFT:h=!0,o=e.CODE_CODE_B;break;case e.CODE_CODE_B:o=e.CODE_CODE_B;break;case e.CODE_CODE_C:o=e.CODE_CODE_C;break;case e.CODE_STOP:f=!0}break;case e.CODE_CODE_B:if(w<96)l+=A===C?String.fromCharCode(" ".charCodeAt(0)+w):String.fromCharCode(" ".charCodeAt(0)+w+128),A=!1;else switch(w!==e.CODE_STOP&&(m=!1),w){case e.CODE_FNC_1:i&&(0===l.length?l+="]C1":l+=String.fromCharCode(29));break;case e.CODE_FNC_2:case e.CODE_FNC_3:break;case e.CODE_FNC_4_B:!C&&A?(C=!0,A=!1):C&&A?(C=!1,A=!1):A=!0;break;case e.CODE_SHIFT:h=!0,o=e.CODE_CODE_A;break;case e.CODE_CODE_A:o=e.CODE_CODE_A;break;case e.CODE_CODE_C:o=e.CODE_CODE_C;break;case e.CODE_STOP:f=!0}break;case e.CODE_CODE_C:if(w<100)w<10&&(l+="0"),l+=w;else switch(w!==e.CODE_STOP&&(m=!1),w){case e.CODE_FNC_1:i&&(0===l.length?l+="]C1":l+=String.fromCharCode(29));break;case e.CODE_CODE_A:o=e.CODE_CODE_A;break;case e.CODE_CODE_B:o=e.CODE_CODE_B;break;case e.CODE_STOP:f=!0}}E&&(o=o===e.CODE_CODE_A?e.CODE_CODE_B:e.CODE_CODE_A)}var I=p-d;if(p=r.getNextUnset(p),!r.isRange(p,Math.min(r.getSize(),p+(p-d)/2),!1))throw new vt;if((v-=_*y)%103!==y)throw new Z;var S=l.length;if(0===S)throw new vt;S>0&&m&&(l=o===e.CODE_CODE_C?l.substring(0,S-2):l.substring(0,S-1));for(var b=(a[1]+a[0])/2,T=d+I/2,O=c.length,R=new Uint8Array(O),N=0;N<O;N++)R[N]=c[N];var D=[new $t(b,t),new $t(T,t)];return new Pt(l,R,0,D,Bt.CODE_128,(new Date).getTime())},e.CODE_PATTERNS=[Int32Array.from([2,1,2,2,2,2]),Int32Array.from([2,2,2,1,2,2]),Int32Array.from([2,2,2,2,2,1]),Int32Array.from([1,2,1,2,2,3]),Int32Array.from([1,2,1,3,2,2]),Int32Array.from([1,3,1,2,2,2]),Int32Array.from([1,2,2,2,1,3]),Int32Array.from([1,2,2,3,1,2]),Int32Array.from([1,3,2,2,1,2]),Int32Array.from([2,2,1,2,1,3]),Int32Array.from([2,2,1,3,1,2]),Int32Array.from([2,3,1,2,1,2]),Int32Array.from([1,1,2,2,3,2]),Int32Array.from([1,2,2,1,3,2]),Int32Array.from([1,2,2,2,3,1]),Int32Array.from([1,1,3,2,2,2]),Int32Array.from([1,2,3,1,2,2]),Int32Array.from([1,2,3,2,2,1]),Int32Array.from([2,2,3,2,1,1]),Int32Array.from([2,2,1,1,3,2]),Int32Array.from([2,2,1,2,3,1]),Int32Array.from([2,1,3,2,1,2]),Int32Array.from([2,2,3,1,1,2]),Int32Array.from([3,1,2,1,3,1]),Int32Array.from([3,1,1,2,2,2]),Int32Array.from([3,2,1,1,2,2]),Int32Array.from([3,2,1,2,2,1]),Int32Array.from([3,1,2,2,1,2]),Int32Array.from([3,2,2,1,1,2]),Int32Array.from([3,2,2,2,1,1]),Int32Array.from([2,1,2,1,2,3]),Int32Array.from([2,1,2,3,2,1]),Int32Array.from([2,3,2,1,2,1]),Int32Array.from([1,1,1,3,2,3]),Int32Array.from([1,3,1,1,2,3]),Int32Array.from([1,3,1,3,2,1]),Int32Array.from([1,1,2,3,1,3]),Int32Array.from([1,3,2,1,1,3]),Int32Array.from([1,3,2,3,1,1]),Int32Array.from([2,1,1,3,1,3]),Int32Array.from([2,3,1,1,1,3]),Int32Array.from([2,3,1,3,1,1]),Int32Array.from([1,1,2,1,3,3]),Int32Array.from([1,1,2,3,3,1]),Int32Array.from([1,3,2,1,3,1]),Int32Array.from([1,1,3,1,2,3]),Int32Array.from([1,1,3,3,2,1]),Int32Array.from([1,3,3,1,2,1]),Int32Array.from([3,1,3,1,2,1]),Int32Array.from([2,1,1,3,3,1]),Int32Array.from([2,3,1,1,3,1]),Int32Array.from([2,1,3,1,1,3]),Int32Array.from([2,1,3,3,1,1]),Int32Array.from([2,1,3,1,3,1]),Int32Array.from([3,1,1,1,2,3]),Int32Array.from([3,1,1,3,2,1]),Int32Array.from([3,3,1,1,2,1]),Int32Array.from([3,1,2,1,1,3]),Int32Array.from([3,1,2,3,1,1]),Int32Array.from([3,3,2,1,1,1]),Int32Array.from([3,1,4,1,1,1]),Int32Array.from([2,2,1,4,1,1]),Int32Array.from([4,3,1,1,1,1]),Int32Array.from([1,1,1,2,2,4]),Int32Array.from([1,1,1,4,2,2]),Int32Array.from([1,2,1,1,2,4]),Int32Array.from([1,2,1,4,2,1]),Int32Array.from([1,4,1,1,2,2]),Int32Array.from([1,4,1,2,2,1]),Int32Array.from([1,1,2,2,1,4]),Int32Array.from([1,1,2,4,1,2]),Int32Array.from([1,2,2,1,1,4]),Int32Array.from([1,2,2,4,1,1]),Int32Array.from([1,4,2,1,1,2]),Int32Array.from([1,4,2,2,1,1]),Int32Array.from([2,4,1,2,1,1]),Int32Array.from([2,2,1,1,1,4]),Int32Array.from([4,1,3,1,1,1]),Int32Array.from([2,4,1,1,1,2]),Int32Array.from([1,3,4,1,1,1]),Int32Array.from([1,1,1,2,4,2]),Int32Array.from([1,2,1,1,4,2]),Int32Array.from([1,2,1,2,4,1]),Int32Array.from([1,1,4,2,1,2]),Int32Array.from([1,2,4,1,1,2]),Int32Array.from([1,2,4,2,1,1]),Int32Array.from([4,1,1,2,1,2]),Int32Array.from([4,2,1,1,1,2]),Int32Array.from([4,2,1,2,1,1]),Int32Array.from([2,1,2,1,4,1]),Int32Array.from([2,1,4,1,2,1]),Int32Array.from([4,1,2,1,2,1]),Int32Array.from([1,1,1,1,4,3]),Int32Array.from([1,1,1,3,4,1]),Int32Array.from([1,3,1,1,4,1]),Int32Array.from([1,1,4,1,1,3]),Int32Array.from([1,1,4,3,1,1]),Int32Array.from([4,1,1,1,1,3]),Int32Array.from([4,1,1,3,1,1]),Int32Array.from([1,1,3,1,4,1]),Int32Array.from([1,1,4,1,3,1]),Int32Array.from([3,1,1,1,4,1]),Int32Array.from([4,1,1,1,3,1]),Int32Array.from([2,1,1,4,1,2]),Int32Array.from([2,1,1,2,1,4]),Int32Array.from([2,1,1,2,3,2]),Int32Array.from([2,3,3,1,1,1,2])],e.MAX_AVG_VARIANCE=.25,e.MAX_INDIVIDUAL_VARIANCE=.7,e.CODE_SHIFT=98,e.CODE_CODE_C=99,e.CODE_CODE_B=100,e.CODE_CODE_A=101,e.CODE_FNC_1=102,e.CODE_FNC_2=97,e.CODE_FNC_3=96,e.CODE_FNC_4_A=101,e.CODE_FNC_4_B=100,e.CODE_START_A=103,e.CODE_START_B=104,e.CODE_START_C=105,e.CODE_STOP=106,e}(de),ye=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),we=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},ve=function(t){function e(e,r){void 0===e&&(e=!1),void 0===r&&(r=!1);var n=t.call(this)||this;return n.usingCheckDigit=e,n.extendedMode=r,n.decodeRowResult="",n.counters=new Int32Array(9),n}return ye(e,t),e.prototype.decodeRow=function(t,r,n){var o,i,a,s,u=this.counters;u.fill(0),this.decodeRowResult="";var c,f,h=e.findAsteriskPattern(r,u),l=r.getNextSet(h[1]),d=r.getSize();do{e.recordPattern(r,l,u);var p=e.toNarrowWidePattern(u);if(p<0)throw new vt;c=e.patternToChar(p),this.decodeRowResult+=c,f=l;try{for(var g=(o=void 0,we(u)),y=g.next();!y.done;y=g.next())l+=y.value}catch(b){o={error:b}}finally{try{y&&!y.done&&(i=g.return)&&i.call(g)}finally{if(o)throw o.error}}l=r.getNextSet(l)}while("*"!==c);this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-1);var w,v=0;try{for(var _=we(u),m=_.next();!m.done;m=_.next())v+=m.value}catch(T){a={error:T}}finally{try{m&&!m.done&&(s=_.return)&&s.call(_)}finally{if(a)throw a.error}}if(l!==d&&2*(l-f-v)<v)throw new vt;if(this.usingCheckDigit){for(var C=this.decodeRowResult.length-1,A=0,E=0;E<C;E++)A+=e.ALPHABET_STRING.indexOf(this.decodeRowResult.charAt(E));if(this.decodeRowResult.charAt(C)!==e.ALPHABET_STRING.charAt(A%43))throw new Z;this.decodeRowResult=this.decodeRowResult.substring(0,C)}if(0===this.decodeRowResult.length)throw new vt;w=this.extendedMode?e.decodeExtended(this.decodeRowResult):this.decodeRowResult;var I=(h[1]+h[0])/2,S=f+v/2;return new Pt(w,null,0,[new $t(I,t),new $t(S,t)],Bt.CODE_39,(new Date).getTime())},e.findAsteriskPattern=function(t,r){for(var n=t.getSize(),o=t.getNextSet(0),i=0,a=o,s=!1,u=r.length,c=o;c<n;c++)if(t.get(c)!==s)r[i]++;else{if(i===u-1){if(this.toNarrowWidePattern(r)===e.ASTERISK_ENCODING&&t.isRange(Math.max(0,a-Math.floor((c-a)/2)),a,!1))return[a,c];a+=r[0]+r[1],r.copyWithin(0,2,2+i-1),r[i-1]=0,r[i]=0,i--}else i++;r[i]=1,s=!s}throw new vt},e.toNarrowWidePattern=function(t){var e,r,n,o=t.length,i=0;do{var a=2147483647;try{for(var s=(e=void 0,we(t)),u=s.next();!u.done;u=s.next())(l=u.value)<a&&l>i&&(a=l)}catch(d){e={error:d}}finally{try{u&&!u.done&&(r=s.return)&&r.call(s)}finally{if(e)throw e.error}}i=a,n=0;for(var c=0,f=0,h=0;h<o;h++)(l=t[h])>i&&(f|=1<<o-1-h,n++,c+=l);if(3===n){for(h=0;h<o&&n>0;h++){var l;if((l=t[h])>i&&(n--,2*l>=c))return-1}return f}}while(n>3);return-1},e.patternToChar=function(t){for(var r=0;r<e.CHARACTER_ENCODINGS.length;r++)if(e.CHARACTER_ENCODINGS[r]===t)return e.ALPHABET_STRING.charAt(r);if(t===e.ASTERISK_ENCODING)return"*";throw new vt},e.decodeExtended=function(t){for(var e=t.length,r="",n=0;n<e;n++){var o=t.charAt(n);if("+"===o||"$"===o||"%"===o||"/"===o){var i=t.charAt(n+1),a="\0";switch(o){case"+":if(!(i>="A"&&i<="Z"))throw new st;a=String.fromCharCode(i.charCodeAt(0)+32);break;case"$":if(!(i>="A"&&i<="Z"))throw new st;a=String.fromCharCode(i.charCodeAt(0)-64);break;case"%":if(i>="A"&&i<="E")a=String.fromCharCode(i.charCodeAt(0)-38);else if(i>="F"&&i<="J")a=String.fromCharCode(i.charCodeAt(0)-11);else if(i>="K"&&i<="O")a=String.fromCharCode(i.charCodeAt(0)+16);else if(i>="P"&&i<="T")a=String.fromCharCode(i.charCodeAt(0)+43);else if("U"===i)a="\0";else if("V"===i)a="@";else if("W"===i)a="`";else{if("X"!==i&&"Y"!==i&&"Z"!==i)throw new st;a=""}break;case"/":if(i>="A"&&i<="O")a=String.fromCharCode(i.charCodeAt(0)-32);else{if("Z"!==i)throw new st;a=":"}}r+=a,n++}else r+=o}return r},e.ALPHABET_STRING="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%",e.CHARACTER_ENCODINGS=[52,289,97,352,49,304,112,37,292,100,265,73,328,25,280,88,13,268,76,28,259,67,322,19,274,82,7,262,70,22,385,193,448,145,400,208,133,388,196,168,162,138,42],e.ASTERISK_ENCODING=148,e}(de),_e=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),me=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Ce=function(t){function e(){var e=t.call(this)||this;return e.decodeRowResult="",e.counters=new Int32Array(6),e}return _e(e,t),e.prototype.decodeRow=function(t,r,n){var o,i,a,s,u,c,f=this.findAsteriskPattern(r),h=r.getNextSet(f[1]),l=r.getSize(),d=this.counters;d.fill(0),this.decodeRowResult="";do{e.recordPattern(r,h,d);var p=this.toPattern(d);if(p<0)throw new vt;u=this.patternToChar(p),this.decodeRowResult+=u,c=h;try{for(var g=(o=void 0,me(d)),y=g.next();!y.done;y=g.next())h+=y.value}catch(E){o={error:E}}finally{try{y&&!y.done&&(i=g.return)&&i.call(g)}finally{if(o)throw o.error}}h=r.getNextSet(h)}while("*"!==u);this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-1);var w=0;try{for(var v=me(d),_=v.next();!_.done;_=v.next())w+=_.value}catch(I){a={error:I}}finally{try{_&&!_.done&&(s=v.return)&&s.call(v)}finally{if(a)throw a.error}}if(h===l||!r.get(h))throw new vt;if(this.decodeRowResult.length<2)throw new vt;this.checkChecksums(this.decodeRowResult),this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-2);var m=this.decodeExtended(this.decodeRowResult),C=(f[1]+f[0])/2,A=c+w/2;return new Pt(m,null,0,[new $t(C,t),new $t(A,t)],Bt.CODE_93,(new Date).getTime())},e.prototype.findAsteriskPattern=function(t){var r=t.getSize(),n=t.getNextSet(0);this.counters.fill(0);for(var o=this.counters,i=n,a=!1,s=o.length,u=0,c=n;c<r;c++)if(t.get(c)!==a)o[u]++;else{if(u===s-1){if(this.toPattern(o)===e.ASTERISK_ENCODING)return new Int32Array([i,c]);i+=o[0]+o[1],o.copyWithin(0,2,2+u-1),o[u-1]=0,o[u]=0,u--}else u++;o[u]=1,a=!a}throw new vt},e.prototype.toPattern=function(t){var e,r,n=0;try{for(var o=me(t),i=o.next();!i.done;i=o.next())n+=i.value}catch(h){e={error:h}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}for(var a=0,s=t.length,u=0;u<s;u++){var c=Math.round(9*t[u]/n);if(c<1||c>4)return-1;if(1&u)a<<=c;else for(var f=0;f<c;f++)a=a<<1|1}return a},e.prototype.patternToChar=function(t){for(var r=0;r<e.CHARACTER_ENCODINGS.length;r++)if(e.CHARACTER_ENCODINGS[r]===t)return e.ALPHABET_STRING.charAt(r);throw new vt},e.prototype.decodeExtended=function(t){for(var e=t.length,r="",n=0;n<e;n++){var o=t.charAt(n);if(o>="a"&&o<="d"){if(n>=e-1)throw new st;var i=t.charAt(n+1),a="\0";switch(o){case"d":if(!(i>="A"&&i<="Z"))throw new st;a=String.fromCharCode(i.charCodeAt(0)+32);break;case"a":if(!(i>="A"&&i<="Z"))throw new st;a=String.fromCharCode(i.charCodeAt(0)-64);break;case"b":if(i>="A"&&i<="E")a=String.fromCharCode(i.charCodeAt(0)-38);else if(i>="F"&&i<="J")a=String.fromCharCode(i.charCodeAt(0)-11);else if(i>="K"&&i<="O")a=String.fromCharCode(i.charCodeAt(0)+16);else if(i>="P"&&i<="T")a=String.fromCharCode(i.charCodeAt(0)+43);else if("U"===i)a="\0";else if("V"===i)a="@";else if("W"===i)a="`";else{if(!(i>="X"&&i<="Z"))throw new st;a=String.fromCharCode(127)}break;case"c":if(i>="A"&&i<="O")a=String.fromCharCode(i.charCodeAt(0)-32);else{if("Z"!==i)throw new st;a=":"}}r+=a,n++}else r+=o}return r},e.prototype.checkChecksums=function(t){var e=t.length;this.checkOneChecksum(t,e-2,20),this.checkOneChecksum(t,e-1,15)},e.prototype.checkOneChecksum=function(t,r,n){for(var o=1,i=0,a=r-1;a>=0;a--)i+=o*e.ALPHABET_STRING.indexOf(t.charAt(a)),++o>n&&(o=1);if(t.charAt(r)!==e.ALPHABET_STRING[i%47])throw new Z},e.ALPHABET_STRING="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%abcd*",e.CHARACTER_ENCODINGS=[276,328,324,322,296,292,290,336,274,266,424,420,418,404,402,394,360,356,354,308,282,344,332,326,300,278,436,434,428,422,406,410,364,358,310,314,302,468,466,458,366,374,430,294,474,470,306,350],e.ASTERISK_ENCODING=e.CHARACTER_ENCODINGS[47],e}(de),Ae=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Ee=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Ie=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.narrowLineWidth=-1,e}return Ae(e,t),e.prototype.decodeRow=function(t,r,n){var o,i,a=this.decodeStart(r),s=this.decodeEnd(r),u=new gt;e.decodeMiddle(r,a[1],s[0],u);var c=u.toString(),f=null;null!=n&&(f=n.get(k.ALLOWED_LENGTHS)),null==f&&(f=e.DEFAULT_ALLOWED_LENGTHS);var h=c.length,l=!1,d=0;try{for(var p=Ee(f),g=p.next();!g.done;g=p.next()){var y=g.value;if(h===y){l=!0;break}y>d&&(d=y)}}catch(v){o={error:v}}finally{try{g&&!g.done&&(i=p.return)&&i.call(p)}finally{if(o)throw o.error}}if(!l&&h>d&&(l=!0),!l)throw new st;var w=[new $t(a[1],t),new $t(s[0],t)];return new Pt(c,null,0,w,Bt.ITF,(new Date).getTime())},e.decodeMiddle=function(t,r,n,o){var i=new Int32Array(10),a=new Int32Array(5),s=new Int32Array(5);for(i.fill(0),a.fill(0),s.fill(0);r<n;){de.recordPattern(t,r,i);for(var u=0;u<5;u++){var c=2*u;a[u]=i[c],s[u]=i[c+1]}var f=e.decodeDigit(a);o.append(f.toString()),f=this.decodeDigit(s),o.append(f.toString()),i.forEach(function(t){r+=t})}},e.prototype.decodeStart=function(t){var r=e.skipWhiteSpace(t),n=e.findGuardPattern(t,r,e.START_PATTERN);return this.narrowLineWidth=(n[1]-n[0])/4,this.validateQuietZone(t,n[0]),n},e.prototype.validateQuietZone=function(t,e){var r=10*this.narrowLineWidth;r=r<e?r:e;for(var n=e-1;r>0&&n>=0&&!t.get(n);n--)r--;if(0!==r)throw new vt},e.skipWhiteSpace=function(t){var e=t.getSize(),r=t.getNextSet(0);if(r===e)throw new vt;return r},e.prototype.decodeEnd=function(t){t.reverse();try{var r=e.skipWhiteSpace(t),n=void 0;try{n=e.findGuardPattern(t,r,e.END_PATTERN_REVERSED[0])}catch(i){i instanceof vt&&(n=e.findGuardPattern(t,r,e.END_PATTERN_REVERSED[1]))}this.validateQuietZone(t,n[0]);var o=n[0];return n[0]=t.getSize()-n[1],n[1]=t.getSize()-o,n}finally{t.reverse()}},e.findGuardPattern=function(t,r,n){var o=n.length,i=new Int32Array(o),a=t.getSize(),s=!1,u=0,c=r;i.fill(0);for(var f=r;f<a;f++)if(t.get(f)!==s)i[u]++;else{if(u===o-1){if(de.patternMatchVariance(i,n,e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return[c,f];c+=i[0]+i[1],q.arraycopy(i,2,i,0,u-1),i[u-1]=0,i[u]=0,u--}else u++;i[u]=1,s=!s}throw new vt},e.decodeDigit=function(t){for(var r=e.MAX_AVG_VARIANCE,n=-1,o=e.PATTERNS.length,i=0;i<o;i++){var a=e.PATTERNS[i],s=de.patternMatchVariance(t,a,e.MAX_INDIVIDUAL_VARIANCE);s<r?(r=s,n=i):s===r&&(n=-1)}if(n>=0)return n%10;throw new vt},e.PATTERNS=[Int32Array.from([1,1,2,2,1]),Int32Array.from([2,1,1,1,2]),Int32Array.from([1,2,1,1,2]),Int32Array.from([2,2,1,1,1]),Int32Array.from([1,1,2,1,2]),Int32Array.from([2,1,2,1,1]),Int32Array.from([1,2,2,1,1]),Int32Array.from([1,1,1,2,2]),Int32Array.from([2,1,1,2,1]),Int32Array.from([1,2,1,2,1]),Int32Array.from([1,1,3,3,1]),Int32Array.from([3,1,1,1,3]),Int32Array.from([1,3,1,1,3]),Int32Array.from([3,3,1,1,1]),Int32Array.from([1,1,3,1,3]),Int32Array.from([3,1,3,1,1]),Int32Array.from([1,3,3,1,1]),Int32Array.from([1,1,1,3,3]),Int32Array.from([3,1,1,3,1]),Int32Array.from([1,3,1,3,1])],e.MAX_AVG_VARIANCE=.38,e.MAX_INDIVIDUAL_VARIANCE=.5,e.DEFAULT_ALLOWED_LENGTHS=[6,8,10,12,14],e.START_PATTERN=Int32Array.from([1,1,1,1]),e.END_PATTERN_REVERSED=[Int32Array.from([1,1,2]),Int32Array.from([1,1,3])],e}(de),Se=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),be=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.decodeRowStringBuffer="",e}return Se(e,t),e.findStartGuardPattern=function(t){for(var r,n=!1,o=0,i=Int32Array.from([0,0,0]);!n;){i=Int32Array.from([0,0,0]);var a=(r=e.findGuardPattern(t,o,!1,this.START_END_PATTERN,i))[0],s=a-((o=r[1])-a);s>=0&&(n=t.isRange(s,a,!1))}return r},e.checkChecksum=function(t){return e.checkStandardUPCEANChecksum(t)},e.checkStandardUPCEANChecksum=function(t){var r=t.length;if(0===r)return!1;var n=parseInt(t.charAt(r-1),10);return e.getStandardUPCEANChecksum(t.substring(0,r-1))===n},e.getStandardUPCEANChecksum=function(t){for(var e=t.length,r=0,n=e-1;n>=0;n-=2){if((o=t.charAt(n).charCodeAt(0)-"0".charCodeAt(0))<0||o>9)throw new st;r+=o}for(r*=3,n=e-2;n>=0;n-=2){var o;if((o=t.charAt(n).charCodeAt(0)-"0".charCodeAt(0))<0||o>9)throw new st;r+=o}return(1e3-r)%10},e.decodeEnd=function(t,r){return e.findGuardPattern(t,r,!1,e.START_END_PATTERN,new Int32Array(e.START_END_PATTERN.length).fill(0))},e.findGuardPatternWithoutCounters=function(t,e,r,n){return this.findGuardPattern(t,e,r,n,new Int32Array(n.length))},e.findGuardPattern=function(t,r,n,o,i){for(var a=t.getSize(),s=0,u=r=n?t.getNextUnset(r):t.getNextSet(r),c=o.length,f=n,h=r;h<a;h++)if(t.get(h)!==f)i[s]++;else{if(s===c-1){if(de.patternMatchVariance(i,o,e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return Int32Array.from([u,h]);u+=i[0]+i[1];for(var l=i.slice(2,i.length),d=0;d<s-1;d++)i[d]=l[d];i[s-1]=0,i[s]=0,s--}else s++;i[s]=1,f=!f}throw new vt},e.decodeDigit=function(t,r,n,o){this.recordPattern(t,n,r);for(var i=this.MAX_AVG_VARIANCE,a=-1,s=o.length,u=0;u<s;u++){var c=o[u],f=de.patternMatchVariance(r,c,e.MAX_INDIVIDUAL_VARIANCE);f<i&&(i=f,a=u)}if(a>=0)return a;throw new vt},e.MAX_AVG_VARIANCE=.48,e.MAX_INDIVIDUAL_VARIANCE=.7,e.START_END_PATTERN=Int32Array.from([1,1,1]),e.MIDDLE_PATTERN=Int32Array.from([1,1,1,1,1]),e.END_PATTERN=Int32Array.from([1,1,1,1,1,1]),e.L_PATTERNS=[Int32Array.from([3,2,1,1]),Int32Array.from([2,2,2,1]),Int32Array.from([2,1,2,2]),Int32Array.from([1,4,1,1]),Int32Array.from([1,1,3,2]),Int32Array.from([1,2,3,1]),Int32Array.from([1,1,1,4]),Int32Array.from([1,3,1,2]),Int32Array.from([1,2,1,3]),Int32Array.from([3,1,1,2])],e}(de),Te=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Oe=function(){function t(){this.CHECK_DIGIT_ENCODINGS=[24,20,18,17,12,6,3,10,9,5],this.decodeMiddleCounters=Int32Array.from([0,0,0,0]),this.decodeRowStringBuffer=""}return t.prototype.decodeRow=function(e,r,n){var o=this.decodeRowStringBuffer,i=this.decodeMiddle(r,n,o),a=o.toString(),s=t.parseExtensionString(a),u=[new $t((n[0]+n[1])/2,e),new $t(i,e)],c=new Pt(a,null,0,u,Bt.UPC_EAN_EXTENSION,(new Date).getTime());return null!=s&&c.putAllMetadata(s),c},t.prototype.decodeMiddle=function(e,r,n){var o,i,a=this.decodeMiddleCounters;a[0]=0,a[1]=0,a[2]=0,a[3]=0;for(var s=e.getSize(),u=r[1],c=0,f=0;f<5&&u<s;f++){var h=be.decodeDigit(e,a,u,be.L_AND_G_PATTERNS);n+=String.fromCharCode("0".charCodeAt(0)+h%10);try{for(var l=(o=void 0,Te(a)),d=l.next();!d.done;d=l.next())u+=d.value}catch(g){o={error:g}}finally{try{d&&!d.done&&(i=l.return)&&i.call(l)}finally{if(o)throw o.error}}h>=10&&(c|=1<<4-f),4!==f&&(u=e.getNextSet(u),u=e.getNextUnset(u))}if(5!==n.length)throw new vt;var p=this.determineCheckDigit(c);if(t.extensionChecksum(n.toString())!==p)throw new vt;return u},t.extensionChecksum=function(t){for(var e=t.length,r=0,n=e-2;n>=0;n-=2)r+=t.charAt(n).charCodeAt(0)-"0".charCodeAt(0);for(r*=3,n=e-1;n>=0;n-=2)r+=t.charAt(n).charCodeAt(0)-"0".charCodeAt(0);return(r*=3)%10},t.prototype.determineCheckDigit=function(t){for(var e=0;e<10;e++)if(t===this.CHECK_DIGIT_ENCODINGS[e])return e;throw new vt},t.parseExtensionString=function(e){if(5!==e.length)return null;var r=t.parseExtension5String(e);return null==r?null:new Map([[Ft.SUGGESTED_PRICE,r]])},t.parseExtension5String=function(t){var e;switch(t.charAt(0)){case"0":e="£";break;case"5":e="$";break;case"9":switch(t){case"90000":return null;case"99991":return"0.00";case"99990":return"Used"}e="";break;default:e=""}var r=parseInt(t.substring(1)),n=r%100;return e+(r/100).toString()+"."+(n<10?"0"+n:n.toString())},t}(),Re=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Ne=function(){function t(){this.decodeMiddleCounters=Int32Array.from([0,0,0,0]),this.decodeRowStringBuffer=""}return t.prototype.decodeRow=function(e,r,n){var o=this.decodeRowStringBuffer,i=this.decodeMiddle(r,n,o),a=o.toString(),s=t.parseExtensionString(a),u=[new $t((n[0]+n[1])/2,e),new $t(i,e)],c=new Pt(a,null,0,u,Bt.UPC_EAN_EXTENSION,(new Date).getTime());return null!=s&&c.putAllMetadata(s),c},t.prototype.decodeMiddle=function(t,e,r){var n,o,i=this.decodeMiddleCounters;i[0]=0,i[1]=0,i[2]=0,i[3]=0;for(var a=t.getSize(),s=e[1],u=0,c=0;c<2&&s<a;c++){var f=be.decodeDigit(t,i,s,be.L_AND_G_PATTERNS);r+=String.fromCharCode("0".charCodeAt(0)+f%10);try{for(var h=(n=void 0,Re(i)),l=h.next();!l.done;l=h.next())s+=l.value}catch(d){n={error:d}}finally{try{l&&!l.done&&(o=h.return)&&o.call(h)}finally{if(n)throw n.error}}f>=10&&(u|=1<<1-c),1!==c&&(s=t.getNextSet(s),s=t.getNextUnset(s))}if(2!==r.length)throw new vt;if(parseInt(r.toString())%4!==u)throw new vt;return s},t.parseExtensionString=function(t){return 2!==t.length?null:new Map([[Ft.ISSUE_NUMBER,parseInt(t)]])},t}(),De=function(){function t(){}return t.decodeRow=function(t,e,r){var n=be.findGuardPattern(e,r,!1,this.EXTENSION_START_PATTERN,new Int32Array(this.EXTENSION_START_PATTERN.length).fill(0));try{return(new Oe).decodeRow(t,e,n)}catch(o){return(new Ne).decodeRow(t,e,n)}},t.EXTENSION_START_PATTERN=Int32Array.from([1,1,2]),t}(),Me=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Pe=function(t){function e(){var r=t.call(this)||this;r.decodeRowStringBuffer="",e.L_AND_G_PATTERNS=e.L_PATTERNS.map(function(t){return Int32Array.from(t)});for(var n=10;n<20;n++){for(var o=e.L_PATTERNS[n-10],i=new Int32Array(o.length),a=0;a<o.length;a++)i[a]=o[o.length-a-1];e.L_AND_G_PATTERNS[n]=i}return r}return Me(e,t),e.prototype.decodeRow=function(t,r,n){var o=e.findStartGuardPattern(r),i=null==n?null:n.get(k.NEED_RESULT_POINT_CALLBACK);if(null!=i){var a=new $t((o[0]+o[1])/2,t);i.foundPossibleResultPoint(a)}var s=this.decodeMiddle(r,o,this.decodeRowStringBuffer),u=s.rowOffset,c=s.resultString;if(null!=i){var f=new $t(u,t);i.foundPossibleResultPoint(f)}var h=e.decodeEnd(r,u);if(null!=i){var l=new $t((h[0]+h[1])/2,t);i.foundPossibleResultPoint(l)}var d=h[1],p=d+(d-h[0]);if(p>=r.getSize()||!r.isRange(d,p,!1))throw new vt;var g=c.toString();if(g.length<8)throw new st;if(!e.checkChecksum(g))throw new Z;var y=(o[1]+o[0])/2,w=(h[1]+h[0])/2,v=this.getBarcodeFormat(),_=[new $t(y,t),new $t(w,t)],m=new Pt(g,null,0,_,v,(new Date).getTime()),C=0;try{var A=De.decodeRow(t,r,h[1]);m.putMetadata(Ft.UPC_EAN_EXTENSION,A.getText()),m.putAllMetadata(A.getResultMetadata()),m.addResultPoints(A.getResultPoints()),C=A.getText().length}catch(b){}var E=null==n?null:n.get(k.ALLOWED_EAN_EXTENSIONS);if(null!=E){var I=!1;for(var S in E)if(C.toString()===S){I=!0;break}if(!I)throw new vt}return v===Bt.EAN_13||Bt.UPC_A,m},e.checkChecksum=function(t){return e.checkStandardUPCEANChecksum(t)},e.checkStandardUPCEANChecksum=function(t){var r=t.length;if(0===r)return!1;var n=parseInt(t.charAt(r-1),10);return e.getStandardUPCEANChecksum(t.substring(0,r-1))===n},e.getStandardUPCEANChecksum=function(t){for(var e=t.length,r=0,n=e-1;n>=0;n-=2){if((o=t.charAt(n).charCodeAt(0)-"0".charCodeAt(0))<0||o>9)throw new st;r+=o}for(r*=3,n=e-2;n>=0;n-=2){var o;if((o=t.charAt(n).charCodeAt(0)-"0".charCodeAt(0))<0||o>9)throw new st;r+=o}return(1e3-r)%10},e.decodeEnd=function(t,r){return e.findGuardPattern(t,r,!1,e.START_END_PATTERN,new Int32Array(e.START_END_PATTERN.length).fill(0))},e}(be),Be=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Le=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Fe=function(t){function e(){var e=t.call(this)||this;return e.decodeMiddleCounters=Int32Array.from([0,0,0,0]),e}return Be(e,t),e.prototype.decodeMiddle=function(t,r,n){var o,i,a,s,u=this.decodeMiddleCounters;u[0]=0,u[1]=0,u[2]=0,u[3]=0;for(var c=t.getSize(),f=r[1],h=0,l=0;l<6&&f<c;l++){var d=Pe.decodeDigit(t,u,f,Pe.L_AND_G_PATTERNS);n+=String.fromCharCode("0".charCodeAt(0)+d%10);try{for(var p=(o=void 0,Le(u)),g=p.next();!g.done;g=p.next())f+=g.value}catch(v){o={error:v}}finally{try{g&&!g.done&&(i=p.return)&&i.call(p)}finally{if(o)throw o.error}}d>=10&&(h|=1<<5-l)}for(n=e.determineFirstDigit(n,h),f=Pe.findGuardPattern(t,f,!0,Pe.MIDDLE_PATTERN,new Int32Array(Pe.MIDDLE_PATTERN.length).fill(0))[1],l=0;l<6&&f<c;l++){d=Pe.decodeDigit(t,u,f,Pe.L_PATTERNS),n+=String.fromCharCode("0".charCodeAt(0)+d);try{for(var y=(a=void 0,Le(u)),w=y.next();!w.done;w=y.next())f+=w.value}catch(_){a={error:_}}finally{try{w&&!w.done&&(s=y.return)&&s.call(y)}finally{if(a)throw a.error}}}return{rowOffset:f,resultString:n}},e.prototype.getBarcodeFormat=function(){return Bt.EAN_13},e.determineFirstDigit=function(t,e){for(var r=0;r<10;r++)if(e===this.FIRST_DIGIT_ENCODINGS[r])return t=String.fromCharCode("0".charCodeAt(0)+r)+t;throw new vt},e.FIRST_DIGIT_ENCODINGS=[0,11,13,14,19,25,28,21,22,26],e}(Pe),ke=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),xe=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Ve=function(t){function e(){var e=t.call(this)||this;return e.decodeMiddleCounters=Int32Array.from([0,0,0,0]),e}return ke(e,t),e.prototype.decodeMiddle=function(t,e,r){var n,o,i,a,s=this.decodeMiddleCounters;s[0]=0,s[1]=0,s[2]=0,s[3]=0;for(var u=t.getSize(),c=e[1],f=0;f<4&&c<u;f++){var h=Pe.decodeDigit(t,s,c,Pe.L_PATTERNS);r+=String.fromCharCode("0".charCodeAt(0)+h);try{for(var l=(n=void 0,xe(s)),d=l.next();!d.done;d=l.next())c+=d.value}catch(y){n={error:y}}finally{try{d&&!d.done&&(o=l.return)&&o.call(l)}finally{if(n)throw n.error}}}for(c=Pe.findGuardPattern(t,c,!0,Pe.MIDDLE_PATTERN,new Int32Array(Pe.MIDDLE_PATTERN.length).fill(0))[1],f=0;f<4&&c<u;f++){h=Pe.decodeDigit(t,s,c,Pe.L_PATTERNS),r+=String.fromCharCode("0".charCodeAt(0)+h);try{for(var p=(i=void 0,xe(s)),g=p.next();!g.done;g=p.next())c+=g.value}catch(w){i={error:w}}finally{try{g&&!g.done&&(a=p.return)&&a.call(p)}finally{if(i)throw i.error}}}return{rowOffset:c,resultString:r}},e.prototype.getBarcodeFormat=function(){return Bt.EAN_8},e}(Pe),Ue=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),He=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.ean13Reader=new Fe,e}return Ue(e,t),e.prototype.getBarcodeFormat=function(){return Bt.UPC_A},e.prototype.decode=function(t,e){return this.maybeReturnResult(this.ean13Reader.decode(t))},e.prototype.decodeRow=function(t,e,r){return this.maybeReturnResult(this.ean13Reader.decodeRow(t,e,r))},e.prototype.decodeMiddle=function(t,e,r){return this.ean13Reader.decodeMiddle(t,e,r)},e.prototype.maybeReturnResult=function(t){var e=t.getText();if("0"===e.charAt(0)){var r=new Pt(e.substring(1),null,null,t.getResultPoints(),Bt.UPC_A);return null!=t.getResultMetadata()&&r.putAllMetadata(t.getResultMetadata()),r}throw new vt},e.prototype.reset=function(){this.ean13Reader.reset()},e}(Pe),Ge=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Xe=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},We=function(t){function e(){var e=t.call(this)||this;return e.decodeMiddleCounters=new Int32Array(4),e}return Ge(e,t),e.prototype.decodeMiddle=function(t,r,n){var o,i,a=this.decodeMiddleCounters.map(function(t){return t});a[0]=0,a[1]=0,a[2]=0,a[3]=0;for(var s=t.getSize(),u=r[1],c=0,f=0;f<6&&u<s;f++){var h=e.decodeDigit(t,a,u,e.L_AND_G_PATTERNS);n+=String.fromCharCode("0".charCodeAt(0)+h%10);try{for(var l=(o=void 0,Xe(a)),d=l.next();!d.done;d=l.next())u+=d.value}catch(p){o={error:p}}finally{try{d&&!d.done&&(i=l.return)&&i.call(l)}finally{if(o)throw o.error}}h>=10&&(c|=1<<5-f)}return e.determineNumSysAndCheckDigit(new gt(n),c),u},e.prototype.decodeEnd=function(t,r){return e.findGuardPatternWithoutCounters(t,r,!0,e.MIDDLE_END_PATTERN)},e.prototype.checkChecksum=function(t){return Pe.checkChecksum(e.convertUPCEtoUPCA(t))},e.determineNumSysAndCheckDigit=function(t,e){for(var r=0;r<=1;r++)for(var n=0;n<10;n++)if(e===this.NUMSYS_AND_CHECK_DIGIT_PATTERNS[r][n])return t.insert(0,"0"+r),void t.append("0"+n);throw vt.getNotFoundInstance()},e.prototype.getBarcodeFormat=function(){return Bt.UPC_E},e.convertUPCEtoUPCA=function(t){var e=t.slice(1,7).split("").map(function(t){return t.charCodeAt(0)}),r=new gt;r.append(t.charAt(0));var n=e[5];switch(n){case 0:case 1:case 2:r.appendChars(e,0,2),r.append(n),r.append("0000"),r.appendChars(e,2,3);break;case 3:r.appendChars(e,0,3),r.append("00000"),r.appendChars(e,3,2);break;case 4:r.appendChars(e,0,4),r.append("00000"),r.append(e[4]);break;default:r.appendChars(e,0,5),r.append("0000"),r.append(n)}return t.length>=8&&r.append(t.charAt(7)),r.toString()},e.MIDDLE_END_PATTERN=Int32Array.from([1,1,1,1,1,1]),e.NUMSYS_AND_CHECK_DIGIT_PATTERNS=[Int32Array.from([56,52,50,49,44,38,35,42,41,37]),Int32Array.from([7,11,13,14,19,25,28,21,22,1])],e}(Pe),je=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),ze=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Ye=function(t){function e(e){var r=t.call(this)||this,n=null==e?null:e.get(k.POSSIBLE_FORMATS),o=[];return null!=n&&(n.indexOf(Bt.EAN_13)>-1&&o.push(new Fe),n.indexOf(Bt.UPC_A)>-1&&o.push(new He),n.indexOf(Bt.EAN_8)>-1&&o.push(new Ve),n.indexOf(Bt.UPC_E)>-1&&o.push(new We)),0===o.length&&(o.push(new Fe),o.push(new He),o.push(new Ve),o.push(new We)),r.readers=o,r}return je(e,t),e.prototype.decodeRow=function(t,e,r){var n,o;try{for(var i=ze(this.readers),a=i.next();!a.done;a=i.next()){var s=a.value;try{var u=s.decodeRow(t,e,r),c=u.getBarcodeFormat()===Bt.EAN_13&&"0"===u.getText().charAt(0),f=null==r?null:r.get(k.POSSIBLE_FORMATS),h=null==f||f.includes(Bt.UPC_A);if(c&&h){var l=u.getRawBytes(),d=new Pt(u.getText().substring(1),l,l?l.length:null,u.getResultPoints(),Bt.UPC_A);return d.putAllMetadata(u.getResultMetadata()),d}return u}catch(p){}}}catch(g){n={error:g}}finally{try{a&&!a.done&&(o=i.return)&&o.call(i)}finally{if(n)throw n.error}}throw new vt},e.prototype.reset=function(){var t,e;try{for(var r=ze(this.readers),n=r.next();!n.done;n=r.next())n.value.reset()}catch(o){t={error:o}}finally{try{n&&!n.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}},e}(de),Ze=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Ke=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.CODA_BAR_CHAR_SET={nnnnnww:"0",nnnnwwn:"1",nnnwnnw:"2",wwnnnnn:"3",nnwnnwn:"4",wnnnnwn:"5",nwnnnnw:"6",nwnnwnn:"7",nwwnnnn:"8",wnnwnnn:"9",nnnwwnn:"-",nnwwnnn:"$",wnnnwnw:":",wnwnnnw:"/",wnwnwnn:".",nnwwwww:"+",nnwwnwn:"A",nwnwnnw:"B",nnnwnww:"C",nnnwwwn:"D"},e}return Ze(e,t),e.prototype.decodeRow=function(t,e,r){var n=this.getValidRowData(e);if(!n)throw new vt;var o=this.codaBarDecodeRow(n.row);if(!o)throw new vt;return new Pt(o,null,0,[new $t(n.left,t),new $t(n.right,t)],Bt.CODABAR,(new Date).getTime())},e.prototype.getValidRowData=function(t){var e=t.toArray(),r=e.indexOf(!0);if(-1===r)return null;var n=e.lastIndexOf(!0);if(n<=r)return null;for(var o=[],i=(e=e.slice(r,n+1))[0],a=1,s=1;s<e.length;s++)e[s]===i?a++:(i=e[s],o.push(a),a=1);return o.push(a),o.length<23&&(o.length+1)%8!=0?null:{row:o,left:r,right:n}},e.prototype.codaBarDecodeRow=function(t){for(var e=[],r=Math.ceil(t.reduce(function(t,e){return(t+e)/2},0));t.length>0;){var n=t.splice(0,8).splice(0,7).map(function(t){return t<r?"n":"w"}).join("");if(void 0===this.CODA_BAR_CHAR_SET[n])return null;e.push(this.CODA_BAR_CHAR_SET[n])}var o=e.join("");return this.validCodaBarString(o)?o:null},e.prototype.validCodaBarString=function(t){return/^[A-D].{1,}[A-D]$/.test(t)},e}(de),qe=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Qe=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Je=function(t){function e(){var e=t.call(this)||this;return e.decodeFinderCounters=new Int32Array(4),e.dataCharacterCounters=new Int32Array(8),e.oddRoundingErrors=new Array(4),e.evenRoundingErrors=new Array(4),e.oddCounts=new Array(e.dataCharacterCounters.length/2),e.evenCounts=new Array(e.dataCharacterCounters.length/2),e}return qe(e,t),e.prototype.getDecodeFinderCounters=function(){return this.decodeFinderCounters},e.prototype.getDataCharacterCounters=function(){return this.dataCharacterCounters},e.prototype.getOddRoundingErrors=function(){return this.oddRoundingErrors},e.prototype.getEvenRoundingErrors=function(){return this.evenRoundingErrors},e.prototype.getOddCounts=function(){return this.oddCounts},e.prototype.getEvenCounts=function(){return this.evenCounts},e.prototype.parseFinderValue=function(t,r){for(var n=0;n<r.length;n++)if(de.patternMatchVariance(t,r[n],e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return n;throw new vt},e.count=function(t){return Qt.sum(new Int32Array(t))},e.increment=function(t,e){for(var r=0,n=e[0],o=1;o<t.length;o++)e[o]>n&&(n=e[o],r=o);t[r]++},e.decrement=function(t,e){for(var r=0,n=e[0],o=1;o<t.length;o++)e[o]<n&&(n=e[o],r=o);t[r]--},e.isFinderPattern=function(t){var r,n,o=t[0]+t[1],i=o/(o+t[2]+t[3]);if(i>=e.MIN_FINDER_PATTERN_RATIO&&i<=e.MAX_FINDER_PATTERN_RATIO){var a=Number.MAX_SAFE_INTEGER,s=Number.MIN_SAFE_INTEGER;try{for(var u=Qe(t),c=u.next();!c.done;c=u.next()){var f=c.value;f>s&&(s=f),f<a&&(a=f)}}catch(h){r={error:h}}finally{try{c&&!c.done&&(n=u.return)&&n.call(u)}finally{if(r)throw r.error}}return s<10*a}return!1},e.MAX_AVG_VARIANCE=.2,e.MAX_INDIVIDUAL_VARIANCE=.45,e.MIN_FINDER_PATTERN_RATIO=9.5/12,e.MAX_FINDER_PATTERN_RATIO=12.5/14,e}(de),$e=function(){function t(t,e){this.value=t,this.checksumPortion=e}return t.prototype.getValue=function(){return this.value},t.prototype.getChecksumPortion=function(){return this.checksumPortion},t.prototype.toString=function(){return this.value+"("+this.checksumPortion+")"},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;return this.value===r.value&&this.checksumPortion===r.checksumPortion},t.prototype.hashCode=function(){return this.value^this.checksumPortion},t}(),tr=function(){function t(t,e,r,n,o){this.value=t,this.startEnd=e,this.value=t,this.startEnd=e,this.resultPoints=new Array,this.resultPoints.push(new $t(r,o)),this.resultPoints.push(new $t(n,o))}return t.prototype.getValue=function(){return this.value},t.prototype.getStartEnd=function(){return this.startEnd},t.prototype.getResultPoints=function(){return this.resultPoints},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;return this.value===r.value},t.prototype.hashCode=function(){return this.value},t}(),er=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},rr=function(){function t(){}return t.getRSSvalue=function(e,r,n){var o,i,a=0;try{for(var s=er(e),u=s.next();!u.done;u=s.next())a+=u.value}catch(w){o={error:w}}finally{try{u&&!u.done&&(i=s.return)&&i.call(s)}finally{if(o)throw o.error}}for(var c=0,f=0,h=e.length,l=0;l<h-1;l++){var d=void 0;for(d=1,f|=1<<l;d<e[l];d++,f&=~(1<<l)){var p=t.combins(a-d-1,h-l-2);if(n&&0===f&&a-d-(h-l-1)>=h-l-1&&(p-=t.combins(a-d-(h-l),h-l-2)),h-l-1>1){for(var g=0,y=a-d-(h-l-2);y>r;y--)g+=t.combins(a-d-y-1,h-l-3);p-=g*(h-1-l)}else a-d>r&&p--;c+=p}a-=d}return c},t.combins=function(t,e){var r,n;t-e>e?(n=e,r=t-e):(n=t-e,r=e);for(var o=1,i=1,a=t;a>r;a--)o*=a,i<=n&&(o/=i,i++);for(;i<=n;)o/=i,i++;return o},t}(),nr=function(){function t(){}return t.buildBitArray=function(t){var e=2*t.length-1;null==t[t.length-1].getRightChar()&&(e-=1);for(var r=new ot(12*e),n=0,o=t[0].getRightChar().getValue(),i=11;i>=0;--i)o&1<<i&&r.set(n),n++;for(i=1;i<t.length;++i){for(var a=t[i],s=a.getLeftChar().getValue(),u=11;u>=0;--u)s&1<<u&&r.set(n),n++;if(null!==a.getRightChar()){var c=a.getRightChar().getValue();for(u=11;u>=0;--u)c&1<<u&&r.set(n),n++}}return r},t}(),or=function(){function t(t,e){e?this.decodedInformation=null:(this.finished=t,this.decodedInformation=e)}return t.prototype.getDecodedInformation=function(){return this.decodedInformation},t.prototype.isFinished=function(){return this.finished},t}(),ir=function(){function t(t){this.newPosition=t}return t.prototype.getNewPosition=function(){return this.newPosition},t}(),ar=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),sr=function(t){function e(e,r){var n=t.call(this,e)||this;return n.value=r,n}return ar(e,t),e.prototype.getValue=function(){return this.value},e.prototype.isFNC1=function(){return this.value===e.FNC1},e.FNC1="$",e}(ir),ur=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),cr=function(t){function e(e,r,n){var o=t.call(this,e)||this;return n?(o.remaining=!0,o.remainingValue=o.remainingValue):(o.remaining=!1,o.remainingValue=0),o.newString=r,o}return ur(e,t),e.prototype.getNewString=function(){return this.newString},e.prototype.isRemaining=function(){return this.remaining},e.prototype.getRemainingValue=function(){return this.remainingValue},e}(ir),fr=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),hr=function(t){function e(e,r,n){var o=t.call(this,e)||this;if(r<0||r>10||n<0||n>10)throw new st;return o.firstDigit=r,o.secondDigit=n,o}return fr(e,t),e.prototype.getFirstDigit=function(){return this.firstDigit},e.prototype.getSecondDigit=function(){return this.secondDigit},e.prototype.getValue=function(){return 10*this.firstDigit+this.secondDigit},e.prototype.isFirstDigitFNC1=function(){return this.firstDigit===e.FNC1},e.prototype.isSecondDigitFNC1=function(){return this.secondDigit===e.FNC1},e.prototype.isAnyFNC1=function(){return this.firstDigit===e.FNC1||this.secondDigit===e.FNC1},e.FNC1=10,e}(ir),lr=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},dr=function(){function t(){}return t.parseFieldsInGeneralPurpose=function(e){var r,n,o,i,a,s,u,c;if(!e)return null;if(e.length<2)throw new vt;var f=e.substring(0,2);try{for(var h=lr(t.TWO_DIGIT_DATA_LENGTH),l=h.next();!l.done;l=h.next())if((C=l.value)[0]===f)return C[1]===t.VARIABLE_LENGTH?t.processVariableAI(2,C[2],e):t.processFixedAI(2,C[1],e)}catch(A){r={error:A}}finally{try{l&&!l.done&&(n=h.return)&&n.call(h)}finally{if(r)throw r.error}}if(e.length<3)throw new vt;var d=e.substring(0,3);try{for(var p=lr(t.THREE_DIGIT_DATA_LENGTH),g=p.next();!g.done;g=p.next())if((C=g.value)[0]===d)return C[1]===t.VARIABLE_LENGTH?t.processVariableAI(3,C[2],e):t.processFixedAI(3,C[1],e)}catch(E){o={error:E}}finally{try{g&&!g.done&&(i=p.return)&&i.call(p)}finally{if(o)throw o.error}}try{for(var y=lr(t.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH),w=y.next();!w.done;w=y.next())if((C=w.value)[0]===d)return C[1]===t.VARIABLE_LENGTH?t.processVariableAI(4,C[2],e):t.processFixedAI(4,C[1],e)}catch(I){a={error:I}}finally{try{w&&!w.done&&(s=y.return)&&s.call(y)}finally{if(a)throw a.error}}if(e.length<4)throw new vt;var v=e.substring(0,4);try{for(var _=lr(t.FOUR_DIGIT_DATA_LENGTH),m=_.next();!m.done;m=_.next()){var C;if((C=m.value)[0]===v)return C[1]===t.VARIABLE_LENGTH?t.processVariableAI(4,C[2],e):t.processFixedAI(4,C[1],e)}}catch(S){u={error:S}}finally{try{m&&!m.done&&(c=_.return)&&c.call(_)}finally{if(u)throw u.error}}throw new vt},t.processFixedAI=function(e,r,n){if(n.length<e)throw new vt;var o=n.substring(0,e);if(n.length<e+r)throw new vt;var i=n.substring(e,e+r),a=n.substring(e+r),s="("+o+")"+i,u=t.parseFieldsInGeneralPurpose(a);return null==u?s:s+u},t.processVariableAI=function(e,r,n){var o,i=n.substring(0,e);o=n.length<e+r?n.length:e+r;var a=n.substring(e,o),s=n.substring(o),u="("+i+")"+a,c=t.parseFieldsInGeneralPurpose(s);return null==c?u:u+c},t.VARIABLE_LENGTH=[],t.TWO_DIGIT_DATA_LENGTH=[["00",18],["01",14],["02",14],["10",t.VARIABLE_LENGTH,20],["11",6],["12",6],["13",6],["15",6],["17",6],["20",2],["21",t.VARIABLE_LENGTH,20],["22",t.VARIABLE_LENGTH,29],["30",t.VARIABLE_LENGTH,8],["37",t.VARIABLE_LENGTH,8],["90",t.VARIABLE_LENGTH,30],["91",t.VARIABLE_LENGTH,30],["92",t.VARIABLE_LENGTH,30],["93",t.VARIABLE_LENGTH,30],["94",t.VARIABLE_LENGTH,30],["95",t.VARIABLE_LENGTH,30],["96",t.VARIABLE_LENGTH,30],["97",t.VARIABLE_LENGTH,3],["98",t.VARIABLE_LENGTH,30],["99",t.VARIABLE_LENGTH,30]],t.THREE_DIGIT_DATA_LENGTH=[["240",t.VARIABLE_LENGTH,30],["241",t.VARIABLE_LENGTH,30],["242",t.VARIABLE_LENGTH,6],["250",t.VARIABLE_LENGTH,30],["251",t.VARIABLE_LENGTH,30],["253",t.VARIABLE_LENGTH,17],["254",t.VARIABLE_LENGTH,20],["400",t.VARIABLE_LENGTH,30],["401",t.VARIABLE_LENGTH,30],["402",17],["403",t.VARIABLE_LENGTH,30],["410",13],["411",13],["412",13],["413",13],["414",13],["420",t.VARIABLE_LENGTH,20],["421",t.VARIABLE_LENGTH,15],["422",3],["423",t.VARIABLE_LENGTH,15],["424",3],["425",3],["426",3]],t.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH=[["310",6],["311",6],["312",6],["313",6],["314",6],["315",6],["316",6],["320",6],["321",6],["322",6],["323",6],["324",6],["325",6],["326",6],["327",6],["328",6],["329",6],["330",6],["331",6],["332",6],["333",6],["334",6],["335",6],["336",6],["340",6],["341",6],["342",6],["343",6],["344",6],["345",6],["346",6],["347",6],["348",6],["349",6],["350",6],["351",6],["352",6],["353",6],["354",6],["355",6],["356",6],["357",6],["360",6],["361",6],["362",6],["363",6],["364",6],["365",6],["366",6],["367",6],["368",6],["369",6],["390",t.VARIABLE_LENGTH,15],["391",t.VARIABLE_LENGTH,18],["392",t.VARIABLE_LENGTH,15],["393",t.VARIABLE_LENGTH,18],["703",t.VARIABLE_LENGTH,30]],t.FOUR_DIGIT_DATA_LENGTH=[["7001",13],["7002",t.VARIABLE_LENGTH,30],["7003",10],["8001",14],["8002",t.VARIABLE_LENGTH,20],["8003",t.VARIABLE_LENGTH,30],["8004",t.VARIABLE_LENGTH,30],["8005",6],["8006",18],["8007",t.VARIABLE_LENGTH,30],["8008",t.VARIABLE_LENGTH,12],["8018",18],["8020",t.VARIABLE_LENGTH,25],["8100",6],["8101",10],["8102",2],["8110",t.VARIABLE_LENGTH,70],["8200",t.VARIABLE_LENGTH,70]],t}(),pr=function(){function t(t){this.buffer=new gt,this.information=t}return t.prototype.decodeAllCodes=function(t,e){for(var r=e,n=null;;){var o=this.decodeGeneralPurposeField(r,n),i=dr.parseFieldsInGeneralPurpose(o.getNewString());if(null!=i&&t.append(i),n=o.isRemaining()?""+o.getRemainingValue():null,r===o.getNewPosition())break;r=o.getNewPosition()}return t.toString()},t.prototype.isStillNumeric=function(t){if(t+7>this.information.getSize())return t+4<=this.information.getSize();for(var e=t;e<t+3;++e)if(this.information.get(e))return!0;return this.information.get(t+3)},t.prototype.decodeNumeric=function(t){if(t+7>this.information.getSize()){var e=this.extractNumericValueFromBitArray(t,4);return new hr(this.information.getSize(),0===e?hr.FNC1:e-1,hr.FNC1)}var r=this.extractNumericValueFromBitArray(t,7);return new hr(t+7,(r-8)/11,(r-8)%11)},t.prototype.extractNumericValueFromBitArray=function(e,r){return t.extractNumericValueFromBitArray(this.information,e,r)},t.extractNumericValueFromBitArray=function(t,e,r){for(var n=0,o=0;o<r;++o)t.get(e+o)&&(n|=1<<r-o-1);return n},t.prototype.decodeGeneralPurposeField=function(t,e){this.buffer.setLengthToZero(),null!=e&&this.buffer.append(e),this.current.setPosition(t);var r=this.parseBlocks();return null!=r&&r.isRemaining()?new cr(this.current.getPosition(),this.buffer.toString(),r.getRemainingValue()):new cr(this.current.getPosition(),this.buffer.toString())},t.prototype.parseBlocks=function(){var t,e;do{var r=this.current.getPosition();if(t=this.current.isAlpha()?(e=this.parseAlphaBlock()).isFinished():this.current.isIsoIec646()?(e=this.parseIsoIec646Block()).isFinished():(e=this.parseNumericBlock()).isFinished(),r===this.current.getPosition()&&!t)break}while(!t);return e.getDecodedInformation()},t.prototype.parseNumericBlock=function(){for(;this.isStillNumeric(this.current.getPosition());){var t=this.decodeNumeric(this.current.getPosition());if(this.current.setPosition(t.getNewPosition()),t.isFirstDigitFNC1()){var e=void 0;return e=t.isSecondDigitFNC1()?new cr(this.current.getPosition(),this.buffer.toString()):new cr(this.current.getPosition(),this.buffer.toString(),t.getSecondDigit()),new or(!0,e)}if(this.buffer.append(t.getFirstDigit()),t.isSecondDigitFNC1())return e=new cr(this.current.getPosition(),this.buffer.toString()),new or(!0,e);this.buffer.append(t.getSecondDigit())}return this.isNumericToAlphaNumericLatch(this.current.getPosition())&&(this.current.setAlpha(),this.current.incrementPosition(4)),new or(!1)},t.prototype.parseIsoIec646Block=function(){for(;this.isStillIsoIec646(this.current.getPosition());){var t=this.decodeIsoIec646(this.current.getPosition());if(this.current.setPosition(t.getNewPosition()),t.isFNC1()){var e=new cr(this.current.getPosition(),this.buffer.toString());return new or(!0,e)}this.buffer.append(t.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setAlpha()),new or(!1)},t.prototype.parseAlphaBlock=function(){for(;this.isStillAlpha(this.current.getPosition());){var t=this.decodeAlphanumeric(this.current.getPosition());if(this.current.setPosition(t.getNewPosition()),t.isFNC1()){var e=new cr(this.current.getPosition(),this.buffer.toString());return new or(!0,e)}this.buffer.append(t.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setIsoIec646()),new or(!1)},t.prototype.isStillIsoIec646=function(t){if(t+5>this.information.getSize())return!1;var e=this.extractNumericValueFromBitArray(t,5);if(e>=5&&e<16)return!0;if(t+7>this.information.getSize())return!1;var r=this.extractNumericValueFromBitArray(t,7);if(r>=64&&r<116)return!0;if(t+8>this.information.getSize())return!1;var n=this.extractNumericValueFromBitArray(t,8);return n>=232&&n<253},t.prototype.decodeIsoIec646=function(t){var e=this.extractNumericValueFromBitArray(t,5);if(15===e)return new sr(t+5,sr.FNC1);if(e>=5&&e<15)return new sr(t+5,"0"+(e-5));var r,n=this.extractNumericValueFromBitArray(t,7);if(n>=64&&n<90)return new sr(t+7,""+(n+1));if(n>=90&&n<116)return new sr(t+7,""+(n+7));switch(this.extractNumericValueFromBitArray(t,8)){case 232:r="!";break;case 233:r='"';break;case 234:r="%";break;case 235:r="&";break;case 236:r="'";break;case 237:r="(";break;case 238:r=")";break;case 239:r="*";break;case 240:r="+";break;case 241:r=",";break;case 242:r="-";break;case 243:r=".";break;case 244:r="/";break;case 245:r=":";break;case 246:r=";";break;case 247:r="<";break;case 248:r="=";break;case 249:r=">";break;case 250:r="?";break;case 251:r="_";break;case 252:r=" ";break;default:throw new st}return new sr(t+8,r)},t.prototype.isStillAlpha=function(t){if(t+5>this.information.getSize())return!1;var e=this.extractNumericValueFromBitArray(t,5);if(e>=5&&e<16)return!0;if(t+6>this.information.getSize())return!1;var r=this.extractNumericValueFromBitArray(t,6);return r>=16&&r<63},t.prototype.decodeAlphanumeric=function(t){var e=this.extractNumericValueFromBitArray(t,5);if(15===e)return new sr(t+5,sr.FNC1);if(e>=5&&e<15)return new sr(t+5,"0"+(e-5));var r,n=this.extractNumericValueFromBitArray(t,6);if(n>=32&&n<58)return new sr(t+6,""+(n+33));switch(n){case 58:r="*";break;case 59:r=",";break;case 60:r="-";break;case 61:r=".";break;case 62:r="/";break;default:throw new Zt("Decoding invalid alphanumeric value: "+n)}return new sr(t+6,r)},t.prototype.isAlphaTo646ToAlphaLatch=function(t){if(t+1>this.information.getSize())return!1;for(var e=0;e<5&&e+t<this.information.getSize();++e)if(2===e){if(!this.information.get(t+2))return!1}else if(this.information.get(t+e))return!1;return!0},t.prototype.isAlphaOr646ToNumericLatch=function(t){if(t+3>this.information.getSize())return!1;for(var e=t;e<t+3;++e)if(this.information.get(e))return!1;return!0},t.prototype.isNumericToAlphaNumericLatch=function(t){if(t+1>this.information.getSize())return!1;for(var e=0;e<4&&e+t<this.information.getSize();++e)if(this.information.get(t+e))return!1;return!0},t}(),gr=function(){function t(t){this.information=t,this.generalDecoder=new pr(t)}return t.prototype.getInformation=function(){return this.information},t.prototype.getGeneralDecoder=function(){return this.generalDecoder},t}(),yr=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),wr=function(t){function e(e){return t.call(this,e)||this}return yr(e,t),e.prototype.encodeCompressedGtin=function(t,e){t.append("(01)");var r=t.length();t.append("9"),this.encodeCompressedGtinWithoutAI(t,e,r)},e.prototype.encodeCompressedGtinWithoutAI=function(t,r,n){for(var o=0;o<4;++o){var i=this.getGeneralDecoder().extractNumericValueFromBitArray(r+10*o,10);i/100==0&&t.append("0"),i/10==0&&t.append("0"),t.append(i)}e.appendCheckDigit(t,n)},e.appendCheckDigit=function(t,e){for(var r=0,n=0;n<13;n++){var o=t.charAt(n+e).charCodeAt(0)-"0".charCodeAt(0);r+=1&n?o:3*o}10==(r=10-r%10)&&(r=0),t.append(r)},e.GTIN_SIZE=40,e}(gr),vr=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),_r=function(t){function e(e){return t.call(this,e)||this}return vr(e,t),e.prototype.parseInformation=function(){var t=new gt;t.append("(01)");var r=t.length(),n=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE,4);return t.append(n),this.encodeCompressedGtinWithoutAI(t,e.HEADER_SIZE+4,r),this.getGeneralDecoder().decodeAllCodes(t,e.HEADER_SIZE+44)},e.HEADER_SIZE=4,e}(wr),mr=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Cr=function(t){function e(e){return t.call(this,e)||this}return mr(e,t),e.prototype.parseInformation=function(){var t=new gt;return this.getGeneralDecoder().decodeAllCodes(t,e.HEADER_SIZE)},e.HEADER_SIZE=5,e}(gr),Ar=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Er=function(t){function e(e){return t.call(this,e)||this}return Ar(e,t),e.prototype.encodeCompressedWeight=function(t,e,r){var n=this.getGeneralDecoder().extractNumericValueFromBitArray(e,r);this.addWeightCode(t,n);for(var o=this.checkWeight(n),i=1e5,a=0;a<5;++a)o/i===0&&t.append("0"),i/=10;t.append(o)},e}(wr),Ir=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Sr=function(t){function e(e){return t.call(this,e)||this}return Ir(e,t),e.prototype.parseInformation=function(){if(this.getInformation().getSize()!==e.HEADER_SIZE+Er.GTIN_SIZE+e.WEIGHT_SIZE)throw new vt;var t=new gt;return this.encodeCompressedGtin(t,e.HEADER_SIZE),this.encodeCompressedWeight(t,e.HEADER_SIZE+Er.GTIN_SIZE,e.WEIGHT_SIZE),t.toString()},e.HEADER_SIZE=5,e.WEIGHT_SIZE=15,e}(Er),br=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Tr=function(t){function e(e){return t.call(this,e)||this}return br(e,t),e.prototype.addWeightCode=function(t,e){t.append("(3103)")},e.prototype.checkWeight=function(t){return t},e}(Sr),Or=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Rr=function(t){function e(e){return t.call(this,e)||this}return Or(e,t),e.prototype.addWeightCode=function(t,e){e<1e4?t.append("(3202)"):t.append("(3203)")},e.prototype.checkWeight=function(t){return t<1e4?t:t-1e4},e}(Sr),Nr=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Dr=function(t){function e(e){return t.call(this,e)||this}return Nr(e,t),e.prototype.parseInformation=function(){if(this.getInformation().getSize()<e.HEADER_SIZE+wr.GTIN_SIZE)throw new vt;var t=new gt;this.encodeCompressedGtin(t,e.HEADER_SIZE);var r=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+wr.GTIN_SIZE,e.LAST_DIGIT_SIZE);t.append("(392"),t.append(r),t.append(")");var n=this.getGeneralDecoder().decodeGeneralPurposeField(e.HEADER_SIZE+wr.GTIN_SIZE+e.LAST_DIGIT_SIZE,null);return t.append(n.getNewString()),t.toString()},e.HEADER_SIZE=8,e.LAST_DIGIT_SIZE=2,e}(wr),Mr=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Pr=function(t){function e(e){return t.call(this,e)||this}return Mr(e,t),e.prototype.parseInformation=function(){if(this.getInformation().getSize()<e.HEADER_SIZE+wr.GTIN_SIZE)throw new vt;var t=new gt;this.encodeCompressedGtin(t,e.HEADER_SIZE);var r=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+wr.GTIN_SIZE,e.LAST_DIGIT_SIZE);t.append("(393"),t.append(r),t.append(")");var n=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+wr.GTIN_SIZE+e.LAST_DIGIT_SIZE,e.FIRST_THREE_DIGITS_SIZE);n/100==0&&t.append("0"),n/10==0&&t.append("0"),t.append(n);var o=this.getGeneralDecoder().decodeGeneralPurposeField(e.HEADER_SIZE+wr.GTIN_SIZE+e.LAST_DIGIT_SIZE+e.FIRST_THREE_DIGITS_SIZE,null);return t.append(o.getNewString()),t.toString()},e.HEADER_SIZE=8,e.LAST_DIGIT_SIZE=2,e.FIRST_THREE_DIGITS_SIZE=10,e}(wr),Br=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Lr=function(t){function e(e,r,n){var o=t.call(this,e)||this;return o.dateCode=n,o.firstAIdigits=r,o}return Br(e,t),e.prototype.parseInformation=function(){if(this.getInformation().getSize()!==e.HEADER_SIZE+e.GTIN_SIZE+e.WEIGHT_SIZE+e.DATE_SIZE)throw new vt;var t=new gt;return this.encodeCompressedGtin(t,e.HEADER_SIZE),this.encodeCompressedWeight(t,e.HEADER_SIZE+e.GTIN_SIZE,e.WEIGHT_SIZE),this.encodeCompressedDate(t,e.HEADER_SIZE+e.GTIN_SIZE+e.WEIGHT_SIZE),t.toString()},e.prototype.encodeCompressedDate=function(t,r){var n=this.getGeneralDecoder().extractNumericValueFromBitArray(r,e.DATE_SIZE);if(38400!==n){t.append("("),t.append(this.dateCode),t.append(")");var o=n%32,i=(n/=32)%12+1,a=n/=12;a/10==0&&t.append("0"),t.append(a),i/10==0&&t.append("0"),t.append(i),o/10==0&&t.append("0"),t.append(o)}},e.prototype.addWeightCode=function(t,e){t.append("("),t.append(this.firstAIdigits),t.append(e/1e5),t.append(")")},e.prototype.checkWeight=function(t){return t%1e5},e.HEADER_SIZE=8,e.WEIGHT_SIZE=20,e.DATE_SIZE=16,e}(Er),Fr=function(){function t(t,e,r,n){this.leftchar=t,this.rightchar=e,this.finderpattern=r,this.maybeLast=n}return t.prototype.mayBeLast=function(){return this.maybeLast},t.prototype.getLeftChar=function(){return this.leftchar},t.prototype.getRightChar=function(){return this.rightchar},t.prototype.getFinderPattern=function(){return this.finderpattern},t.prototype.mustBeLast=function(){return null==this.rightchar},t.prototype.toString=function(){return"[ "+this.leftchar+", "+this.rightchar+" : "+(null==this.finderpattern?"null":this.finderpattern.getValue())+" ]"},t.equals=function(e,r){return e instanceof t&&t.equalsOrNull(e.leftchar,r.leftchar)&&t.equalsOrNull(e.rightchar,r.rightchar)&&t.equalsOrNull(e.finderpattern,r.finderpattern)},t.equalsOrNull=function(e,r){return null===e?null===r:t.equals(e,r)},t.prototype.hashCode=function(){return this.leftchar.getValue()^this.rightchar.getValue()^this.finderpattern.getValue()},t}(),kr=function(){function t(t,e,r){this.pairs=t,this.rowNumber=e,this.wasReversed=r}return t.prototype.getPairs=function(){return this.pairs},t.prototype.getRowNumber=function(){return this.rowNumber},t.prototype.isReversed=function(){return this.wasReversed},t.prototype.isEquivalent=function(t){return this.checkEqualitity(this,t)},t.prototype.toString=function(){return"{ "+this.pairs+" }"},t.prototype.equals=function(e,r){return e instanceof t&&this.checkEqualitity(e,r)&&e.wasReversed===r.wasReversed},t.prototype.checkEqualitity=function(t,e){var r;if(t&&e)return t.forEach(function(t,n){e.forEach(function(e){t.getLeftChar().getValue()===e.getLeftChar().getValue()&&t.getRightChar().getValue()===e.getRightChar().getValue()&&t.getFinderPatter().getValue()===e.getFinderPatter().getValue()&&(r=!0)})}),r},t}(),xr=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Vr=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Ur=function(t){function e(){var r=null!==t&&t.apply(this,arguments)||this;return r.pairs=new Array(e.MAX_PAIRS),r.rows=new Array,r.startEnd=[2],r}return xr(e,t),e.prototype.decodeRow=function(t,r,n){this.pairs.length=0,this.startFromEven=!1;try{return e.constructResult(this.decodeRow2pairs(t,r))}catch(o){}return this.pairs.length=0,this.startFromEven=!0,e.constructResult(this.decodeRow2pairs(t,r))},e.prototype.reset=function(){this.pairs.length=0,this.rows.length=0},e.prototype.decodeRow2pairs=function(t,e){for(var r,n=!1;!n;)try{this.pairs.push(this.retrieveNextPair(e,this.pairs,t))}catch(i){if(i instanceof vt){if(!this.pairs.length)throw new vt;n=!0}}if(this.checkChecksum())return this.pairs;if(r=!!this.rows.length,this.storeRow(t,!1),r){var o=this.checkRowsBoolean(!1);if(null!=o)return o;if(null!=(o=this.checkRowsBoolean(!0)))return o}throw new vt},e.prototype.checkRowsBoolean=function(t){if(this.rows.length>25)return this.rows.length=0,null;this.pairs.length=0,t&&(this.rows=this.rows.reverse());var e=null;try{e=this.checkRows(new Array,0)}catch(r){console.log(r)}return t&&(this.rows=this.rows.reverse()),e},e.prototype.checkRows=function(t,r){for(var n,o,i=r;i<this.rows.length;i++){var a=this.rows[i];this.pairs.length=0;try{for(var s=(n=void 0,Vr(t)),u=s.next();!u.done;u=s.next()){var c=u.value;this.pairs.push(c.getPairs())}}catch(h){n={error:h}}finally{try{u&&!u.done&&(o=s.return)&&o.call(s)}finally{if(n)throw n.error}}if(this.pairs.push(a.getPairs()),e.isValidSequence(this.pairs)){if(this.checkChecksum())return this.pairs;var f=new Array(t);f.push(a);try{return this.checkRows(f,i+1)}catch(l){console.log(l)}}}throw new vt},e.isValidSequence=function(t){var r,n;try{for(var o=Vr(e.FINDER_PATTERN_SEQUENCES),i=o.next();!i.done;i=o.next()){var a=i.value;if(!(t.length>a.length)){for(var s=!0,u=0;u<t.length;u++)if(t[u].getFinderPattern().getValue()!==a[u]){s=!1;break}if(s)return!0}}}catch(c){r={error:c}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return!1},e.prototype.storeRow=function(t,r){for(var n=0,o=!1,i=!1;n<this.rows.length;){var a=this.rows[n];if(a.getRowNumber()>t){i=a.isEquivalent(this.pairs);break}o=a.isEquivalent(this.pairs),n++}i||o||e.isPartialRow(this.pairs,this.rows)||(this.rows.push(n,new kr(this.pairs,t,r)),this.removePartialRows(this.pairs,this.rows))},e.prototype.removePartialRows=function(t,e){var r,n,o,i,a,s;try{for(var u=Vr(e),c=u.next();!c.done;c=u.next()){var f=c.value;if(f.getPairs().length!==t.length)try{for(var h=(o=void 0,Vr(f.getPairs())),l=h.next();!l.done;l=h.next()){var d=l.value;try{for(var p=(a=void 0,Vr(t)),g=p.next();!g.done;g=p.next()){var y=g.value;if(Fr.equals(d,y)){0;break}}}catch(w){a={error:w}}finally{try{g&&!g.done&&(s=p.return)&&s.call(p)}finally{if(a)throw a.error}}}}catch(v){o={error:v}}finally{try{l&&!l.done&&(i=h.return)&&i.call(h)}finally{if(o)throw o.error}}}}catch(_){r={error:_}}finally{try{c&&!c.done&&(n=u.return)&&n.call(u)}finally{if(r)throw r.error}}},e.isPartialRow=function(t,e){var r,n,o,i,a,s;try{for(var u=Vr(e),c=u.next();!c.done;c=u.next()){var f=c.value,h=!0;try{for(var l=(o=void 0,Vr(t)),d=l.next();!d.done;d=l.next()){var p=d.value,g=!1;try{for(var y=(a=void 0,Vr(f.getPairs())),w=y.next();!w.done;w=y.next()){var v=w.value;if(p.equals(v)){g=!0;break}}}catch(_){a={error:_}}finally{try{w&&!w.done&&(s=y.return)&&s.call(y)}finally{if(a)throw a.error}}if(!g){h=!1;break}}}catch(m){o={error:m}}finally{try{d&&!d.done&&(i=l.return)&&i.call(l)}finally{if(o)throw o.error}}if(h)return!0}}catch(C){r={error:C}}finally{try{c&&!c.done&&(n=u.return)&&n.call(u)}finally{if(r)throw r.error}}return!1},e.prototype.getRows=function(){return this.rows},e.constructResult=function(t){var e=function(t){try{if(t.get(1))return new _r(t);if(!t.get(2))return new Cr(t);switch(pr.extractNumericValueFromBitArray(t,1,4)){case 4:return new Tr(t);case 5:return new Rr(t)}switch(pr.extractNumericValueFromBitArray(t,1,5)){case 12:return new Dr(t);case 13:return new Pr(t)}switch(pr.extractNumericValueFromBitArray(t,1,7)){case 56:return new Lr(t,"310","11");case 57:return new Lr(t,"320","11");case 58:return new Lr(t,"310","13");case 59:return new Lr(t,"320","13");case 60:return new Lr(t,"310","15");case 61:return new Lr(t,"320","15");case 62:return new Lr(t,"310","17");case 63:return new Lr(t,"320","17")}}catch(e){throw console.log(e),new Zt("unknown decoder: "+t)}}(nr.buildBitArray(t)).parseInformation(),r=t[0].getFinderPattern().getResultPoints(),n=t[t.length-1].getFinderPattern().getResultPoints(),o=[r[0],r[1],n[0],n[1]];return new Pt(e,null,null,o,Bt.RSS_EXPANDED,null)},e.prototype.checkChecksum=function(){var t=this.pairs.get(0),e=t.getLeftChar(),r=t.getRightChar();if(null===r)return!1;for(var n=r.getChecksumPortion(),o=2,i=1;i<this.pairs.size();++i){var a=this.pairs.get(i);n+=a.getLeftChar().getChecksumPortion(),o++;var s=a.getRightChar();null!=s&&(n+=s.getChecksumPortion(),o++)}return 211*(o-4)+(n%=211)===e.getValue()},e.getNextSecondBar=function(t,e){var r;return t.get(e)?(r=t.getNextUnset(e),r=t.getNextSet(r)):(r=t.getNextSet(e),r=t.getNextUnset(r)),r},e.prototype.retrieveNextPair=function(t,r,n){var o,i=r.length%2==0;this.startFromEven&&(i=!i);var a=!0,s=-1;do{this.findNextPair(t,r,s),null===(o=this.parseFoundFinderPattern(t,n,i))?s=e.getNextSecondBar(t,this.startEnd[0]):a=!1}while(a);var u,c=this.decodeDataCharacter(t,o,i,!0);if(!this.isEmptyPair(r)&&r[r.length-1].mustBeLast())throw new vt;try{u=this.decodeDataCharacter(t,o,i,!1)}catch(f){u=null,console.log(f)}return new Fr(c,u,o,!0)},e.prototype.isEmptyPair=function(t){return 0===t.length},e.prototype.findNextPair=function(t,r,n){var o=this.getDecodeFinderCounters();o[0]=0,o[1]=0,o[2]=0,o[3]=0;var i,a=t.getSize();i=n>=0?n:this.isEmptyPair(r)?0:r[r.length-1].getFinderPattern().getStartEnd()[1];var s=r.length%2!=0;this.startFromEven&&(s=!s);for(var u=!1;i<a&&(u=!t.get(i));)i++;for(var c=0,f=i,h=i;h<a;h++)if(t.get(h)!==u)o[c]++;else{if(3===c){if(s&&e.reverseCounters(o),e.isFinderPattern(o))return this.startEnd[0]=f,void(this.startEnd[1]=h);s&&e.reverseCounters(o),f+=o[0]+o[1],o[0]=o[2],o[1]=o[3],o[2]=0,o[3]=0,c--}else c++;o[c]=1,u=!u}throw new vt},e.reverseCounters=function(t){for(var e=t.length,r=0;r<e/2;++r){var n=t[r];t[r]=t[e-r-1],t[e-r-1]=n}},e.prototype.parseFoundFinderPattern=function(t,r,n){var o,i,a;if(n){for(var s=this.startEnd[0]-1;s>=0&&!t.get(s);)s--;s++,o=this.startEnd[0]-s,i=s,a=this.startEnd[1]}else i=this.startEnd[0],o=(a=t.getNextUnset(this.startEnd[1]+1))-this.startEnd[1];var u,c=this.getDecodeFinderCounters();q.arraycopy(c,0,c,1,c.length-1),c[0]=o;try{u=this.parseFinderValue(c,e.FINDER_PATTERNS)}catch(f){return null}return new tr(u,[i,a],i,a,r)},e.prototype.decodeDataCharacter=function(t,r,n,o){for(var i=this.getDataCharacterCounters(),a=0;a<i.length;a++)i[a]=0;if(o)e.recordPatternInReverse(t,r.getStartEnd()[0],i);else{e.recordPattern(t,r.getStartEnd()[1],i);for(var s=0,u=i.length-1;s<u;s++,u--){var c=i[s];i[s]=i[u],i[u]=c}}var f=Qt.sum(new Int32Array(i))/17,h=(r.getStartEnd()[1]-r.getStartEnd()[0])/15;if(Math.abs(f-h)/h>.3)throw new vt;var l=this.getOddCounts(),d=this.getEvenCounts(),p=this.getOddRoundingErrors(),g=this.getEvenRoundingErrors();for(s=0;s<i.length;s++){var y=1*i[s]/f,w=y+.5;if(w<1){if(y<.3)throw new vt;w=1}else if(w>8){if(y>8.7)throw new vt;w=8}var v=s/2;1&s?(d[v]=w,g[v]=y-w):(l[v]=w,p[v]=y-w)}this.adjustOddEvenCounts(17);var _=4*r.getValue()+(n?0:2)+(o?0:1)-1,m=0,C=0;for(s=l.length-1;s>=0;s--){if(e.isNotA1left(r,n,o)){var A=e.WEIGHTS[_][2*s];C+=l[s]*A}m+=l[s]}var E=0;for(s=d.length-1;s>=0;s--)e.isNotA1left(r,n,o)&&(A=e.WEIGHTS[_][2*s+1],E+=d[s]*A);var I=C+E;if(1&m||m>13||m<4)throw new vt;var S=(13-m)/2,b=e.SYMBOL_WIDEST[S],T=9-b,O=rr.getRSSvalue(l,b,!0),R=rr.getRSSvalue(d,T,!1),N=e.EVEN_TOTAL_SUBSET[S],D=e.GSUM[S];return new $e(O*N+R+D,I)},e.isNotA1left=function(t,e,r){return!(0===t.getValue()&&e&&r)},e.prototype.adjustOddEvenCounts=function(t){var r=Qt.sum(new Int32Array(this.getOddCounts())),n=Qt.sum(new Int32Array(this.getEvenCounts())),o=!1,i=!1;r>13?i=!0:r<4&&(o=!0);var a=!1,s=!1;n>13?s=!0:n<4&&(a=!0);var u=r+n-t,c=!(1&~r),f=!(1&n);if(1===u)if(c){if(f)throw new vt;i=!0}else{if(!f)throw new vt;s=!0}else if(-1===u)if(c){if(f)throw new vt;o=!0}else{if(!f)throw new vt;a=!0}else{if(0!==u)throw new vt;if(c){if(!f)throw new vt;r<n?(o=!0,s=!0):(i=!0,a=!0)}else if(f)throw new vt}if(o){if(i)throw new vt;e.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(i&&e.decrement(this.getOddCounts(),this.getOddRoundingErrors()),a){if(s)throw new vt;e.increment(this.getEvenCounts(),this.getOddRoundingErrors())}s&&e.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())},e.SYMBOL_WIDEST=[7,5,4,3,1],e.EVEN_TOTAL_SUBSET=[4,20,52,104,204],e.GSUM=[0,348,1388,2948,3988],e.FINDER_PATTERNS=[Int32Array.from([1,8,4,1]),Int32Array.from([3,6,4,1]),Int32Array.from([3,4,6,1]),Int32Array.from([3,2,8,1]),Int32Array.from([2,6,5,1]),Int32Array.from([2,2,9,1])],e.WEIGHTS=[[1,3,9,27,81,32,96,77],[20,60,180,118,143,7,21,63],[189,145,13,39,117,140,209,205],[193,157,49,147,19,57,171,91],[62,186,136,197,169,85,44,132],[185,133,188,142,4,12,36,108],[113,128,173,97,80,29,87,50],[150,28,84,41,123,158,52,156],[46,138,203,187,139,206,196,166],[76,17,51,153,37,111,122,155],[43,129,176,106,107,110,119,146],[16,48,144,10,30,90,59,177],[109,116,137,200,178,112,125,164],[70,210,208,202,184,130,179,115],[134,191,151,31,93,68,204,190],[148,22,66,198,172,94,71,2],[6,18,54,162,64,192,154,40],[120,149,25,75,14,42,126,167],[79,26,78,23,69,207,199,175],[103,98,83,38,114,131,182,124],[161,61,183,127,170,88,53,159],[55,165,73,8,24,72,5,15],[45,135,194,160,58,174,100,89]],e.FINDER_PAT_A=0,e.FINDER_PAT_B=1,e.FINDER_PAT_C=2,e.FINDER_PAT_D=3,e.FINDER_PAT_E=4,e.FINDER_PAT_F=5,e.FINDER_PATTERN_SEQUENCES=[[e.FINDER_PAT_A,e.FINDER_PAT_A],[e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B],[e.FINDER_PAT_A,e.FINDER_PAT_C,e.FINDER_PAT_B,e.FINDER_PAT_D],[e.FINDER_PAT_A,e.FINDER_PAT_E,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_C],[e.FINDER_PAT_A,e.FINDER_PAT_E,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_D,e.FINDER_PAT_F],[e.FINDER_PAT_A,e.FINDER_PAT_E,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_D],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_E],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F]],e.MAX_PAIRS=11,e}(Je),Hr=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Gr=function(t){function e(e,r,n){var o=t.call(this,e,r)||this;return o.count=0,o.finderPattern=n,o}return Hr(e,t),e.prototype.getFinderPattern=function(){return this.finderPattern},e.prototype.getCount=function(){return this.count},e.prototype.incrementCount=function(){this.count++},e}($e),Xr=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Wr=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},jr=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.possibleLeftPairs=[],e.possibleRightPairs=[],e}return Xr(e,t),e.prototype.decodeRow=function(t,r,n){var o,i,a,s,u=this.decodePair(r,!1,t,n);e.addOrTally(this.possibleLeftPairs,u),r.reverse();var c=this.decodePair(r,!0,t,n);e.addOrTally(this.possibleRightPairs,c),r.reverse();try{for(var f=Wr(this.possibleLeftPairs),h=f.next();!h.done;h=f.next()){var l=h.value;if(l.getCount()>1)try{for(var d=(a=void 0,Wr(this.possibleRightPairs)),p=d.next();!p.done;p=d.next()){var g=p.value;if(g.getCount()>1&&e.checkChecksum(l,g))return e.constructResult(l,g)}}catch(y){a={error:y}}finally{try{p&&!p.done&&(s=d.return)&&s.call(d)}finally{if(a)throw a.error}}}}catch(w){o={error:w}}finally{try{h&&!h.done&&(i=f.return)&&i.call(f)}finally{if(o)throw o.error}}throw new vt},e.addOrTally=function(t,e){var r,n;if(null!=e){var o=!1;try{for(var i=Wr(t),a=i.next();!a.done;a=i.next()){var s=a.value;if(s.getValue()===e.getValue()){s.incrementCount(),o=!0;break}}}catch(u){r={error:u}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}o||t.push(e)}},e.prototype.reset=function(){this.possibleLeftPairs.length=0,this.possibleRightPairs.length=0},e.constructResult=function(t,e){for(var r=4537077*t.getValue()+e.getValue(),n=new String(r).toString(),o=new gt,i=13-n.length;i>0;i--)o.append("0");o.append(n);var a=0;for(i=0;i<13;i++){var s=o.charAt(i).charCodeAt(0)-"0".charCodeAt(0);a+=1&i?s:3*s}10==(a=10-a%10)&&(a=0),o.append(a.toString());var u=t.getFinderPattern().getResultPoints(),c=e.getFinderPattern().getResultPoints();return new Pt(o.toString(),null,0,[u[0],u[1],c[0],c[1]],Bt.RSS_14,(new Date).getTime())},e.checkChecksum=function(t,e){var r=(t.getChecksumPortion()+16*e.getChecksumPortion())%79,n=9*t.getFinderPattern().getValue()+e.getFinderPattern().getValue();return n>72&&n--,n>8&&n--,r===n},e.prototype.decodePair=function(t,e,r,n){try{var o=this.findFinderPattern(t,e),i=this.parseFoundFinderPattern(t,r,e,o),a=null==n?null:n.get(k.NEED_RESULT_POINT_CALLBACK);if(null!=a){var s=(o[0]+o[1])/2;e&&(s=t.getSize()-1-s),a.foundPossibleResultPoint(new $t(s,r))}var u=this.decodeDataCharacter(t,i,!0),c=this.decodeDataCharacter(t,i,!1);return new Gr(1597*u.getValue()+c.getValue(),u.getChecksumPortion()+4*c.getChecksumPortion(),i)}catch(f){return null}},e.prototype.decodeDataCharacter=function(t,r,n){for(var o=this.getDataCharacterCounters(),i=0;i<o.length;i++)o[i]=0;if(n)de.recordPatternInReverse(t,r.getStartEnd()[0],o);else{de.recordPattern(t,r.getStartEnd()[1]+1,o);for(var a=0,s=o.length-1;a<s;a++,s--){var u=o[a];o[a]=o[s],o[s]=u}}var c=n?16:15,f=Qt.sum(new Int32Array(o))/c,h=this.getOddCounts(),l=this.getEvenCounts(),d=this.getOddRoundingErrors(),p=this.getEvenRoundingErrors();for(a=0;a<o.length;a++){var g=o[a]/f,y=Math.floor(g+.5);y<1?y=1:y>8&&(y=8);var w=Math.floor(a/2);1&a?(l[w]=y,p[w]=g-y):(h[w]=y,d[w]=g-y)}this.adjustOddEvenCounts(n,c);var v=0,_=0;for(a=h.length-1;a>=0;a--)_*=9,_+=h[a],v+=h[a];var m=0,C=0;for(a=l.length-1;a>=0;a--)m*=9,m+=l[a],C+=l[a];var A=_+3*m;if(n){if(1&v||v>12||v<4)throw new vt;var E=(12-v)/2,I=9-(R=e.OUTSIDE_ODD_WIDEST[E]),S=rr.getRSSvalue(h,R,!1),b=rr.getRSSvalue(l,I,!0),T=e.OUTSIDE_EVEN_TOTAL_SUBSET[E],O=e.OUTSIDE_GSUM[E];return new $e(S*T+b+O,A)}if(1&C||C>10||C<4)throw new vt;E=(10-C)/2,I=9-(R=e.INSIDE_ODD_WIDEST[E]),S=rr.getRSSvalue(h,R,!0),b=rr.getRSSvalue(l,I,!1);var R,N=e.INSIDE_ODD_TOTAL_SUBSET[E];return O=e.INSIDE_GSUM[E],new $e(b*N+S+O,A)},e.prototype.findFinderPattern=function(t,e){var r=this.getDecodeFinderCounters();r[0]=0,r[1]=0,r[2]=0,r[3]=0;for(var n=t.getSize(),o=!1,i=0;i<n&&e!==(o=!t.get(i));)i++;for(var a=0,s=i,u=i;u<n;u++)if(t.get(u)!==o)r[a]++;else{if(3===a){if(Je.isFinderPattern(r))return[s,u];s+=r[0]+r[1],r[0]=r[2],r[1]=r[3],r[2]=0,r[3]=0,a--}else a++;r[a]=1,o=!o}throw new vt},e.prototype.parseFoundFinderPattern=function(t,r,n,o){for(var i=t.get(o[0]),a=o[0]-1;a>=0&&i!==t.get(a);)a--;a++;var s=o[0]-a,u=this.getDecodeFinderCounters(),c=new Int32Array(u.length);q.arraycopy(u,0,c,1,u.length-1),c[0]=s;var f=this.parseFinderValue(c,e.FINDER_PATTERNS),h=a,l=o[1];return n&&(h=t.getSize()-1-h,l=t.getSize()-1-l),new tr(f,[a,o[1]],h,l,r)},e.prototype.adjustOddEvenCounts=function(t,e){var r=Qt.sum(new Int32Array(this.getOddCounts())),n=Qt.sum(new Int32Array(this.getEvenCounts())),o=!1,i=!1,a=!1,s=!1;t?(r>12?i=!0:r<4&&(o=!0),n>12?s=!0:n<4&&(a=!0)):(r>11?i=!0:r<5&&(o=!0),n>10?s=!0:n<4&&(a=!0));var u=r+n-e,c=(1&r)==(t?1:0),f=!(1&~n);if(1===u)if(c){if(f)throw new vt;i=!0}else{if(!f)throw new vt;s=!0}else if(-1===u)if(c){if(f)throw new vt;o=!0}else{if(!f)throw new vt;a=!0}else{if(0!==u)throw new vt;if(c){if(!f)throw new vt;r<n?(o=!0,s=!0):(i=!0,a=!0)}else if(f)throw new vt}if(o){if(i)throw new vt;Je.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(i&&Je.decrement(this.getOddCounts(),this.getOddRoundingErrors()),a){if(s)throw new vt;Je.increment(this.getEvenCounts(),this.getOddRoundingErrors())}s&&Je.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())},e.OUTSIDE_EVEN_TOTAL_SUBSET=[1,10,34,70,126],e.INSIDE_ODD_TOTAL_SUBSET=[4,20,48,81],e.OUTSIDE_GSUM=[0,161,961,2015,2715],e.INSIDE_GSUM=[0,336,1036,1516],e.OUTSIDE_ODD_WIDEST=[8,6,4,3,1],e.INSIDE_ODD_WIDEST=[2,4,6,8],e.FINDER_PATTERNS=[Int32Array.from([3,8,2,1]),Int32Array.from([3,5,5,1]),Int32Array.from([3,3,7,1]),Int32Array.from([3,1,9,1]),Int32Array.from([2,7,4,1]),Int32Array.from([2,5,6,1]),Int32Array.from([2,3,8,1]),Int32Array.from([1,5,7,1]),Int32Array.from([1,3,9,1])],e}(Je),zr=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Yr=function(t){function e(e){var r=t.call(this)||this;r.readers=[];var n=e?e.get(k.POSSIBLE_FORMATS):null,o=e&&void 0!==e.get(k.ASSUME_CODE_39_CHECK_DIGIT);return n&&((n.includes(Bt.EAN_13)||n.includes(Bt.UPC_A)||n.includes(Bt.EAN_8)||n.includes(Bt.UPC_E))&&r.readers.push(new Ye(e)),n.includes(Bt.CODE_39)&&r.readers.push(new ve(o)),n.includes(Bt.CODE_93)&&r.readers.push(new Ce),n.includes(Bt.CODE_128)&&r.readers.push(new ge),n.includes(Bt.ITF)&&r.readers.push(new Ie),n.includes(Bt.CODABAR)&&r.readers.push(new Ke),n.includes(Bt.RSS_14)&&r.readers.push(new jr),n.includes(Bt.RSS_EXPANDED)&&(console.warn("RSS Expanded reader IS NOT ready for production yet! use at your own risk."),r.readers.push(new Ur))),0===r.readers.length&&(r.readers.push(new Ye(e)),r.readers.push(new ve),r.readers.push(new Ce),r.readers.push(new Ye(e)),r.readers.push(new ge),r.readers.push(new Ie),r.readers.push(new jr)),r}return zr(e,t),e.prototype.decodeRow=function(t,e,r){for(var n=0;n<this.readers.length;n++)try{return this.readers[n].decodeRow(t,e,r)}catch(o){}throw new vt},e.prototype.reset=function(){this.readers.forEach(function(t){return t.reset()})},e}(de),Zr=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();!function(t){function e(e,r){return void 0===e&&(e=500),t.call(this,new Yr(r),e,r)||this}Zr(e,t)}(Mt);var Kr,qr=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Qr=function(){function t(t,e,r){this.ecCodewords=t,this.ecBlocks=[e],r&&this.ecBlocks.push(r)}return t.prototype.getECCodewords=function(){return this.ecCodewords},t.prototype.getECBlocks=function(){return this.ecBlocks},t}(),Jr=function(){function t(t,e){this.count=t,this.dataCodewords=e}return t.prototype.getCount=function(){return this.count},t.prototype.getDataCodewords=function(){return this.dataCodewords},t}(),$r=function(){function t(t,e,r,n,o,i){var a,s;this.versionNumber=t,this.symbolSizeRows=e,this.symbolSizeColumns=r,this.dataRegionSizeRows=n,this.dataRegionSizeColumns=o,this.ecBlocks=i;var u=0,c=i.getECCodewords(),f=i.getECBlocks();try{for(var h=qr(f),l=h.next();!l.done;l=h.next()){var d=l.value;u+=d.getCount()*(d.getDataCodewords()+c)}}catch(p){a={error:p}}finally{try{l&&!l.done&&(s=h.return)&&s.call(h)}finally{if(a)throw a.error}}this.totalCodewords=u}return t.prototype.getVersionNumber=function(){return this.versionNumber},t.prototype.getSymbolSizeRows=function(){return this.symbolSizeRows},t.prototype.getSymbolSizeColumns=function(){return this.symbolSizeColumns},t.prototype.getDataRegionSizeRows=function(){return this.dataRegionSizeRows},t.prototype.getDataRegionSizeColumns=function(){return this.dataRegionSizeColumns},t.prototype.getTotalCodewords=function(){return this.totalCodewords},t.prototype.getECBlocks=function(){return this.ecBlocks},t.getVersionForDimensions=function(e,r){var n,o;if(1&e||1&r)throw new st;try{for(var i=qr(t.VERSIONS),a=i.next();!a.done;a=i.next()){var s=a.value;if(s.symbolSizeRows===e&&s.symbolSizeColumns===r)return s}}catch(u){n={error:u}}finally{try{a&&!a.done&&(o=i.return)&&o.call(i)}finally{if(n)throw n.error}}throw new st},t.prototype.toString=function(){return""+this.versionNumber},t.buildVersions=function(){return[new t(1,10,10,8,8,new Qr(5,new Jr(1,3))),new t(2,12,12,10,10,new Qr(7,new Jr(1,5))),new t(3,14,14,12,12,new Qr(10,new Jr(1,8))),new t(4,16,16,14,14,new Qr(12,new Jr(1,12))),new t(5,18,18,16,16,new Qr(14,new Jr(1,18))),new t(6,20,20,18,18,new Qr(18,new Jr(1,22))),new t(7,22,22,20,20,new Qr(20,new Jr(1,30))),new t(8,24,24,22,22,new Qr(24,new Jr(1,36))),new t(9,26,26,24,24,new Qr(28,new Jr(1,44))),new t(10,32,32,14,14,new Qr(36,new Jr(1,62))),new t(11,36,36,16,16,new Qr(42,new Jr(1,86))),new t(12,40,40,18,18,new Qr(48,new Jr(1,114))),new t(13,44,44,20,20,new Qr(56,new Jr(1,144))),new t(14,48,48,22,22,new Qr(68,new Jr(1,174))),new t(15,52,52,24,24,new Qr(42,new Jr(2,102))),new t(16,64,64,14,14,new Qr(56,new Jr(2,140))),new t(17,72,72,16,16,new Qr(36,new Jr(4,92))),new t(18,80,80,18,18,new Qr(48,new Jr(4,114))),new t(19,88,88,20,20,new Qr(56,new Jr(4,144))),new t(20,96,96,22,22,new Qr(68,new Jr(4,174))),new t(21,104,104,24,24,new Qr(56,new Jr(6,136))),new t(22,120,120,18,18,new Qr(68,new Jr(6,175))),new t(23,132,132,20,20,new Qr(62,new Jr(8,163))),new t(24,144,144,22,22,new Qr(62,new Jr(8,156),new Jr(2,155))),new t(25,8,18,6,16,new Qr(7,new Jr(1,5))),new t(26,8,32,6,14,new Qr(11,new Jr(1,10))),new t(27,12,26,10,24,new Qr(14,new Jr(1,16))),new t(28,12,36,10,16,new Qr(18,new Jr(1,22))),new t(29,16,36,14,16,new Qr(24,new Jr(1,32))),new t(30,16,48,14,22,new Qr(28,new Jr(1,49)))]},t.VERSIONS=t.buildVersions(),t}(),tn=function(){function t(e){var r=e.getHeight();if(r<8||r>144||1&r)throw new st;this.version=t.readVersion(e),this.mappingBitMatrix=this.extractDataRegion(e),this.readMappingMatrix=new yt(this.mappingBitMatrix.getWidth(),this.mappingBitMatrix.getHeight())}return t.prototype.getVersion=function(){return this.version},t.readVersion=function(t){var e=t.getHeight(),r=t.getWidth();return $r.getVersionForDimensions(e,r)},t.prototype.readCodewords=function(){var t=new Int8Array(this.version.getTotalCodewords()),e=0,r=4,n=0,o=this.mappingBitMatrix.getHeight(),i=this.mappingBitMatrix.getWidth(),a=!1,s=!1,u=!1,c=!1;do{if(r!==o||0!==n||a)if(r===o-2&&0===n&&3&i&&!s)t[e++]=255&this.readCorner2(o,i),r-=2,n+=2,s=!0;else if(r!==o+4||2!==n||7&i||u)if(r!==o-2||0!==n||4!=(7&i)||c){do{r<o&&n>=0&&!this.readMappingMatrix.get(n,r)&&(t[e++]=255&this.readUtah(r,n,o,i)),r-=2,n+=2}while(r>=0&&n<i);r+=1,n+=3;do{r>=0&&n<i&&!this.readMappingMatrix.get(n,r)&&(t[e++]=255&this.readUtah(r,n,o,i)),r+=2,n-=2}while(r<o&&n>=0);r+=3,n+=1}else t[e++]=255&this.readCorner4(o,i),r-=2,n+=2,c=!0;else t[e++]=255&this.readCorner3(o,i),r-=2,n+=2,u=!0;else t[e++]=255&this.readCorner1(o,i),r-=2,n+=2,a=!0}while(r<o||n<i);if(e!==this.version.getTotalCodewords())throw new st;return t},t.prototype.readModule=function(t,e,r,n){return t<0&&(t+=r,e+=4-(r+4&7)),e<0&&(e+=n,t+=4-(n+4&7)),this.readMappingMatrix.set(e,t),this.mappingBitMatrix.get(e,t)},t.prototype.readUtah=function(t,e,r,n){var o=0;return this.readModule(t-2,e-2,r,n)&&(o|=1),o<<=1,this.readModule(t-2,e-1,r,n)&&(o|=1),o<<=1,this.readModule(t-1,e-2,r,n)&&(o|=1),o<<=1,this.readModule(t-1,e-1,r,n)&&(o|=1),o<<=1,this.readModule(t-1,e,r,n)&&(o|=1),o<<=1,this.readModule(t,e-2,r,n)&&(o|=1),o<<=1,this.readModule(t,e-1,r,n)&&(o|=1),o<<=1,this.readModule(t,e,r,n)&&(o|=1),o},t.prototype.readCorner1=function(t,e){var r=0;return this.readModule(t-1,0,t,e)&&(r|=1),r<<=1,this.readModule(t-1,1,t,e)&&(r|=1),r<<=1,this.readModule(t-1,2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-1,t,e)&&(r|=1),r<<=1,this.readModule(1,e-1,t,e)&&(r|=1),r<<=1,this.readModule(2,e-1,t,e)&&(r|=1),r<<=1,this.readModule(3,e-1,t,e)&&(r|=1),r},t.prototype.readCorner2=function(t,e){var r=0;return this.readModule(t-3,0,t,e)&&(r|=1),r<<=1,this.readModule(t-2,0,t,e)&&(r|=1),r<<=1,this.readModule(t-1,0,t,e)&&(r|=1),r<<=1,this.readModule(0,e-4,t,e)&&(r|=1),r<<=1,this.readModule(0,e-3,t,e)&&(r|=1),r<<=1,this.readModule(0,e-2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-1,t,e)&&(r|=1),r<<=1,this.readModule(1,e-1,t,e)&&(r|=1),r},t.prototype.readCorner3=function(t,e){var r=0;return this.readModule(t-1,0,t,e)&&(r|=1),r<<=1,this.readModule(t-1,e-1,t,e)&&(r|=1),r<<=1,this.readModule(0,e-3,t,e)&&(r|=1),r<<=1,this.readModule(0,e-2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-1,t,e)&&(r|=1),r<<=1,this.readModule(1,e-3,t,e)&&(r|=1),r<<=1,this.readModule(1,e-2,t,e)&&(r|=1),r<<=1,this.readModule(1,e-1,t,e)&&(r|=1),r},t.prototype.readCorner4=function(t,e){var r=0;return this.readModule(t-3,0,t,e)&&(r|=1),r<<=1,this.readModule(t-2,0,t,e)&&(r|=1),r<<=1,this.readModule(t-1,0,t,e)&&(r|=1),r<<=1,this.readModule(0,e-2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-1,t,e)&&(r|=1),r<<=1,this.readModule(1,e-1,t,e)&&(r|=1),r<<=1,this.readModule(2,e-1,t,e)&&(r|=1),r<<=1,this.readModule(3,e-1,t,e)&&(r|=1),r},t.prototype.extractDataRegion=function(t){var e=this.version.getSymbolSizeRows(),r=this.version.getSymbolSizeColumns();if(t.getHeight()!==e)throw new j("Dimension of bitMatrix must match the version size");for(var n=this.version.getDataRegionSizeRows(),o=this.version.getDataRegionSizeColumns(),i=e/n|0,a=r/o|0,s=new yt(a*o,i*n),u=0;u<i;++u)for(var c=u*n,f=0;f<a;++f)for(var h=f*o,l=0;l<n;++l)for(var d=u*(n+2)+1+l,p=c+l,g=0;g<o;++g){var y=f*(o+2)+1+g;if(t.get(y,d)){var w=h+g;s.set(w,p)}}return s},t}(),en=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},rn=function(){function t(t,e){this.numDataCodewords=t,this.codewords=e}return t.getDataBlocks=function(e,r){var n,o,i,a,s=r.getECBlocks(),u=0,c=s.getECBlocks();try{for(var f=en(c),h=f.next();!h.done;h=f.next())u+=(y=h.value).getCount()}catch(R){n={error:R}}finally{try{h&&!h.done&&(o=f.return)&&o.call(f)}finally{if(n)throw n.error}}var l=new Array(u),d=0;try{for(var p=en(c),g=p.next();!g.done;g=p.next())for(var y=g.value,w=0;w<y.getCount();w++){var v=y.getDataCodewords(),_=s.getECCodewords()+v;l[d++]=new t(v,new Uint8Array(_))}}catch(N){i={error:N}}finally{try{g&&!g.done&&(a=p.return)&&a.call(p)}finally{if(i)throw i.error}}var m=l[0].codewords.length-s.getECCodewords(),C=m-1,A=0;for(w=0;w<C;w++)for(var E=0;E<d;E++)l[E].codewords[w]=e[A++];var I=24===r.getVersionNumber(),S=I?8:d;for(E=0;E<S;E++)l[E].codewords[m-1]=e[A++];var b=l[0].codewords.length;for(w=m;w<b;w++)for(E=0;E<d;E++){var T=I?(E+8)%d:E,O=I&&T>7?w-1:w;l[T].codewords[O]=e[A++]}if(A!==e.length)throw new j;return l},t.prototype.getNumDataCodewords=function(){return this.numDataCodewords},t.prototype.getCodewords=function(){return this.codewords},t}(),nn=function(){function t(t){this.bytes=t,this.byteOffset=0,this.bitOffset=0}return t.prototype.getBitOffset=function(){return this.bitOffset},t.prototype.getByteOffset=function(){return this.byteOffset},t.prototype.readBits=function(t){if(t<1||t>32||t>this.available())throw new j(""+t);var e=0,r=this.bitOffset,n=this.byteOffset,o=this.bytes;if(r>0){var i=8-r,a=t<i?t:i,s=255>>8-a<<(u=i-a);e=(o[n]&s)>>u,t-=a,8===(r+=a)&&(r=0,n++)}if(t>0){for(;t>=8;)e=e<<8|255&o[n],n++,t-=8;var u;if(t>0)s=255>>(u=8-t)<<u,e=e<<t|(o[n]&s)>>u,r+=t}return this.bitOffset=r,this.byteOffset=n,e},t.prototype.available=function(){return 8*(this.bytes.length-this.byteOffset)-this.bitOffset},t}();!function(t){t[t.PAD_ENCODE=0]="PAD_ENCODE",t[t.ASCII_ENCODE=1]="ASCII_ENCODE",t[t.C40_ENCODE=2]="C40_ENCODE",t[t.TEXT_ENCODE=3]="TEXT_ENCODE",t[t.ANSIX12_ENCODE=4]="ANSIX12_ENCODE",t[t.EDIFACT_ENCODE=5]="EDIFACT_ENCODE",t[t.BASE256_ENCODE=6]="BASE256_ENCODE"}(Kr||(Kr={}));var on,an=function(){function t(){}return t.decode=function(t){var e=new nn(t),r=new gt,n=new gt,o=new Array,i=Kr.ASCII_ENCODE;do{if(i===Kr.ASCII_ENCODE)i=this.decodeAsciiSegment(e,r,n);else{switch(i){case Kr.C40_ENCODE:this.decodeC40Segment(e,r);break;case Kr.TEXT_ENCODE:this.decodeTextSegment(e,r);break;case Kr.ANSIX12_ENCODE:this.decodeAnsiX12Segment(e,r);break;case Kr.EDIFACT_ENCODE:this.decodeEdifactSegment(e,r);break;case Kr.BASE256_ENCODE:this.decodeBase256Segment(e,r,o);break;default:throw new st}i=Kr.ASCII_ENCODE}}while(i!==Kr.PAD_ENCODE&&e.available()>0);return n.length()>0&&r.append(n.toString()),new xt(t,r.toString(),0===o.length?null:o,null)},t.decodeAsciiSegment=function(t,e,r){var n=!1;do{var o=t.readBits(8);if(0===o)throw new st;if(o<=128)return n&&(o+=128),e.append(String.fromCharCode(o-1)),Kr.ASCII_ENCODE;if(129===o)return Kr.PAD_ENCODE;if(o<=229){var i=o-130;i<10&&e.append("0"),e.append(""+i)}else switch(o){case 230:return Kr.C40_ENCODE;case 231:return Kr.BASE256_ENCODE;case 232:e.append(String.fromCharCode(29));break;case 233:case 234:case 241:break;case 235:n=!0;break;case 236:e.append("[)>05"),r.insert(0,"");break;case 237:e.append("[)>06"),r.insert(0,"");break;case 238:return Kr.ANSIX12_ENCODE;case 239:return Kr.TEXT_ENCODE;case 240:return Kr.EDIFACT_ENCODE;default:if(254!==o||0!==t.available())throw new st}}while(t.available()>0);return Kr.ASCII_ENCODE},t.decodeC40Segment=function(t,e){var r=!1,n=[],o=0;do{if(8===t.available())return;var i=t.readBits(8);if(254===i)return;this.parseTwoBytes(i,t.readBits(8),n);for(var a=0;a<3;a++){var s=n[a];switch(o){case 0:if(s<3)o=s+1;else{if(!(s<this.C40_BASIC_SET_CHARS.length))throw new st;var u=this.C40_BASIC_SET_CHARS[s];r?(e.append(String.fromCharCode(u.charCodeAt(0)+128)),r=!1):e.append(u)}break;case 1:r?(e.append(String.fromCharCode(s+128)),r=!1):e.append(String.fromCharCode(s)),o=0;break;case 2:if(s<this.C40_SHIFT2_SET_CHARS.length)u=this.C40_SHIFT2_SET_CHARS[s],r?(e.append(String.fromCharCode(u.charCodeAt(0)+128)),r=!1):e.append(u);else switch(s){case 27:e.append(String.fromCharCode(29));break;case 30:r=!0;break;default:throw new st}o=0;break;case 3:r?(e.append(String.fromCharCode(s+224)),r=!1):e.append(String.fromCharCode(s+96)),o=0;break;default:throw new st}}}while(t.available()>0)},t.decodeTextSegment=function(t,e){var r=!1,n=[],o=0;do{if(8===t.available())return;var i=t.readBits(8);if(254===i)return;this.parseTwoBytes(i,t.readBits(8),n);for(var a=0;a<3;a++){var s=n[a];switch(o){case 0:if(s<3)o=s+1;else{if(!(s<this.TEXT_BASIC_SET_CHARS.length))throw new st;var u=this.TEXT_BASIC_SET_CHARS[s];r?(e.append(String.fromCharCode(u.charCodeAt(0)+128)),r=!1):e.append(u)}break;case 1:r?(e.append(String.fromCharCode(s+128)),r=!1):e.append(String.fromCharCode(s)),o=0;break;case 2:if(s<this.TEXT_SHIFT2_SET_CHARS.length)u=this.TEXT_SHIFT2_SET_CHARS[s],r?(e.append(String.fromCharCode(u.charCodeAt(0)+128)),r=!1):e.append(u);else switch(s){case 27:e.append(String.fromCharCode(29));break;case 30:r=!0;break;default:throw new st}o=0;break;case 3:if(!(s<this.TEXT_SHIFT3_SET_CHARS.length))throw new st;u=this.TEXT_SHIFT3_SET_CHARS[s],r?(e.append(String.fromCharCode(u.charCodeAt(0)+128)),r=!1):e.append(u),o=0;break;default:throw new st}}}while(t.available()>0)},t.decodeAnsiX12Segment=function(t,e){var r=[];do{if(8===t.available())return;var n=t.readBits(8);if(254===n)return;this.parseTwoBytes(n,t.readBits(8),r);for(var o=0;o<3;o++){var i=r[o];switch(i){case 0:e.append("\r");break;case 1:e.append("*");break;case 2:e.append(">");break;case 3:e.append(" ");break;default:if(i<14)e.append(String.fromCharCode(i+44));else{if(!(i<40))throw new st;e.append(String.fromCharCode(i+51))}}}}while(t.available()>0)},t.parseTwoBytes=function(t,e,r){var n=(t<<8)+e-1,o=Math.floor(n/1600);r[0]=o,n-=1600*o,o=Math.floor(n/40),r[1]=o,r[2]=n-40*o},t.decodeEdifactSegment=function(t,e){do{if(t.available()<=16)return;for(var r=0;r<4;r++){var n=t.readBits(6);if(31===n){var o=8-t.getBitOffset();return void(8!==o&&t.readBits(o))}32&n||(n|=64),e.append(String.fromCharCode(n))}}while(t.available()>0)},t.decodeBase256Segment=function(t,e,r){var n,o=1+t.getByteOffset(),i=this.unrandomize255State(t.readBits(8),o++);if((n=0===i?t.available()/8|0:i<250?i:250*(i-249)+this.unrandomize255State(t.readBits(8),o++))<0)throw new st;for(var a=new Uint8Array(n),s=0;s<n;s++){if(t.available()<8)throw new st;a[s]=this.unrandomize255State(t.readBits(8),o++)}r.push(a);try{e.append(dt.decode(a,pt.ISO88591))}catch(u){throw new Zt("Platform does not support required encoding: "+u.message)}},t.unrandomize255State=function(t,e){var r=t-(149*e%255+1);return r>=0?r:r+256},t.C40_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],t.C40_SHIFT2_SET_CHARS=["!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","@","[","\\","]","^","_"],t.TEXT_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"],t.TEXT_SHIFT2_SET_CHARS=t.C40_SHIFT2_SET_CHARS,t.TEXT_SHIFT3_SET_CHARS=["`","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","{","|","}","~",String.fromCharCode(127)],t}(),sn=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},un=function(){function t(){this.rsDecoder=new Kt(Wt.DATA_MATRIX_FIELD_256)}return t.prototype.decode=function(t){var e,r,n=new tn(t),o=n.getVersion(),i=n.readCodewords(),a=rn.getDataBlocks(i,o),s=0;try{for(var u=sn(a),c=u.next();!c.done;c=u.next())s+=c.value.getNumDataCodewords()}catch(w){e={error:w}}finally{try{c&&!c.done&&(r=u.return)&&r.call(u)}finally{if(e)throw e.error}}for(var f=new Uint8Array(s),h=a.length,l=0;l<h;l++){var d=a[l],p=d.getCodewords(),g=d.getNumDataCodewords();this.correctErrors(p,g);for(var y=0;y<g;y++)f[y*h+l]=p[y]}return an.decode(f)},t.prototype.correctErrors=function(t,e){var r=new Int32Array(t);try{this.rsDecoder.decode(r,t.length-e)}catch(o){throw new Z}for(var n=0;n<e;n++)t[n]=r[n]},t}(),cn=function(){function t(t){this.image=t,this.rectangleDetector=new ne(this.image)}return t.prototype.detect=function(){var e=this.rectangleDetector.detect(),r=this.detectSolid1(e);if((r=this.detectSolid2(r))[3]=this.correctTopRight(r),!r[3])throw new vt;var n=(r=this.shiftToModuleCenter(r))[0],o=r[1],i=r[2],a=r[3],s=this.transitionsBetween(n,a)+1,u=this.transitionsBetween(i,a)+1;1&~s||(s+=1),1&~u||(u+=1),4*s<7*u&&4*u<7*s&&(s=u=Math.max(s,u));var c=t.sampleGrid(this.image,n,o,i,a,s,u);return new te(c,[n,o,i,a])},t.shiftPoint=function(t,e,r){var n=(e.getX()-t.getX())/(r+1),o=(e.getY()-t.getY())/(r+1);return new $t(t.getX()+n,t.getY()+o)},t.moveAway=function(t,e,r){var n=t.getX(),o=t.getY();return n<e?n-=1:n+=1,o<r?o-=1:o+=1,new $t(n,o)},t.prototype.detectSolid1=function(t){var e=t[0],r=t[1],n=t[3],o=t[2],i=this.transitionsBetween(e,r),a=this.transitionsBetween(r,n),s=this.transitionsBetween(n,o),u=this.transitionsBetween(o,e),c=i,f=[o,e,r,n];return c>a&&(c=a,f[0]=e,f[1]=r,f[2]=n,f[3]=o),c>s&&(c=s,f[0]=r,f[1]=n,f[2]=o,f[3]=e),c>u&&(f[0]=n,f[1]=o,f[2]=e,f[3]=r),f},t.prototype.detectSolid2=function(e){var r=e[0],n=e[1],o=e[2],i=e[3],a=this.transitionsBetween(r,i),s=t.shiftPoint(n,o,4*(a+1)),u=t.shiftPoint(o,n,4*(a+1));return this.transitionsBetween(s,r)<this.transitionsBetween(u,i)?(e[0]=r,e[1]=n,e[2]=o,e[3]=i):(e[0]=n,e[1]=o,e[2]=i,e[3]=r),e},t.prototype.correctTopRight=function(e){var r=e[0],n=e[1],o=e[2],i=e[3],a=this.transitionsBetween(r,i),s=this.transitionsBetween(n,i),u=t.shiftPoint(r,n,4*(s+1)),c=t.shiftPoint(o,n,4*(a+1));a=this.transitionsBetween(u,i),s=this.transitionsBetween(c,i);var f=new $t(i.getX()+(o.getX()-n.getX())/(a+1),i.getY()+(o.getY()-n.getY())/(a+1)),h=new $t(i.getX()+(r.getX()-n.getX())/(s+1),i.getY()+(r.getY()-n.getY())/(s+1));return this.isValid(f)?this.isValid(h)?this.transitionsBetween(u,f)+this.transitionsBetween(c,f)>this.transitionsBetween(u,h)+this.transitionsBetween(c,h)?f:h:f:this.isValid(h)?h:null},t.prototype.shiftToModuleCenter=function(e){var r=e[0],n=e[1],o=e[2],i=e[3],a=this.transitionsBetween(r,i)+1,s=this.transitionsBetween(o,i)+1,u=t.shiftPoint(r,n,4*s),c=t.shiftPoint(o,n,4*a);1&~(a=this.transitionsBetween(u,i)+1)||(a+=1),1&~(s=this.transitionsBetween(c,i)+1)||(s+=1);var f,h,l=(r.getX()+n.getX()+o.getX()+i.getX())/4,d=(r.getY()+n.getY()+o.getY()+i.getY())/4;return r=t.moveAway(r,l,d),n=t.moveAway(n,l,d),o=t.moveAway(o,l,d),i=t.moveAway(i,l,d),u=t.shiftPoint(r,n,4*s),u=t.shiftPoint(u,i,4*a),f=t.shiftPoint(n,r,4*s),f=t.shiftPoint(f,o,4*a),c=t.shiftPoint(o,i,4*s),c=t.shiftPoint(c,n,4*a),h=t.shiftPoint(i,o,4*s),[u,f,c,h=t.shiftPoint(h,r,4*a)]},t.prototype.isValid=function(t){return t.getX()>=0&&t.getX()<this.image.getWidth()&&t.getY()>0&&t.getY()<this.image.getHeight()},t.sampleGrid=function(t,e,r,n,o,i,a){return ue.getInstance().sampleGrid(t,i,a,.5,.5,i-.5,.5,i-.5,a-.5,.5,a-.5,e.getX(),e.getY(),o.getX(),o.getY(),n.getX(),n.getY(),r.getX(),r.getY())},t.prototype.transitionsBetween=function(t,e){var r=Math.trunc(t.getX()),n=Math.trunc(t.getY()),o=Math.trunc(e.getX()),i=Math.trunc(e.getY()),a=Math.abs(i-n)>Math.abs(o-r);if(a){var s=r;r=n,n=s,s=o,o=i,i=s}for(var u=Math.abs(o-r),c=Math.abs(i-n),f=-u/2,h=n<i?1:-1,l=r<o?1:-1,d=0,p=this.image.get(a?n:r,a?r:n),g=r,y=n;g!==o;g+=l){var w=this.image.get(a?y:g,a?g:y);if(w!==p&&(d++,p=w),(f+=c)>0){if(y===i)break;y+=h,f-=u}}return d},t}(),fn=function(){function t(){this.decoder=new un}return t.prototype.decode=function(e,r){var n,o;if(void 0===r&&(r=null),null!=r&&r.has(k.PURE_BARCODE)){var i=t.extractPureBits(e.getBlackMatrix());n=this.decoder.decode(i),o=t.NO_POINTS}else{var a=new cn(e.getBlackMatrix()).detect();n=this.decoder.decode(a.getBits()),o=a.getPoints()}var s=n.getRawBytes(),u=new Pt(n.getText(),s,8*s.length,o,Bt.DATA_MATRIX,q.currentTimeMillis()),c=n.getByteSegments();null!=c&&u.putMetadata(Ft.BYTE_SEGMENTS,c);var f=n.getECLevel();return null!=f&&u.putMetadata(Ft.ERROR_CORRECTION_LEVEL,f),u},t.prototype.reset=function(){},t.extractPureBits=function(t){var e=t.getTopLeftOnBit(),r=t.getBottomRightOnBit();if(null==e||null==r)throw new vt;var n=this.moduleSize(e,t),o=e[1],i=r[1],a=e[0],s=(r[0]-a+1)/n,u=(i-o+1)/n;if(s<=0||u<=0)throw new vt;var c=n/2;o+=c,a+=c;for(var f=new yt(s,u),h=0;h<u;h++)for(var l=o+h*n,d=0;d<s;d++)t.get(a+d*n,l)&&f.set(d,h);return f},t.moduleSize=function(t,e){for(var r=e.getWidth(),n=t[0],o=t[1];n<r&&e.get(n,o);)n++;if(n===r)throw new vt;var i=n-t[0];if(0===i)throw new vt;return i},t.NO_POINTS=[],t}(),hn=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();!function(t){function e(e){return void 0===e&&(e=500),t.call(this,new fn,e)||this}hn(e,t)}(Mt),function(t){t[t.L=0]="L",t[t.M=1]="M",t[t.Q=2]="Q",t[t.H=3]="H"}(on||(on={}));var ln,dn=function(){function t(e,r,n){this.value=e,this.stringValue=r,this.bits=n,t.FOR_BITS.set(n,this),t.FOR_VALUE.set(e,this)}return t.prototype.getValue=function(){return this.value},t.prototype.getBits=function(){return this.bits},t.fromString=function(e){switch(e){case"L":return t.L;case"M":return t.M;case"Q":return t.Q;case"H":return t.H;default:throw new X(e+"not available")}},t.prototype.toString=function(){return this.stringValue},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;return this.value===r.value},t.forBits=function(e){if(e<0||e>=t.FOR_BITS.size)throw new j;return t.FOR_BITS.get(e)},t.FOR_BITS=new Map,t.FOR_VALUE=new Map,t.L=new t(on.L,"L",1),t.M=new t(on.M,"M",0),t.Q=new t(on.Q,"Q",3),t.H=new t(on.H,"H",2),t}(),pn=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},gn=function(){function t(t){this.errorCorrectionLevel=dn.forBits(t>>3&3),this.dataMask=7&t}return t.numBitsDiffering=function(t,e){return nt.bitCount(t^e)},t.decodeFormatInformation=function(e,r){var n=t.doDecodeFormatInformation(e,r);return null!==n?n:t.doDecodeFormatInformation(e^t.FORMAT_INFO_MASK_QR,r^t.FORMAT_INFO_MASK_QR)},t.doDecodeFormatInformation=function(e,r){var n,o,i=Number.MAX_SAFE_INTEGER,a=0;try{for(var s=pn(t.FORMAT_INFO_DECODE_LOOKUP),u=s.next();!u.done;u=s.next()){var c=u.value,f=c[0];if(f===e||f===r)return new t(c[1]);var h=t.numBitsDiffering(e,f);h<i&&(a=c[1],i=h),e!==r&&(h=t.numBitsDiffering(r,f))<i&&(a=c[1],i=h)}}catch(l){n={error:l}}finally{try{u&&!u.done&&(o=s.return)&&o.call(s)}finally{if(n)throw n.error}}return i<=3?new t(a):null},t.prototype.getErrorCorrectionLevel=function(){return this.errorCorrectionLevel},t.prototype.getDataMask=function(){return this.dataMask},t.prototype.hashCode=function(){return this.errorCorrectionLevel.getBits()<<3|this.dataMask},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;return this.errorCorrectionLevel===r.errorCorrectionLevel&&this.dataMask===r.dataMask},t.FORMAT_INFO_MASK_QR=21522,t.FORMAT_INFO_DECODE_LOOKUP=[Int32Array.from([21522,0]),Int32Array.from([20773,1]),Int32Array.from([24188,2]),Int32Array.from([23371,3]),Int32Array.from([17913,4]),Int32Array.from([16590,5]),Int32Array.from([20375,6]),Int32Array.from([19104,7]),Int32Array.from([30660,8]),Int32Array.from([29427,9]),Int32Array.from([32170,10]),Int32Array.from([30877,11]),Int32Array.from([26159,12]),Int32Array.from([25368,13]),Int32Array.from([27713,14]),Int32Array.from([26998,15]),Int32Array.from([5769,16]),Int32Array.from([5054,17]),Int32Array.from([7399,18]),Int32Array.from([6608,19]),Int32Array.from([1890,20]),Int32Array.from([597,21]),Int32Array.from([3340,22]),Int32Array.from([2107,23]),Int32Array.from([13663,24]),Int32Array.from([12392,25]),Int32Array.from([16177,26]),Int32Array.from([14854,27]),Int32Array.from([9396,28]),Int32Array.from([8579,29]),Int32Array.from([11994,30]),Int32Array.from([11245,31])],t}(),yn=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},wn=function(){function t(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];this.ecCodewordsPerBlock=t,this.ecBlocks=e}return t.prototype.getECCodewordsPerBlock=function(){return this.ecCodewordsPerBlock},t.prototype.getNumBlocks=function(){var t,e,r=0,n=this.ecBlocks;try{for(var o=yn(n),i=o.next();!i.done;i=o.next())r+=i.value.getCount()}catch(a){t={error:a}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}return r},t.prototype.getTotalECCodewords=function(){return this.ecCodewordsPerBlock*this.getNumBlocks()},t.prototype.getECBlocks=function(){return this.ecBlocks},t}(),vn=function(){function t(t,e){this.count=t,this.dataCodewords=e}return t.prototype.getCount=function(){return this.count},t.prototype.getDataCodewords=function(){return this.dataCodewords},t}(),_n=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},mn=function(){function t(t,e){for(var r,n,o=[],i=2;i<arguments.length;i++)o[i-2]=arguments[i];this.versionNumber=t,this.alignmentPatternCenters=e,this.ecBlocks=o;var a=0,s=o[0].getECCodewordsPerBlock(),u=o[0].getECBlocks();try{for(var c=_n(u),f=c.next();!f.done;f=c.next()){var h=f.value;a+=h.getCount()*(h.getDataCodewords()+s)}}catch(l){r={error:l}}finally{try{f&&!f.done&&(n=c.return)&&n.call(c)}finally{if(r)throw r.error}}this.totalCodewords=a}return t.prototype.getVersionNumber=function(){return this.versionNumber},t.prototype.getAlignmentPatternCenters=function(){return this.alignmentPatternCenters},t.prototype.getTotalCodewords=function(){return this.totalCodewords},t.prototype.getDimensionForVersion=function(){return 17+4*this.versionNumber},t.prototype.getECBlocksForLevel=function(t){return this.ecBlocks[t.getValue()]},t.getProvisionalVersionForDimension=function(t){if(t%4!=1)throw new st;try{return this.getVersionForNumber((t-17)/4)}catch(e){throw new st}},t.getVersionForNumber=function(e){if(e<1||e>40)throw new j;return t.VERSIONS[e-1]},t.decodeVersionInformation=function(e){for(var r=Number.MAX_SAFE_INTEGER,n=0,o=0;o<t.VERSION_DECODE_INFO.length;o++){var i=t.VERSION_DECODE_INFO[o];if(i===e)return t.getVersionForNumber(o+7);var a=gn.numBitsDiffering(e,i);a<r&&(n=o+7,r=a)}return r<=3?t.getVersionForNumber(n):null},t.prototype.buildFunctionPattern=function(){var t=this.getDimensionForVersion(),e=new yt(t);e.setRegion(0,0,9,9),e.setRegion(t-8,0,8,9),e.setRegion(0,t-8,9,8);for(var r=this.alignmentPatternCenters.length,n=0;n<r;n++)for(var o=this.alignmentPatternCenters[n]-2,i=0;i<r;i++)0===n&&(0===i||i===r-1)||n===r-1&&0===i||e.setRegion(this.alignmentPatternCenters[i]-2,o,5,5);return e.setRegion(6,9,1,t-17),e.setRegion(9,6,t-17,1),this.versionNumber>6&&(e.setRegion(t-11,0,3,6),e.setRegion(0,t-11,6,3)),e},t.prototype.toString=function(){return""+this.versionNumber},t.VERSION_DECODE_INFO=Int32Array.from([31892,34236,39577,42195,48118,51042,55367,58893,63784,68472,70749,76311,79154,84390,87683,92361,96236,102084,102881,110507,110734,117786,119615,126325,127568,133589,136944,141498,145311,150283,152622,158308,161089,167017]),t.VERSIONS=[new t(1,new Int32Array(0),new wn(7,new vn(1,19)),new wn(10,new vn(1,16)),new wn(13,new vn(1,13)),new wn(17,new vn(1,9))),new t(2,Int32Array.from([6,18]),new wn(10,new vn(1,34)),new wn(16,new vn(1,28)),new wn(22,new vn(1,22)),new wn(28,new vn(1,16))),new t(3,Int32Array.from([6,22]),new wn(15,new vn(1,55)),new wn(26,new vn(1,44)),new wn(18,new vn(2,17)),new wn(22,new vn(2,13))),new t(4,Int32Array.from([6,26]),new wn(20,new vn(1,80)),new wn(18,new vn(2,32)),new wn(26,new vn(2,24)),new wn(16,new vn(4,9))),new t(5,Int32Array.from([6,30]),new wn(26,new vn(1,108)),new wn(24,new vn(2,43)),new wn(18,new vn(2,15),new vn(2,16)),new wn(22,new vn(2,11),new vn(2,12))),new t(6,Int32Array.from([6,34]),new wn(18,new vn(2,68)),new wn(16,new vn(4,27)),new wn(24,new vn(4,19)),new wn(28,new vn(4,15))),new t(7,Int32Array.from([6,22,38]),new wn(20,new vn(2,78)),new wn(18,new vn(4,31)),new wn(18,new vn(2,14),new vn(4,15)),new wn(26,new vn(4,13),new vn(1,14))),new t(8,Int32Array.from([6,24,42]),new wn(24,new vn(2,97)),new wn(22,new vn(2,38),new vn(2,39)),new wn(22,new vn(4,18),new vn(2,19)),new wn(26,new vn(4,14),new vn(2,15))),new t(9,Int32Array.from([6,26,46]),new wn(30,new vn(2,116)),new wn(22,new vn(3,36),new vn(2,37)),new wn(20,new vn(4,16),new vn(4,17)),new wn(24,new vn(4,12),new vn(4,13))),new t(10,Int32Array.from([6,28,50]),new wn(18,new vn(2,68),new vn(2,69)),new wn(26,new vn(4,43),new vn(1,44)),new wn(24,new vn(6,19),new vn(2,20)),new wn(28,new vn(6,15),new vn(2,16))),new t(11,Int32Array.from([6,30,54]),new wn(20,new vn(4,81)),new wn(30,new vn(1,50),new vn(4,51)),new wn(28,new vn(4,22),new vn(4,23)),new wn(24,new vn(3,12),new vn(8,13))),new t(12,Int32Array.from([6,32,58]),new wn(24,new vn(2,92),new vn(2,93)),new wn(22,new vn(6,36),new vn(2,37)),new wn(26,new vn(4,20),new vn(6,21)),new wn(28,new vn(7,14),new vn(4,15))),new t(13,Int32Array.from([6,34,62]),new wn(26,new vn(4,107)),new wn(22,new vn(8,37),new vn(1,38)),new wn(24,new vn(8,20),new vn(4,21)),new wn(22,new vn(12,11),new vn(4,12))),new t(14,Int32Array.from([6,26,46,66]),new wn(30,new vn(3,115),new vn(1,116)),new wn(24,new vn(4,40),new vn(5,41)),new wn(20,new vn(11,16),new vn(5,17)),new wn(24,new vn(11,12),new vn(5,13))),new t(15,Int32Array.from([6,26,48,70]),new wn(22,new vn(5,87),new vn(1,88)),new wn(24,new vn(5,41),new vn(5,42)),new wn(30,new vn(5,24),new vn(7,25)),new wn(24,new vn(11,12),new vn(7,13))),new t(16,Int32Array.from([6,26,50,74]),new wn(24,new vn(5,98),new vn(1,99)),new wn(28,new vn(7,45),new vn(3,46)),new wn(24,new vn(15,19),new vn(2,20)),new wn(30,new vn(3,15),new vn(13,16))),new t(17,Int32Array.from([6,30,54,78]),new wn(28,new vn(1,107),new vn(5,108)),new wn(28,new vn(10,46),new vn(1,47)),new wn(28,new vn(1,22),new vn(15,23)),new wn(28,new vn(2,14),new vn(17,15))),new t(18,Int32Array.from([6,30,56,82]),new wn(30,new vn(5,120),new vn(1,121)),new wn(26,new vn(9,43),new vn(4,44)),new wn(28,new vn(17,22),new vn(1,23)),new wn(28,new vn(2,14),new vn(19,15))),new t(19,Int32Array.from([6,30,58,86]),new wn(28,new vn(3,113),new vn(4,114)),new wn(26,new vn(3,44),new vn(11,45)),new wn(26,new vn(17,21),new vn(4,22)),new wn(26,new vn(9,13),new vn(16,14))),new t(20,Int32Array.from([6,34,62,90]),new wn(28,new vn(3,107),new vn(5,108)),new wn(26,new vn(3,41),new vn(13,42)),new wn(30,new vn(15,24),new vn(5,25)),new wn(28,new vn(15,15),new vn(10,16))),new t(21,Int32Array.from([6,28,50,72,94]),new wn(28,new vn(4,116),new vn(4,117)),new wn(26,new vn(17,42)),new wn(28,new vn(17,22),new vn(6,23)),new wn(30,new vn(19,16),new vn(6,17))),new t(22,Int32Array.from([6,26,50,74,98]),new wn(28,new vn(2,111),new vn(7,112)),new wn(28,new vn(17,46)),new wn(30,new vn(7,24),new vn(16,25)),new wn(24,new vn(34,13))),new t(23,Int32Array.from([6,30,54,78,102]),new wn(30,new vn(4,121),new vn(5,122)),new wn(28,new vn(4,47),new vn(14,48)),new wn(30,new vn(11,24),new vn(14,25)),new wn(30,new vn(16,15),new vn(14,16))),new t(24,Int32Array.from([6,28,54,80,106]),new wn(30,new vn(6,117),new vn(4,118)),new wn(28,new vn(6,45),new vn(14,46)),new wn(30,new vn(11,24),new vn(16,25)),new wn(30,new vn(30,16),new vn(2,17))),new t(25,Int32Array.from([6,32,58,84,110]),new wn(26,new vn(8,106),new vn(4,107)),new wn(28,new vn(8,47),new vn(13,48)),new wn(30,new vn(7,24),new vn(22,25)),new wn(30,new vn(22,15),new vn(13,16))),new t(26,Int32Array.from([6,30,58,86,114]),new wn(28,new vn(10,114),new vn(2,115)),new wn(28,new vn(19,46),new vn(4,47)),new wn(28,new vn(28,22),new vn(6,23)),new wn(30,new vn(33,16),new vn(4,17))),new t(27,Int32Array.from([6,34,62,90,118]),new wn(30,new vn(8,122),new vn(4,123)),new wn(28,new vn(22,45),new vn(3,46)),new wn(30,new vn(8,23),new vn(26,24)),new wn(30,new vn(12,15),new vn(28,16))),new t(28,Int32Array.from([6,26,50,74,98,122]),new wn(30,new vn(3,117),new vn(10,118)),new wn(28,new vn(3,45),new vn(23,46)),new wn(30,new vn(4,24),new vn(31,25)),new wn(30,new vn(11,15),new vn(31,16))),new t(29,Int32Array.from([6,30,54,78,102,126]),new wn(30,new vn(7,116),new vn(7,117)),new wn(28,new vn(21,45),new vn(7,46)),new wn(30,new vn(1,23),new vn(37,24)),new wn(30,new vn(19,15),new vn(26,16))),new t(30,Int32Array.from([6,26,52,78,104,130]),new wn(30,new vn(5,115),new vn(10,116)),new wn(28,new vn(19,47),new vn(10,48)),new wn(30,new vn(15,24),new vn(25,25)),new wn(30,new vn(23,15),new vn(25,16))),new t(31,Int32Array.from([6,30,56,82,108,134]),new wn(30,new vn(13,115),new vn(3,116)),new wn(28,new vn(2,46),new vn(29,47)),new wn(30,new vn(42,24),new vn(1,25)),new wn(30,new vn(23,15),new vn(28,16))),new t(32,Int32Array.from([6,34,60,86,112,138]),new wn(30,new vn(17,115)),new wn(28,new vn(10,46),new vn(23,47)),new wn(30,new vn(10,24),new vn(35,25)),new wn(30,new vn(19,15),new vn(35,16))),new t(33,Int32Array.from([6,30,58,86,114,142]),new wn(30,new vn(17,115),new vn(1,116)),new wn(28,new vn(14,46),new vn(21,47)),new wn(30,new vn(29,24),new vn(19,25)),new wn(30,new vn(11,15),new vn(46,16))),new t(34,Int32Array.from([6,34,62,90,118,146]),new wn(30,new vn(13,115),new vn(6,116)),new wn(28,new vn(14,46),new vn(23,47)),new wn(30,new vn(44,24),new vn(7,25)),new wn(30,new vn(59,16),new vn(1,17))),new t(35,Int32Array.from([6,30,54,78,102,126,150]),new wn(30,new vn(12,121),new vn(7,122)),new wn(28,new vn(12,47),new vn(26,48)),new wn(30,new vn(39,24),new vn(14,25)),new wn(30,new vn(22,15),new vn(41,16))),new t(36,Int32Array.from([6,24,50,76,102,128,154]),new wn(30,new vn(6,121),new vn(14,122)),new wn(28,new vn(6,47),new vn(34,48)),new wn(30,new vn(46,24),new vn(10,25)),new wn(30,new vn(2,15),new vn(64,16))),new t(37,Int32Array.from([6,28,54,80,106,132,158]),new wn(30,new vn(17,122),new vn(4,123)),new wn(28,new vn(29,46),new vn(14,47)),new wn(30,new vn(49,24),new vn(10,25)),new wn(30,new vn(24,15),new vn(46,16))),new t(38,Int32Array.from([6,32,58,84,110,136,162]),new wn(30,new vn(4,122),new vn(18,123)),new wn(28,new vn(13,46),new vn(32,47)),new wn(30,new vn(48,24),new vn(14,25)),new wn(30,new vn(42,15),new vn(32,16))),new t(39,Int32Array.from([6,26,54,82,110,138,166]),new wn(30,new vn(20,117),new vn(4,118)),new wn(28,new vn(40,47),new vn(7,48)),new wn(30,new vn(43,24),new vn(22,25)),new wn(30,new vn(10,15),new vn(67,16))),new t(40,Int32Array.from([6,30,58,86,114,142,170]),new wn(30,new vn(19,118),new vn(6,119)),new wn(28,new vn(18,47),new vn(31,48)),new wn(30,new vn(34,24),new vn(34,25)),new wn(30,new vn(20,15),new vn(61,16)))],t}();!function(t){t[t.DATA_MASK_000=0]="DATA_MASK_000",t[t.DATA_MASK_001=1]="DATA_MASK_001",t[t.DATA_MASK_010=2]="DATA_MASK_010",t[t.DATA_MASK_011=3]="DATA_MASK_011",t[t.DATA_MASK_100=4]="DATA_MASK_100",t[t.DATA_MASK_101=5]="DATA_MASK_101",t[t.DATA_MASK_110=6]="DATA_MASK_110",t[t.DATA_MASK_111=7]="DATA_MASK_111"}(ln||(ln={}));var Cn,An=function(){function t(t,e){this.value=t,this.isMasked=e}return t.prototype.unmaskBitMatrix=function(t,e){for(var r=0;r<e;r++)for(var n=0;n<e;n++)this.isMasked(r,n)&&t.flip(n,r)},t.values=new Map([[ln.DATA_MASK_000,new t(ln.DATA_MASK_000,function(t,e){return!(t+e&1)})],[ln.DATA_MASK_001,new t(ln.DATA_MASK_001,function(t,e){return!(1&t)})],[ln.DATA_MASK_010,new t(ln.DATA_MASK_010,function(t,e){return e%3==0})],[ln.DATA_MASK_011,new t(ln.DATA_MASK_011,function(t,e){return(t+e)%3==0})],[ln.DATA_MASK_100,new t(ln.DATA_MASK_100,function(t,e){return!(Math.floor(t/2)+Math.floor(e/3)&1)})],[ln.DATA_MASK_101,new t(ln.DATA_MASK_101,function(t,e){return t*e%6==0})],[ln.DATA_MASK_110,new t(ln.DATA_MASK_110,function(t,e){return t*e%6<3})],[ln.DATA_MASK_111,new t(ln.DATA_MASK_111,function(t,e){return!(t+e+t*e%3&1)})]]),t}(),En=function(){function t(t){var e=t.getHeight();if(e<21||1!=(3&e))throw new st;this.bitMatrix=t}return t.prototype.readFormatInformation=function(){if(null!==this.parsedFormatInfo&&void 0!==this.parsedFormatInfo)return this.parsedFormatInfo;for(var t=0,e=0;e<6;e++)t=this.copyBit(e,8,t);t=this.copyBit(7,8,t),t=this.copyBit(8,8,t),t=this.copyBit(8,7,t);for(var r=5;r>=0;r--)t=this.copyBit(8,r,t);var n=this.bitMatrix.getHeight(),o=0,i=n-7;for(r=n-1;r>=i;r--)o=this.copyBit(8,r,o);for(e=n-8;e<n;e++)o=this.copyBit(e,8,o);if(this.parsedFormatInfo=gn.decodeFormatInformation(t,o),null!==this.parsedFormatInfo)return this.parsedFormatInfo;throw new st},t.prototype.readVersion=function(){if(null!==this.parsedVersion&&void 0!==this.parsedVersion)return this.parsedVersion;var t=this.bitMatrix.getHeight(),e=Math.floor((t-17)/4);if(e<=6)return mn.getVersionForNumber(e);for(var r=0,n=t-11,o=5;o>=0;o--)for(var i=t-9;i>=n;i--)r=this.copyBit(i,o,r);var a=mn.decodeVersionInformation(r);if(null!==a&&a.getDimensionForVersion()===t)return this.parsedVersion=a,a;for(r=0,i=5;i>=0;i--)for(o=t-9;o>=n;o--)r=this.copyBit(i,o,r);if(null!==(a=mn.decodeVersionInformation(r))&&a.getDimensionForVersion()===t)return this.parsedVersion=a,a;throw new st},t.prototype.copyBit=function(t,e,r){return(this.isMirror?this.bitMatrix.get(e,t):this.bitMatrix.get(t,e))?r<<1|1:r<<1},t.prototype.readCodewords=function(){var t=this.readFormatInformation(),e=this.readVersion(),r=An.values.get(t.getDataMask()),n=this.bitMatrix.getHeight();r.unmaskBitMatrix(this.bitMatrix,n);for(var o=e.buildFunctionPattern(),i=!0,a=new Uint8Array(e.getTotalCodewords()),s=0,u=0,c=0,f=n-1;f>0;f-=2){6===f&&f--;for(var h=0;h<n;h++)for(var l=i?n-1-h:h,d=0;d<2;d++)o.get(f-d,l)||(c++,u<<=1,this.bitMatrix.get(f-d,l)&&(u|=1),8===c&&(a[s++]=u,c=0,u=0));i=!i}if(s!==e.getTotalCodewords())throw new st;return a},t.prototype.remask=function(){if(null!==this.parsedFormatInfo){var t=An.values[this.parsedFormatInfo.getDataMask()],e=this.bitMatrix.getHeight();t.unmaskBitMatrix(this.bitMatrix,e)}},t.prototype.setMirror=function(t){this.parsedVersion=null,this.parsedFormatInfo=null,this.isMirror=t},t.prototype.mirror=function(){for(var t=this.bitMatrix,e=0,r=t.getWidth();e<r;e++)for(var n=e+1,o=t.getHeight();n<o;n++)t.get(e,n)!==t.get(n,e)&&(t.flip(n,e),t.flip(e,n))},t}(),In=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Sn=function(){function t(t,e){this.numDataCodewords=t,this.codewords=e}return t.getDataBlocks=function(e,r,n){var o,i,a,s;if(e.length!==r.getTotalCodewords())throw new j;var u=r.getECBlocksForLevel(n),c=0,f=u.getECBlocks();try{for(var h=In(f),l=h.next();!l.done;l=h.next())c+=(w=l.value).getCount()}catch(O){o={error:O}}finally{try{l&&!l.done&&(i=h.return)&&i.call(h)}finally{if(o)throw o.error}}var d=new Array(c),p=0;try{for(var g=In(f),y=g.next();!y.done;y=g.next())for(var w=y.value,v=0;v<w.getCount();v++){var _=w.getDataCodewords(),m=u.getECCodewordsPerBlock()+_;d[p++]=new t(_,new Uint8Array(m))}}catch(R){a={error:R}}finally{try{y&&!y.done&&(s=g.return)&&s.call(g)}finally{if(a)throw a.error}}for(var C=d[0].codewords.length,A=d.length-1;A>=0&&d[A].codewords.length!==C;)A--;A++;var E=C-u.getECCodewordsPerBlock(),I=0;for(v=0;v<E;v++)for(var S=0;S<p;S++)d[S].codewords[v]=e[I++];for(S=A;S<p;S++)d[S].codewords[E]=e[I++];var b=d[0].codewords.length;for(v=E;v<b;v++)for(S=0;S<p;S++){var T=S<A?v:v+1;d[S].codewords[T]=e[I++]}return d},t.prototype.getNumDataCodewords=function(){return this.numDataCodewords},t.prototype.getCodewords=function(){return this.codewords},t}();!function(t){t[t.TERMINATOR=0]="TERMINATOR",t[t.NUMERIC=1]="NUMERIC",t[t.ALPHANUMERIC=2]="ALPHANUMERIC",t[t.STRUCTURED_APPEND=3]="STRUCTURED_APPEND",t[t.BYTE=4]="BYTE",t[t.ECI=5]="ECI",t[t.KANJI=6]="KANJI",t[t.FNC1_FIRST_POSITION=7]="FNC1_FIRST_POSITION",t[t.FNC1_SECOND_POSITION=8]="FNC1_SECOND_POSITION",t[t.HANZI=9]="HANZI"}(Cn||(Cn={}));var bn,Tn,On=function(){function t(e,r,n,o){this.value=e,this.stringValue=r,this.characterCountBitsForVersions=n,this.bits=o,t.FOR_BITS.set(o,this),t.FOR_VALUE.set(e,this)}return t.forBits=function(e){var r=t.FOR_BITS.get(e);if(void 0===r)throw new j;return r},t.prototype.getCharacterCountBits=function(t){var e,r=t.getVersionNumber();return e=r<=9?0:r<=26?1:2,this.characterCountBitsForVersions[e]},t.prototype.getValue=function(){return this.value},t.prototype.getBits=function(){return this.bits},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;return this.value===r.value},t.prototype.toString=function(){return this.stringValue},t.FOR_BITS=new Map,t.FOR_VALUE=new Map,t.TERMINATOR=new t(Cn.TERMINATOR,"TERMINATOR",Int32Array.from([0,0,0]),0),t.NUMERIC=new t(Cn.NUMERIC,"NUMERIC",Int32Array.from([10,12,14]),1),t.ALPHANUMERIC=new t(Cn.ALPHANUMERIC,"ALPHANUMERIC",Int32Array.from([9,11,13]),2),t.STRUCTURED_APPEND=new t(Cn.STRUCTURED_APPEND,"STRUCTURED_APPEND",Int32Array.from([0,0,0]),3),t.BYTE=new t(Cn.BYTE,"BYTE",Int32Array.from([8,16,16]),4),t.ECI=new t(Cn.ECI,"ECI",Int32Array.from([0,0,0]),7),t.KANJI=new t(Cn.KANJI,"KANJI",Int32Array.from([8,10,12]),8),t.FNC1_FIRST_POSITION=new t(Cn.FNC1_FIRST_POSITION,"FNC1_FIRST_POSITION",Int32Array.from([0,0,0]),5),t.FNC1_SECOND_POSITION=new t(Cn.FNC1_SECOND_POSITION,"FNC1_SECOND_POSITION",Int32Array.from([0,0,0]),9),t.HANZI=new t(Cn.HANZI,"HANZI",Int32Array.from([8,10,12]),13),t}(),Rn=function(){function t(){}return t.decode=function(e,r,n,o){var i=new nn(e),a=new gt,s=new Array,u=-1,c=-1;try{var f=null,h=!1,l=void 0;do{if(i.available()<4)l=On.TERMINATOR;else{var d=i.readBits(4);l=On.forBits(d)}switch(l){case On.TERMINATOR:break;case On.FNC1_FIRST_POSITION:case On.FNC1_SECOND_POSITION:h=!0;break;case On.STRUCTURED_APPEND:if(i.available()<16)throw new st;u=i.readBits(8),c=i.readBits(8);break;case On.ECI:var p=t.parseECIValue(i);if(null===(f=ft.getCharacterSetECIByValue(p)))throw new st;break;case On.HANZI:var g=i.readBits(4),y=i.readBits(l.getCharacterCountBits(r));g===t.GB2312_SUBSET&&t.decodeHanziSegment(i,a,y);break;default:var w=i.readBits(l.getCharacterCountBits(r));switch(l){case On.NUMERIC:t.decodeNumericSegment(i,a,w);break;case On.ALPHANUMERIC:t.decodeAlphanumericSegment(i,a,w,h);break;case On.BYTE:t.decodeByteSegment(i,a,w,f,s,o);break;case On.KANJI:t.decodeKanjiSegment(i,a,w);break;default:throw new st}}}while(l!==On.TERMINATOR)}catch(v){throw new st}return new xt(e,a.toString(),0===s.length?null:s,null===n?null:n.toString(),u,c)},t.decodeHanziSegment=function(t,e,r){if(13*r>t.available())throw new st;for(var n=new Uint8Array(2*r),o=0;r>0;){var i=t.readBits(13),a=i/96<<8&4294967295|i%96;a+=a<959?41377:42657,n[o]=a>>8&255,n[o+1]=255&a,o+=2,r--}try{e.append(dt.decode(n,pt.GB2312))}catch(s){throw new st(s)}},t.decodeKanjiSegment=function(t,e,r){if(13*r>t.available())throw new st;for(var n=new Uint8Array(2*r),o=0;r>0;){var i=t.readBits(13),a=i/192<<8&4294967295|i%192;a+=a<7936?33088:49472,n[o]=a>>8,n[o+1]=a,o+=2,r--}try{e.append(dt.decode(n,pt.SHIFT_JIS))}catch(s){throw new st(s)}},t.decodeByteSegment=function(t,e,r,n,o,i){if(8*r>t.available())throw new st;for(var a,s=new Uint8Array(r),u=0;u<r;u++)s[u]=t.readBits(8);a=null===n?pt.guessEncoding(s,i):n.getName();try{e.append(dt.decode(s,a))}catch(c){throw new st(c)}o.push(s)},t.toAlphaNumericChar=function(e){if(e>=t.ALPHANUMERIC_CHARS.length)throw new st;return t.ALPHANUMERIC_CHARS[e]},t.decodeAlphanumericSegment=function(e,r,n,o){for(var i=r.length();n>1;){if(e.available()<11)throw new st;var a=e.readBits(11);r.append(t.toAlphaNumericChar(Math.floor(a/45))),r.append(t.toAlphaNumericChar(a%45)),n-=2}if(1===n){if(e.available()<6)throw new st;r.append(t.toAlphaNumericChar(e.readBits(6)))}if(o)for(var s=i;s<r.length();s++)"%"===r.charAt(s)&&(s<r.length()-1&&"%"===r.charAt(s+1)?r.deleteCharAt(s+1):r.setCharAt(s,String.fromCharCode(29)))},t.decodeNumericSegment=function(e,r,n){for(;n>=3;){if(e.available()<10)throw new st;var o=e.readBits(10);if(o>=1e3)throw new st;r.append(t.toAlphaNumericChar(Math.floor(o/100))),r.append(t.toAlphaNumericChar(Math.floor(o/10)%10)),r.append(t.toAlphaNumericChar(o%10)),n-=3}if(2===n){if(e.available()<7)throw new st;var i=e.readBits(7);if(i>=100)throw new st;r.append(t.toAlphaNumericChar(Math.floor(i/10))),r.append(t.toAlphaNumericChar(i%10))}else if(1===n){if(e.available()<4)throw new st;var a=e.readBits(4);if(a>=10)throw new st;r.append(t.toAlphaNumericChar(a))}},t.parseECIValue=function(t){var e=t.readBits(8);if(!(128&e))return 127&e;if(128==(192&e))return(63&e)<<8&4294967295|t.readBits(8);if(192==(224&e))return(31&e)<<16&4294967295|t.readBits(16);throw new st},t.ALPHANUMERIC_CHARS="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",t.GB2312_SUBSET=1,t}(),Nn=function(){function t(t){this.mirrored=t}return t.prototype.isMirrored=function(){return this.mirrored},t.prototype.applyMirroredCorrection=function(t){if(this.mirrored&&null!==t&&!(t.length<3)){var e=t[0];t[0]=t[2],t[2]=e}},t}(),Dn=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Mn=function(){function t(){this.rsDecoder=new Kt(Wt.QR_CODE_FIELD_256)}return t.prototype.decodeBooleanArray=function(t,e){return this.decodeBitMatrix(yt.parseFromBooleanArray(t),e)},t.prototype.decodeBitMatrix=function(t,e){var r=new En(t),n=null;try{return this.decodeBitMatrixParser(r,e)}catch(i){n=i}try{r.remask(),r.setMirror(!0),r.readVersion(),r.readFormatInformation(),r.mirror();var o=this.decodeBitMatrixParser(r,e);return o.setOther(new Nn(!0)),o}catch(i){if(null!==n)throw n;throw i}},t.prototype.decodeBitMatrixParser=function(t,e){var r,n,o,i,a=t.readVersion(),s=t.readFormatInformation().getErrorCorrectionLevel(),u=t.readCodewords(),c=Sn.getDataBlocks(u,a,s),f=0;try{for(var h=Dn(c),l=h.next();!l.done;l=h.next())f+=(w=l.value).getNumDataCodewords()}catch(C){r={error:C}}finally{try{l&&!l.done&&(n=h.return)&&n.call(h)}finally{if(r)throw r.error}}var d=new Uint8Array(f),p=0;try{for(var g=Dn(c),y=g.next();!y.done;y=g.next()){var w,v=(w=y.value).getCodewords(),_=w.getNumDataCodewords();this.correctErrors(v,_);for(var m=0;m<_;m++)d[p++]=v[m]}}catch(A){o={error:A}}finally{try{y&&!y.done&&(i=g.return)&&i.call(g)}finally{if(o)throw o.error}}return Rn.decode(d,a,s,e)},t.prototype.correctErrors=function(t,e){var r=new Int32Array(t);try{this.rsDecoder.decode(r,t.length-e)}catch(o){throw new Z}for(var n=0;n<e;n++)t[n]=r[n]},t}(),Pn=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Bn=function(t){function e(e,r,n){var o=t.call(this,e,r)||this;return o.estimatedModuleSize=n,o}return Pn(e,t),e.prototype.aboutEquals=function(t,e,r){if(Math.abs(e-this.getY())<=t&&Math.abs(r-this.getX())<=t){var n=Math.abs(t-this.estimatedModuleSize);return n<=1||n<=this.estimatedModuleSize}return!1},e.prototype.combineEstimate=function(t,r,n){return new e((this.getX()+r)/2,(this.getY()+t)/2,(this.estimatedModuleSize+n)/2)},e}($t),Ln=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Fn=function(){function t(t,e,r,n,o,i,a){this.image=t,this.startX=e,this.startY=r,this.width=n,this.height=o,this.moduleSize=i,this.resultPointCallback=a,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(3)}return t.prototype.find=function(){for(var t=this.startX,e=this.height,r=t+this.width,n=this.startY+e/2,o=new Int32Array(3),i=this.image,a=0;a<e;a++){var s=n+(1&a?-Math.floor((a+1)/2):Math.floor((a+1)/2));o[0]=0,o[1]=0,o[2]=0;for(var u=t;u<r&&!i.get(u,s);)u++;for(var c=0;u<r;){if(i.get(u,s))if(1===c)o[1]++;else if(2===c){var f;if(this.foundPatternCross(o)&&null!==(f=this.handlePossibleCenter(o,s,u)))return f;o[0]=o[2],o[1]=1,o[2]=0,c=1}else o[++c]++;else 1===c&&c++,o[c]++;u++}if(this.foundPatternCross(o)&&null!==(f=this.handlePossibleCenter(o,s,r)))return f}if(0!==this.possibleCenters.length)return this.possibleCenters[0];throw new vt},t.centerFromEnd=function(t,e){return e-t[2]-t[1]/2},t.prototype.foundPatternCross=function(t){for(var e=this.moduleSize,r=e/2,n=0;n<3;n++)if(Math.abs(e-t[n])>=r)return!1;return!0},t.prototype.crossCheckVertical=function(e,r,n,o){var i=this.image,a=i.getHeight(),s=this.crossCheckStateCount;s[0]=0,s[1]=0,s[2]=0;for(var u=e;u>=0&&i.get(r,u)&&s[1]<=n;)s[1]++,u--;if(u<0||s[1]>n)return NaN;for(;u>=0&&!i.get(r,u)&&s[0]<=n;)s[0]++,u--;if(s[0]>n)return NaN;for(u=e+1;u<a&&i.get(r,u)&&s[1]<=n;)s[1]++,u++;if(u===a||s[1]>n)return NaN;for(;u<a&&!i.get(r,u)&&s[2]<=n;)s[2]++,u++;if(s[2]>n)return NaN;var c=s[0]+s[1]+s[2];return 5*Math.abs(c-o)>=2*o?NaN:this.foundPatternCross(s)?t.centerFromEnd(s,u):NaN},t.prototype.handlePossibleCenter=function(e,r,n){var o,i,a=e[0]+e[1]+e[2],s=t.centerFromEnd(e,n),u=this.crossCheckVertical(r,s,2*e[1],a);if(!isNaN(u)){var c=(e[0]+e[1]+e[2])/3;try{for(var f=Ln(this.possibleCenters),h=f.next();!h.done;h=f.next()){var l=h.value;if(l.aboutEquals(c,u,s))return l.combineEstimate(u,s,c)}}catch(p){o={error:p}}finally{try{h&&!h.done&&(i=f.return)&&i.call(f)}finally{if(o)throw o.error}}var d=new Bn(s,u,c);this.possibleCenters.push(d),null!==this.resultPointCallback&&void 0!==this.resultPointCallback&&this.resultPointCallback.foundPossibleResultPoint(d)}return null},t}(),kn=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),xn=function(t){function e(e,r,n,o){var i=t.call(this,e,r)||this;return i.estimatedModuleSize=n,i.count=o,void 0===o&&(i.count=1),i}return kn(e,t),e.prototype.getEstimatedModuleSize=function(){return this.estimatedModuleSize},e.prototype.getCount=function(){return this.count},e.prototype.aboutEquals=function(t,e,r){if(Math.abs(e-this.getY())<=t&&Math.abs(r-this.getX())<=t){var n=Math.abs(t-this.estimatedModuleSize);return n<=1||n<=this.estimatedModuleSize}return!1},e.prototype.combineEstimate=function(t,r,n){var o=this.count+1;return new e((this.count*this.getX()+r)/o,(this.count*this.getY()+t)/o,(this.count*this.estimatedModuleSize+n)/o,o)},e}($t),Vn=function(){function t(t){this.bottomLeft=t[0],this.topLeft=t[1],this.topRight=t[2]}return t.prototype.getBottomLeft=function(){return this.bottomLeft},t.prototype.getTopLeft=function(){return this.topLeft},t.prototype.getTopRight=function(){return this.topRight},t}(),Un=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Hn=function(){function t(t,e){this.image=t,this.resultPointCallback=e,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(5),this.resultPointCallback=e}return t.prototype.getImage=function(){return this.image},t.prototype.getPossibleCenters=function(){return this.possibleCenters},t.prototype.find=function(e){var r=null!=e&&void 0!==e.get(k.TRY_HARDER),n=null!=e&&void 0!==e.get(k.PURE_BARCODE),o=this.image,i=o.getHeight(),a=o.getWidth(),s=Math.floor(3*i/(4*t.MAX_MODULES));(s<t.MIN_SKIP||r)&&(s=t.MIN_SKIP);for(var u=!1,c=new Int32Array(5),f=s-1;f<i&&!u;f+=s){c[0]=0,c[1]=0,c[2]=0,c[3]=0,c[4]=0;for(var h=0,l=0;l<a;l++)if(o.get(l,f))1&~h||h++,c[h]++;else if(1&h)c[h]++;else if(4===h)if(t.foundPatternCross(c)){if(!0!==this.handlePossibleCenter(c,f,l,n)){c[0]=c[2],c[1]=c[3],c[2]=c[4],c[3]=1,c[4]=0,h=3;continue}if(s=2,!0===this.hasSkipped)u=this.haveMultiplyConfirmedCenters();else{var d=this.findRowSkip();d>c[2]&&(f+=d-c[2]-s,l=a-1)}h=0,c[0]=0,c[1]=0,c[2]=0,c[3]=0,c[4]=0}else c[0]=c[2],c[1]=c[3],c[2]=c[4],c[3]=1,c[4]=0,h=3;else c[++h]++;t.foundPatternCross(c)&&!0===this.handlePossibleCenter(c,f,a,n)&&(s=c[0],this.hasSkipped&&(u=this.haveMultiplyConfirmedCenters()))}var p=this.selectBestPatterns();return $t.orderBestPatterns(p),new Vn(p)},t.centerFromEnd=function(t,e){return e-t[4]-t[3]-t[2]/2},t.foundPatternCross=function(t){for(var e=0,r=0;r<5;r++){var n=t[r];if(0===n)return!1;e+=n}if(e<7)return!1;var o=e/7,i=o/2;return Math.abs(o-t[0])<i&&Math.abs(o-t[1])<i&&Math.abs(3*o-t[2])<3*i&&Math.abs(o-t[3])<i&&Math.abs(o-t[4])<i},t.prototype.getCrossCheckStateCount=function(){var t=this.crossCheckStateCount;return t[0]=0,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t},t.prototype.crossCheckDiagonal=function(e,r,n,o){for(var i=this.getCrossCheckStateCount(),a=0,s=this.image;e>=a&&r>=a&&s.get(r-a,e-a);)i[2]++,a++;if(e<a||r<a)return!1;for(;e>=a&&r>=a&&!s.get(r-a,e-a)&&i[1]<=n;)i[1]++,a++;if(e<a||r<a||i[1]>n)return!1;for(;e>=a&&r>=a&&s.get(r-a,e-a)&&i[0]<=n;)i[0]++,a++;if(i[0]>n)return!1;var u=s.getHeight(),c=s.getWidth();for(a=1;e+a<u&&r+a<c&&s.get(r+a,e+a);)i[2]++,a++;if(e+a>=u||r+a>=c)return!1;for(;e+a<u&&r+a<c&&!s.get(r+a,e+a)&&i[3]<n;)i[3]++,a++;if(e+a>=u||r+a>=c||i[3]>=n)return!1;for(;e+a<u&&r+a<c&&s.get(r+a,e+a)&&i[4]<n;)i[4]++,a++;if(i[4]>=n)return!1;var f=i[0]+i[1]+i[2]+i[3]+i[4];return Math.abs(f-o)<2*o&&t.foundPatternCross(i)},t.prototype.crossCheckVertical=function(e,r,n,o){for(var i=this.image,a=i.getHeight(),s=this.getCrossCheckStateCount(),u=e;u>=0&&i.get(r,u);)s[2]++,u--;if(u<0)return NaN;for(;u>=0&&!i.get(r,u)&&s[1]<=n;)s[1]++,u--;if(u<0||s[1]>n)return NaN;for(;u>=0&&i.get(r,u)&&s[0]<=n;)s[0]++,u--;if(s[0]>n)return NaN;for(u=e+1;u<a&&i.get(r,u);)s[2]++,u++;if(u===a)return NaN;for(;u<a&&!i.get(r,u)&&s[3]<n;)s[3]++,u++;if(u===a||s[3]>=n)return NaN;for(;u<a&&i.get(r,u)&&s[4]<n;)s[4]++,u++;if(s[4]>=n)return NaN;var c=s[0]+s[1]+s[2]+s[3]+s[4];return 5*Math.abs(c-o)>=2*o?NaN:t.foundPatternCross(s)?t.centerFromEnd(s,u):NaN},t.prototype.crossCheckHorizontal=function(e,r,n,o){for(var i=this.image,a=i.getWidth(),s=this.getCrossCheckStateCount(),u=e;u>=0&&i.get(u,r);)s[2]++,u--;if(u<0)return NaN;for(;u>=0&&!i.get(u,r)&&s[1]<=n;)s[1]++,u--;if(u<0||s[1]>n)return NaN;for(;u>=0&&i.get(u,r)&&s[0]<=n;)s[0]++,u--;if(s[0]>n)return NaN;for(u=e+1;u<a&&i.get(u,r);)s[2]++,u++;if(u===a)return NaN;for(;u<a&&!i.get(u,r)&&s[3]<n;)s[3]++,u++;if(u===a||s[3]>=n)return NaN;for(;u<a&&i.get(u,r)&&s[4]<n;)s[4]++,u++;if(s[4]>=n)return NaN;var c=s[0]+s[1]+s[2]+s[3]+s[4];return 5*Math.abs(c-o)>=o?NaN:t.foundPatternCross(s)?t.centerFromEnd(s,u):NaN},t.prototype.handlePossibleCenter=function(e,r,n,o){var i=e[0]+e[1]+e[2]+e[3]+e[4],a=t.centerFromEnd(e,n),s=this.crossCheckVertical(r,Math.floor(a),e[2],i);if(!isNaN(s)&&(a=this.crossCheckHorizontal(Math.floor(a),Math.floor(s),e[2],i),!isNaN(a)&&(!o||this.crossCheckDiagonal(Math.floor(s),Math.floor(a),e[2],i)))){for(var u=i/7,c=!1,f=this.possibleCenters,h=0,l=f.length;h<l;h++){var d=f[h];if(d.aboutEquals(u,s,a)){f[h]=d.combineEstimate(s,a,u),c=!0;break}}if(!c){var p=new xn(a,s,u);f.push(p),null!==this.resultPointCallback&&void 0!==this.resultPointCallback&&this.resultPointCallback.foundPossibleResultPoint(p)}return!0}return!1},t.prototype.findRowSkip=function(){var e,r;if(this.possibleCenters.length<=1)return 0;var n=null;try{for(var o=Un(this.possibleCenters),i=o.next();!i.done;i=o.next()){var a=i.value;if(a.getCount()>=t.CENTER_QUORUM){if(null!=n)return this.hasSkipped=!0,Math.floor((Math.abs(n.getX()-a.getX())-Math.abs(n.getY()-a.getY()))/2);n=a}}}catch(s){e={error:s}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return 0},t.prototype.haveMultiplyConfirmedCenters=function(){var e,r,n,o,i=0,a=0,s=this.possibleCenters.length;try{for(var u=Un(this.possibleCenters),c=u.next();!c.done;c=u.next())(p=c.value).getCount()>=t.CENTER_QUORUM&&(i++,a+=p.getEstimatedModuleSize())}catch(g){e={error:g}}finally{try{c&&!c.done&&(r=u.return)&&r.call(u)}finally{if(e)throw e.error}}if(i<3)return!1;var f=a/s,h=0;try{for(var l=Un(this.possibleCenters),d=l.next();!d.done;d=l.next()){var p=d.value;h+=Math.abs(p.getEstimatedModuleSize()-f)}}catch(y){n={error:y}}finally{try{d&&!d.done&&(o=l.return)&&o.call(l)}finally{if(n)throw n.error}}return h<=.05*a},t.prototype.selectBestPatterns=function(){var t,e,r,n,o=this.possibleCenters.length;if(o<3)throw new vt;var i,a=this.possibleCenters;if(o>3){var s=0,u=0;try{for(var c=Un(this.possibleCenters),f=c.next();!f.done;f=c.next()){var h=f.value.getEstimatedModuleSize();s+=h,u+=h*h}}catch(v){t={error:v}}finally{try{f&&!f.done&&(e=c.return)&&e.call(c)}finally{if(t)throw t.error}}i=s/o;var l=Math.sqrt(u/o-i*i);a.sort(function(t,e){var r=Math.abs(e.getEstimatedModuleSize()-i),n=Math.abs(t.getEstimatedModuleSize()-i);return r<n?-1:r>n?1:0});for(var d=Math.max(.2*i,l),p=0;p<a.length&&a.length>3;p++){var g=a[p];Math.abs(g.getEstimatedModuleSize()-i)>d&&(a.splice(p,1),p--)}}if(a.length>3){s=0;try{for(var y=Un(a),w=y.next();!w.done;w=y.next())s+=w.value.getEstimatedModuleSize()}catch(_){r={error:_}}finally{try{w&&!w.done&&(n=y.return)&&n.call(y)}finally{if(r)throw r.error}}i=s/a.length,a.sort(function(t,e){if(e.getCount()===t.getCount()){var r=Math.abs(e.getEstimatedModuleSize()-i),n=Math.abs(t.getEstimatedModuleSize()-i);return r<n?1:r>n?-1:0}return e.getCount()-t.getCount()}),a.splice(3)}return[a[0],a[1],a[2]]},t.CENTER_QUORUM=2,t.MIN_SKIP=3,t.MAX_MODULES=57,t}(),Gn=function(){function t(t){this.image=t}return t.prototype.getImage=function(){return this.image},t.prototype.getResultPointCallback=function(){return this.resultPointCallback},t.prototype.detect=function(t){this.resultPointCallback=null==t?null:t.get(k.NEED_RESULT_POINT_CALLBACK);var e=new Hn(this.image,this.resultPointCallback).find(t);return this.processFinderPatternInfo(e)},t.prototype.processFinderPatternInfo=function(e){var r=e.getTopLeft(),n=e.getTopRight(),o=e.getBottomLeft(),i=this.calculateModuleSize(r,n,o);if(i<1)throw new vt("No pattern found in proccess finder.");var a=t.computeDimension(r,n,o,i),s=mn.getProvisionalVersionForDimension(a),u=s.getDimensionForVersion()-7,c=null;if(s.getAlignmentPatternCenters().length>0)for(var f=n.getX()-r.getX()+o.getX(),h=n.getY()-r.getY()+o.getY(),l=1-3/u,d=Math.floor(r.getX()+l*(f-r.getX())),p=Math.floor(r.getY()+l*(h-r.getY())),g=4;g<=16;g<<=1)try{c=this.findAlignmentInRegion(i,d,p,g);break}catch(v){if(!(v instanceof vt))throw v}var y=t.createTransform(r,n,o,c,a),w=t.sampleGrid(this.image,y,a);return new te(w,null===c?[o,r,n]:[o,r,n,c])},t.createTransform=function(t,e,r,n,o){var i,a,s,u,c=o-3.5;return null!==n?(i=n.getX(),a=n.getY(),u=s=c-3):(i=e.getX()-t.getX()+r.getX(),a=e.getY()-t.getY()+r.getY(),s=c,u=c),ie.quadrilateralToQuadrilateral(3.5,3.5,c,3.5,s,u,3.5,c,t.getX(),t.getY(),e.getX(),e.getY(),i,a,r.getX(),r.getY())},t.sampleGrid=function(t,e,r){return ue.getInstance().sampleGridWithTransform(t,r,r,e)},t.computeDimension=function(t,e,r,n){var o=Qt.round($t.distance(t,e)/n),i=Qt.round($t.distance(t,r)/n),a=Math.floor((o+i)/2)+7;switch(3&a){case 0:a++;break;case 2:a--;break;case 3:throw new vt("Dimensions could be not found.")}return a},t.prototype.calculateModuleSize=function(t,e,r){return(this.calculateModuleSizeOneWay(t,e)+this.calculateModuleSizeOneWay(t,r))/2},t.prototype.calculateModuleSizeOneWay=function(t,e){var r=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(t.getX()),Math.floor(t.getY()),Math.floor(e.getX()),Math.floor(e.getY())),n=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(e.getX()),Math.floor(e.getY()),Math.floor(t.getX()),Math.floor(t.getY()));return isNaN(r)?n/7:isNaN(n)?r/7:(r+n)/14},t.prototype.sizeOfBlackWhiteBlackRunBothWays=function(t,e,r,n){var o=this.sizeOfBlackWhiteBlackRun(t,e,r,n),i=1,a=t-(r-t);a<0?(i=t/(t-a),a=0):a>=this.image.getWidth()&&(i=(this.image.getWidth()-1-t)/(a-t),a=this.image.getWidth()-1);var s=Math.floor(e-(n-e)*i);return i=1,s<0?(i=e/(e-s),s=0):s>=this.image.getHeight()&&(i=(this.image.getHeight()-1-e)/(s-e),s=this.image.getHeight()-1),a=Math.floor(t+(a-t)*i),(o+=this.sizeOfBlackWhiteBlackRun(t,e,a,s))-1},t.prototype.sizeOfBlackWhiteBlackRun=function(t,e,r,n){var o=Math.abs(n-e)>Math.abs(r-t);if(o){var i=t;t=e,e=i,i=r,r=n,n=i}for(var a=Math.abs(r-t),s=Math.abs(n-e),u=-a/2,c=t<r?1:-1,f=e<n?1:-1,h=0,l=r+c,d=t,p=e;d!==l;d+=c){var g=o?p:d,y=o?d:p;if(1===h===this.image.get(g,y)){if(2===h)return Qt.distance(d,p,t,e);h++}if((u+=s)>0){if(p===n)break;p+=f,u-=a}}return 2===h?Qt.distance(r+c,n,t,e):NaN},t.prototype.findAlignmentInRegion=function(t,e,r,n){var o=Math.floor(n*t),i=Math.max(0,e-o),a=Math.min(this.image.getWidth()-1,e+o);if(a-i<3*t)throw new vt("Alignment top exceeds estimated module size.");var s=Math.max(0,r-o),u=Math.min(this.image.getHeight()-1,r+o);if(u-s<3*t)throw new vt("Alignment bottom exceeds estimated module size.");return new Fn(this.image,i,s,a-i,u-s,t,this.resultPointCallback).find()},t}(),Xn=function(){function t(){this.decoder=new Mn}return t.prototype.getDecoder=function(){return this.decoder},t.prototype.decode=function(e,r){var n,o;if(null!=r&&void 0!==r.get(k.PURE_BARCODE)){var i=t.extractPureBits(e.getBlackMatrix());n=this.decoder.decodeBitMatrix(i,r),o=t.NO_POINTS}else{var a=new Gn(e.getBlackMatrix()).detect(r);n=this.decoder.decodeBitMatrix(a.getBits(),r),o=a.getPoints()}n.getOther()instanceof Nn&&n.getOther().applyMirroredCorrection(o);var s=new Pt(n.getText(),n.getRawBytes(),void 0,o,Bt.QR_CODE,void 0),u=n.getByteSegments();null!==u&&s.putMetadata(Ft.BYTE_SEGMENTS,u);var c=n.getECLevel();return null!==c&&s.putMetadata(Ft.ERROR_CORRECTION_LEVEL,c),n.hasStructuredAppend()&&(s.putMetadata(Ft.STRUCTURED_APPEND_SEQUENCE,n.getStructuredAppendSequenceNumber()),s.putMetadata(Ft.STRUCTURED_APPEND_PARITY,n.getStructuredAppendParity())),s},t.prototype.reset=function(){},t.extractPureBits=function(t){var e=t.getTopLeftOnBit(),r=t.getBottomRightOnBit();if(null===e||null===r)throw new vt;var n=this.moduleSize(e,t),o=e[1],i=r[1],a=e[0],s=r[0];if(a>=s||o>=i)throw new vt;if(i-o!==s-a&&(s=a+(i-o))>=t.getWidth())throw new vt;var u=Math.round((s-a+1)/n),c=Math.round((i-o+1)/n);if(u<=0||c<=0)throw new vt;if(c!==u)throw new vt;var f=Math.floor(n/2);o+=f;var h=(a+=f)+Math.floor((u-1)*n)-s;if(h>0){if(h>f)throw new vt;a-=h}var l=o+Math.floor((c-1)*n)-i;if(l>0){if(l>f)throw new vt;o-=l}for(var d=new yt(u,c),p=0;p<c;p++)for(var g=o+Math.floor(p*n),y=0;y<u;y++)t.get(a+Math.floor(y*n),g)&&d.set(y,p);return d},t.moduleSize=function(t,e){for(var r=e.getHeight(),n=e.getWidth(),o=t[0],i=t[1],a=!0,s=0;o<n&&i<r;){if(a!==e.get(o,i)){if(5===++s)break;a=!a}o++,i++}if(o===n||i===r)throw new vt;return(o-t[0])/7},t.NO_POINTS=new Array,t}(),Wn=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},jn=function(){function t(){}return t.prototype.PDF417Common=function(){},t.getBitCountSum=function(t){return Qt.sum(t)},t.toIntArray=function(e){var r,n;if(null==e||!e.length)return t.EMPTY_INT_ARRAY;var o=new Int32Array(e.length),i=0;try{for(var a=Wn(e),s=a.next();!s.done;s=a.next()){var u=s.value;o[i++]=u}}catch(c){r={error:c}}finally{try{s&&!s.done&&(n=a.return)&&n.call(a)}finally{if(r)throw r.error}}return o},t.getCodeword=function(e){var r=rt.binarySearch(t.SYMBOL_TABLE,262143&e);return r<0?-1:(t.CODEWORD_TABLE[r]-1)%t.NUMBER_OF_CODEWORDS},t.NUMBER_OF_CODEWORDS=929,t.MAX_CODEWORDS_IN_BARCODE=t.NUMBER_OF_CODEWORDS-1,t.MIN_ROWS_IN_BARCODE=3,t.MAX_ROWS_IN_BARCODE=90,t.MODULES_IN_CODEWORD=17,t.MODULES_IN_STOP_PATTERN=18,t.BARS_IN_MODULE=8,t.EMPTY_INT_ARRAY=new Int32Array([]),t.SYMBOL_TABLE=Int32Array.from([66142,66170,66206,66236,66290,66292,66350,66382,66396,66454,66470,66476,66594,66600,66614,66626,66628,66632,66640,66654,66662,66668,66682,66690,66718,66720,66748,66758,66776,66798,66802,66804,66820,66824,66832,66846,66848,66876,66880,66936,66950,66956,66968,66992,67006,67022,67036,67042,67044,67048,67062,67118,67150,67164,67214,67228,67256,67294,67322,67350,67366,67372,67398,67404,67416,67438,67474,67476,67490,67492,67496,67510,67618,67624,67650,67656,67664,67678,67686,67692,67706,67714,67716,67728,67742,67744,67772,67782,67788,67800,67822,67826,67828,67842,67848,67870,67872,67900,67904,67960,67974,67992,68016,68030,68046,68060,68066,68068,68072,68086,68104,68112,68126,68128,68156,68160,68216,68336,68358,68364,68376,68400,68414,68448,68476,68494,68508,68536,68546,68548,68552,68560,68574,68582,68588,68654,68686,68700,68706,68708,68712,68726,68750,68764,68792,68802,68804,68808,68816,68830,68838,68844,68858,68878,68892,68920,68976,68990,68994,68996,69e3,69008,69022,69024,69052,69062,69068,69080,69102,69106,69108,69142,69158,69164,69190,69208,69230,69254,69260,69272,69296,69310,69326,69340,69386,69394,69396,69410,69416,69430,69442,69444,69448,69456,69470,69478,69484,69554,69556,69666,69672,69698,69704,69712,69726,69754,69762,69764,69776,69790,69792,69820,69830,69836,69848,69870,69874,69876,69890,69918,69920,69948,69952,70008,70022,70040,70064,70078,70094,70108,70114,70116,70120,70134,70152,70174,70176,70264,70384,70412,70448,70462,70496,70524,70542,70556,70584,70594,70600,70608,70622,70630,70636,70664,70672,70686,70688,70716,70720,70776,70896,71136,71180,71192,71216,71230,71264,71292,71360,71416,71452,71480,71536,71550,71554,71556,71560,71568,71582,71584,71612,71622,71628,71640,71662,71726,71732,71758,71772,71778,71780,71784,71798,71822,71836,71864,71874,71880,71888,71902,71910,71916,71930,71950,71964,71992,72048,72062,72066,72068,72080,72094,72096,72124,72134,72140,72152,72174,72178,72180,72206,72220,72248,72304,72318,72416,72444,72456,72464,72478,72480,72508,72512,72568,72588,72600,72624,72638,72654,72668,72674,72676,72680,72694,72726,72742,72748,72774,72780,72792,72814,72838,72856,72880,72894,72910,72924,72930,72932,72936,72950,72966,72972,72984,73008,73022,73056,73084,73102,73116,73144,73156,73160,73168,73182,73190,73196,73210,73226,73234,73236,73250,73252,73256,73270,73282,73284,73296,73310,73318,73324,73346,73348,73352,73360,73374,73376,73404,73414,73420,73432,73454,73498,73518,73522,73524,73550,73564,73570,73572,73576,73590,73800,73822,73858,73860,73872,73886,73888,73916,73944,73970,73972,73992,74014,74016,74044,74048,74104,74118,74136,74160,74174,74210,74212,74216,74230,74244,74256,74270,74272,74360,74480,74502,74508,74544,74558,74592,74620,74638,74652,74680,74690,74696,74704,74726,74732,74782,74784,74812,74992,75232,75288,75326,75360,75388,75456,75512,75576,75632,75646,75650,75652,75664,75678,75680,75708,75718,75724,75736,75758,75808,75836,75840,75896,76016,76256,76736,76824,76848,76862,76896,76924,76992,77048,77296,77340,77368,77424,77438,77536,77564,77572,77576,77584,77600,77628,77632,77688,77702,77708,77720,77744,77758,77774,77788,77870,77902,77916,77922,77928,77966,77980,78008,78018,78024,78032,78046,78060,78074,78094,78136,78192,78206,78210,78212,78224,78238,78240,78268,78278,78284,78296,78322,78324,78350,78364,78448,78462,78560,78588,78600,78622,78624,78652,78656,78712,78726,78744,78768,78782,78798,78812,78818,78820,78824,78838,78862,78876,78904,78960,78974,79072,79100,79296,79352,79368,79376,79390,79392,79420,79424,79480,79600,79628,79640,79664,79678,79712,79740,79772,79800,79810,79812,79816,79824,79838,79846,79852,79894,79910,79916,79942,79948,79960,79982,79988,80006,80024,80048,80062,80078,80092,80098,80100,80104,80134,80140,80176,80190,80224,80252,80270,80284,80312,80328,80336,80350,80358,80364,80378,80390,80396,80408,80432,80446,80480,80508,80576,80632,80654,80668,80696,80752,80766,80776,80784,80798,80800,80828,80844,80856,80878,80882,80884,80914,80916,80930,80932,80936,80950,80962,80968,80976,80990,80998,81004,81026,81028,81040,81054,81056,81084,81094,81100,81112,81134,81154,81156,81160,81168,81182,81184,81212,81216,81272,81286,81292,81304,81328,81342,81358,81372,81380,81384,81398,81434,81454,81458,81460,81486,81500,81506,81508,81512,81526,81550,81564,81592,81602,81604,81608,81616,81630,81638,81644,81702,81708,81722,81734,81740,81752,81774,81778,81780,82050,82078,82080,82108,82180,82184,82192,82206,82208,82236,82240,82296,82316,82328,82352,82366,82402,82404,82408,82440,82448,82462,82464,82492,82496,82552,82672,82694,82700,82712,82736,82750,82784,82812,82830,82882,82884,82888,82896,82918,82924,82952,82960,82974,82976,83004,83008,83064,83184,83424,83468,83480,83504,83518,83552,83580,83648,83704,83740,83768,83824,83838,83842,83844,83848,83856,83872,83900,83910,83916,83928,83950,83984,84e3,84028,84032,84088,84208,84448,84928,85040,85054,85088,85116,85184,85240,85488,85560,85616,85630,85728,85756,85764,85768,85776,85790,85792,85820,85824,85880,85894,85900,85912,85936,85966,85980,86048,86080,86136,86256,86496,86976,88160,88188,88256,88312,88560,89056,89200,89214,89312,89340,89536,89592,89608,89616,89632,89664,89720,89840,89868,89880,89904,89952,89980,89998,90012,90040,90190,90204,90254,90268,90296,90306,90308,90312,90334,90382,90396,90424,90480,90494,90500,90504,90512,90526,90528,90556,90566,90572,90584,90610,90612,90638,90652,90680,90736,90750,90848,90876,90884,90888,90896,90910,90912,90940,90944,91e3,91014,91020,91032,91056,91070,91086,91100,91106,91108,91112,91126,91150,91164,91192,91248,91262,91360,91388,91584,91640,91664,91678,91680,91708,91712,91768,91888,91928,91952,91966,92e3,92028,92046,92060,92088,92098,92100,92104,92112,92126,92134,92140,92188,92216,92272,92384,92412,92608,92664,93168,93200,93214,93216,93244,93248,93304,93424,93664,93720,93744,93758,93792,93820,93888,93944,93980,94008,94064,94078,94084,94088,94096,94110,94112,94140,94150,94156,94168,94246,94252,94278,94284,94296,94318,94342,94348,94360,94384,94398,94414,94428,94440,94470,94476,94488,94512,94526,94560,94588,94606,94620,94648,94658,94660,94664,94672,94686,94694,94700,94714,94726,94732,94744,94768,94782,94816,94844,94912,94968,94990,95004,95032,95088,95102,95112,95120,95134,95136,95164,95180,95192,95214,95218,95220,95244,95256,95280,95294,95328,95356,95424,95480,95728,95758,95772,95800,95856,95870,95968,95996,96008,96016,96030,96032,96060,96064,96120,96152,96176,96190,96220,96226,96228,96232,96290,96292,96296,96310,96322,96324,96328,96336,96350,96358,96364,96386,96388,96392,96400,96414,96416,96444,96454,96460,96472,96494,96498,96500,96514,96516,96520,96528,96542,96544,96572,96576,96632,96646,96652,96664,96688,96702,96718,96732,96738,96740,96744,96758,96772,96776,96784,96798,96800,96828,96832,96888,97008,97030,97036,97048,97072,97086,97120,97148,97166,97180,97208,97220,97224,97232,97246,97254,97260,97326,97330,97332,97358,97372,97378,97380,97384,97398,97422,97436,97464,97474,97476,97480,97488,97502,97510,97516,97550,97564,97592,97648,97666,97668,97672,97680,97694,97696,97724,97734,97740,97752,97774,97830,97836,97850,97862,97868,97880,97902,97906,97908,97926,97932,97944,97968,97998,98012,98018,98020,98024,98038,98618,98674,98676,98838,98854,98874,98892,98904,98926,98930,98932,98968,99006,99042,99044,99048,99062,99166,99194,99246,99286,99350,99366,99372,99386,99398,99416,99438,99442,99444,99462,99504,99518,99534,99548,99554,99556,99560,99574,99590,99596,99608,99632,99646,99680,99708,99726,99740,99768,99778,99780,99784,99792,99806,99814,99820,99834,99858,99860,99874,99880,99894,99906,99920,99934,99962,99970,99972,99976,99984,99998,1e5,100028,100038,100044,100056,100078,100082,100084,100142,100174,100188,100246,100262,100268,100306,100308,100390,100396,100410,100422,100428,100440,100462,100466,100468,100486,100504,100528,100542,100558,100572,100578,100580,100584,100598,100620,100656,100670,100704,100732,100750,100792,100802,100808,100816,100830,100838,100844,100858,100888,100912,100926,100960,100988,101056,101112,101148,101176,101232,101246,101250,101252,101256,101264,101278,101280,101308,101318,101324,101336,101358,101362,101364,101410,101412,101416,101430,101442,101448,101456,101470,101478,101498,101506,101508,101520,101534,101536,101564,101580,101618,101620,101636,101640,101648,101662,101664,101692,101696,101752,101766,101784,101838,101858,101860,101864,101934,101938,101940,101966,101980,101986,101988,101992,102030,102044,102072,102082,102084,102088,102096,102138,102166,102182,102188,102214,102220,102232,102254,102282,102290,102292,102306,102308,102312,102326,102444,102458,102470,102476,102488,102514,102516,102534,102552,102576,102590,102606,102620,102626,102632,102646,102662,102668,102704,102718,102752,102780,102798,102812,102840,102850,102856,102864,102878,102886,102892,102906,102936,102974,103008,103036,103104,103160,103224,103280,103294,103298,103300,103312,103326,103328,103356,103366,103372,103384,103406,103410,103412,103472,103486,103520,103548,103616,103672,103920,103992,104048,104062,104160,104188,104194,104196,104200,104208,104224,104252,104256,104312,104326,104332,104344,104368,104382,104398,104412,104418,104420,104424,104482,104484,104514,104520,104528,104542,104550,104570,104578,104580,104592,104606,104608,104636,104652,104690,104692,104706,104712,104734,104736,104764,104768,104824,104838,104856,104910,104930,104932,104936,104968,104976,104990,104992,105020,105024,105080,105200,105240,105278,105312,105372,105410,105412,105416,105424,105446,105518,105524,105550,105564,105570,105572,105576,105614,105628,105656,105666,105672,105680,105702,105722,105742,105756,105784,105840,105854,105858,105860,105864,105872,105888,105932,105970,105972,106006,106022,106028,106054,106060,106072,106100,106118,106124,106136,106160,106174,106190,106210,106212,106216,106250,106258,106260,106274,106276,106280,106306,106308,106312,106320,106334,106348,106394,106414,106418,106420,106566,106572,106610,106612,106630,106636,106648,106672,106686,106722,106724,106728,106742,106758,106764,106776,106800,106814,106848,106876,106894,106908,106936,106946,106948,106952,106960,106974,106982,106988,107032,107056,107070,107104,107132,107200,107256,107292,107320,107376,107390,107394,107396,107400,107408,107422,107424,107452,107462,107468,107480,107502,107506,107508,107544,107568,107582,107616,107644,107712,107768,108016,108060,108088,108144,108158,108256,108284,108290,108292,108296,108304,108318,108320,108348,108352,108408,108422,108428,108440,108464,108478,108494,108508,108514,108516,108520,108592,108640,108668,108736,108792,109040,109536,109680,109694,109792,109820,110016,110072,110084,110088,110096,110112,110140,110144,110200,110320,110342,110348,110360,110384,110398,110432,110460,110478,110492,110520,110532,110536,110544,110558,110658,110686,110714,110722,110724,110728,110736,110750,110752,110780,110796,110834,110836,110850,110852,110856,110864,110878,110880,110908,110912,110968,110982,111e3,111054,111074,111076,111080,111108,111112,111120,111134,111136,111164,111168,111224,111344,111372,111422,111456,111516,111554,111556,111560,111568,111590,111632,111646,111648,111676,111680,111736,111856,112096,112152,112224,112252,112320,112440,112514,112516,112520,112528,112542,112544,112588,112686,112718,112732,112782,112796,112824,112834,112836,112840,112848,112870,112890,112910,112924,112952,113008,113022,113026,113028,113032,113040,113054,113056,113100,113138,113140,113166,113180,113208,113264,113278,113376,113404,113416,113424,113440,113468,113472,113560,113614,113634,113636,113640,113686,113702,113708,113734,113740,113752,113778,113780,113798,113804,113816,113840,113854,113870,113890,113892,113896,113926,113932,113944,113968,113982,114016,114044,114076,114114,114116,114120,114128,114150,114170,114194,114196,114210,114212,114216,114242,114244,114248,114256,114270,114278,114306,114308,114312,114320,114334,114336,114364,114380,114420,114458,114478,114482,114484,114510,114524,114530,114532,114536,114842,114866,114868,114970,114994,114996,115042,115044,115048,115062,115130,115226,115250,115252,115278,115292,115298,115300,115304,115318,115342,115394,115396,115400,115408,115422,115430,115436,115450,115478,115494,115514,115526,115532,115570,115572,115738,115758,115762,115764,115790,115804,115810,115812,115816,115830,115854,115868,115896,115906,115912,115920,115934,115942,115948,115962,115996,116024,116080,116094,116098,116100,116104,116112,116126,116128,116156,116166,116172,116184,116206,116210,116212,116246,116262,116268,116282,116294,116300,116312,116334,116338,116340,116358,116364,116376,116400,116414,116430,116444,116450,116452,116456,116498,116500,116514,116520,116534,116546,116548,116552,116560,116574,116582,116588,116602,116654,116694,116714,116762,116782,116786,116788,116814,116828,116834,116836,116840,116854,116878,116892,116920,116930,116936,116944,116958,116966,116972,116986,117006,117048,117104,117118,117122,117124,117136,117150,117152,117180,117190,117196,117208,117230,117234,117236,117304,117360,117374,117472,117500,117506,117508,117512,117520,117536,117564,117568,117624,117638,117644,117656,117680,117694,117710,117724,117730,117732,117736,117750,117782,117798,117804,117818,117830,117848,117874,117876,117894,117936,117950,117966,117986,117988,117992,118022,118028,118040,118064,118078,118112,118140,118172,118210,118212,118216,118224,118238,118246,118266,118306,118312,118338,118352,118366,118374,118394,118402,118404,118408,118416,118430,118432,118460,118476,118514,118516,118574,118578,118580,118606,118620,118626,118628,118632,118678,118694,118700,118730,118738,118740,118830,118834,118836,118862,118876,118882,118884,118888,118902,118926,118940,118968,118978,118980,118984,118992,119006,119014,119020,119034,119068,119096,119152,119166,119170,119172,119176,119184,119198,119200,119228,119238,119244,119256,119278,119282,119284,119324,119352,119408,119422,119520,119548,119554,119556,119560,119568,119582,119584,119612,119616,119672,119686,119692,119704,119728,119742,119758,119772,119778,119780,119784,119798,119920,119934,120032,120060,120256,120312,120324,120328,120336,120352,120384,120440,120560,120582,120588,120600,120624,120638,120672,120700,120718,120732,120760,120770,120772,120776,120784,120798,120806,120812,120870,120876,120890,120902,120908,120920,120946,120948,120966,120972,120984,121008,121022,121038,121058,121060,121064,121078,121100,121112,121136,121150,121184,121212,121244,121282,121284,121288,121296,121318,121338,121356,121368,121392,121406,121440,121468,121536,121592,121656,121730,121732,121736,121744,121758,121760,121804,121842,121844,121890,121922,121924,121928,121936,121950,121958,121978,121986,121988,121992,122e3,122014,122016,122044,122060,122098,122100,122116,122120,122128,122142,122144,122172,122176,122232,122246,122264,122318,122338,122340,122344,122414,122418,122420,122446,122460,122466,122468,122472,122510,122524,122552,122562,122564,122568,122576,122598,122618,122646,122662,122668,122694,122700,122712,122738,122740,122762,122770,122772,122786,122788,122792,123018,123026,123028,123042,123044,123048,123062,123098,123146,123154,123156,123170,123172,123176,123190,123202,123204,123208,123216,123238,123244,123258,123290,123314,123316,123402,123410,123412,123426,123428,123432,123446,123458,123464,123472,123486,123494,123500,123514,123522,123524,123528,123536,123552,123580,123590,123596,123608,123630,123634,123636,123674,123698,123700,123740,123746,123748,123752,123834,123914,123922,123924,123938,123944,123958,123970,123976,123984,123998,124006,124012,124026,124034,124036,124048,124062,124064,124092,124102,124108,124120,124142,124146,124148,124162,124164,124168,124176,124190,124192,124220,124224,124280,124294,124300,124312,124336,124350,124366,124380,124386,124388,124392,124406,124442,124462,124466,124468,124494,124508,124514,124520,124558,124572,124600,124610,124612,124616,124624,124646,124666,124694,124710,124716,124730,124742,124748,124760,124786,124788,124818,124820,124834,124836,124840,124854,124946,124948,124962,124964,124968,124982,124994,124996,125e3,125008,125022,125030,125036,125050,125058,125060,125064,125072,125086,125088,125116,125126,125132,125144,125166,125170,125172,125186,125188,125192,125200,125216,125244,125248,125304,125318,125324,125336,125360,125374,125390,125404,125410,125412,125416,125430,125444,125448,125456,125472,125504,125560,125680,125702,125708,125720,125744,125758,125792,125820,125838,125852,125880,125890,125892,125896,125904,125918,125926,125932,125978,125998,126002,126004,126030,126044,126050,126052,126056,126094,126108,126136,126146,126148,126152,126160,126182,126202,126222,126236,126264,126320,126334,126338,126340,126344,126352,126366,126368,126412,126450,126452,126486,126502,126508,126522,126534,126540,126552,126574,126578,126580,126598,126604,126616,126640,126654,126670,126684,126690,126692,126696,126738,126754,126756,126760,126774,126786,126788,126792,126800,126814,126822,126828,126842,126894,126898,126900,126934,127126,127142,127148,127162,127178,127186,127188,127254,127270,127276,127290,127302,127308,127320,127342,127346,127348,127370,127378,127380,127394,127396,127400,127450,127510,127526,127532,127546,127558,127576,127598,127602,127604,127622,127628,127640,127664,127678,127694,127708,127714,127716,127720,127734,127754,127762,127764,127778,127784,127810,127812,127816,127824,127838,127846,127866,127898,127918,127922,127924,128022,128038,128044,128058,128070,128076,128088,128110,128114,128116,128134,128140,128152,128176,128190,128206,128220,128226,128228,128232,128246,128262,128268,128280,128304,128318,128352,128380,128398,128412,128440,128450,128452,128456,128464,128478,128486,128492,128506,128522,128530,128532,128546,128548,128552,128566,128578,128580,128584,128592,128606,128614,128634,128642,128644,128648,128656,128670,128672,128700,128716,128754,128756,128794,128814,128818,128820,128846,128860,128866,128868,128872,128886,128918,128934,128940,128954,128978,128980,129178,129198,129202,129204,129238,129258,129306,129326,129330,129332,129358,129372,129378,129380,129384,129398,129430,129446,129452,129466,129482,129490,129492,129562,129582,129586,129588,129614,129628,129634,129636,129640,129654,129678,129692,129720,129730,129732,129736,129744,129758,129766,129772,129814,129830,129836,129850,129862,129868,129880,129902,129906,129908,129930,129938,129940,129954,129956,129960,129974,130010]),t.CODEWORD_TABLE=Int32Array.from([2627,1819,2622,2621,1813,1812,2729,2724,2723,2779,2774,2773,902,896,908,868,865,861,859,2511,873,871,1780,835,2493,825,2491,842,837,844,1764,1762,811,810,809,2483,807,2482,806,2480,815,814,813,812,2484,817,816,1745,1744,1742,1746,2655,2637,2635,2626,2625,2623,2628,1820,2752,2739,2737,2728,2727,2725,2730,2785,2783,2778,2777,2775,2780,787,781,747,739,736,2413,754,752,1719,692,689,681,2371,678,2369,700,697,694,703,1688,1686,642,638,2343,631,2341,627,2338,651,646,643,2345,654,652,1652,1650,1647,1654,601,599,2322,596,2321,594,2319,2317,611,610,608,606,2324,603,2323,615,614,612,1617,1616,1614,1612,616,1619,1618,2575,2538,2536,905,901,898,909,2509,2507,2504,870,867,864,860,2512,875,872,1781,2490,2489,2487,2485,1748,836,834,832,830,2494,827,2492,843,841,839,845,1765,1763,2701,2676,2674,2653,2648,2656,2634,2633,2631,2629,1821,2638,2636,2770,2763,2761,2750,2745,2753,2736,2735,2733,2731,1848,2740,2738,2786,2784,591,588,576,569,566,2296,1590,537,534,526,2276,522,2274,545,542,539,548,1572,1570,481,2245,466,2242,462,2239,492,485,482,2249,496,494,1534,1531,1528,1538,413,2196,406,2191,2188,425,419,2202,415,2199,432,430,427,1472,1467,1464,433,1476,1474,368,367,2160,365,2159,362,2157,2155,2152,378,377,375,2166,372,2165,369,2162,383,381,379,2168,1419,1418,1416,1414,385,1411,384,1423,1422,1420,1424,2461,802,2441,2439,790,786,783,794,2409,2406,2403,750,742,738,2414,756,753,1720,2367,2365,2362,2359,1663,693,691,684,2373,680,2370,702,699,696,704,1690,1687,2337,2336,2334,2332,1624,2329,1622,640,637,2344,634,2342,630,2340,650,648,645,2346,655,653,1653,1651,1649,1655,2612,2597,2595,2571,2568,2565,2576,2534,2529,2526,1787,2540,2537,907,904,900,910,2503,2502,2500,2498,1768,2495,1767,2510,2508,2506,869,866,863,2513,876,874,1782,2720,2713,2711,2697,2694,2691,2702,2672,2670,2664,1828,2678,2675,2647,2646,2644,2642,1823,2639,1822,2654,2652,2650,2657,2771,1855,2765,2762,1850,1849,2751,2749,2747,2754,353,2148,344,342,336,2142,332,2140,345,1375,1373,306,2130,299,2128,295,2125,319,314,311,2132,1354,1352,1349,1356,262,257,2101,253,2096,2093,274,273,267,2107,263,2104,280,278,275,1316,1311,1308,1320,1318,2052,202,2050,2044,2040,219,2063,212,2060,208,2055,224,221,2066,1260,1258,1252,231,1248,229,1266,1264,1261,1268,155,1998,153,1996,1994,1991,1988,165,164,2007,162,2006,159,2003,2e3,172,171,169,2012,166,2010,1186,1184,1182,1179,175,1176,173,1192,1191,1189,1187,176,1194,1193,2313,2307,2305,592,589,2294,2292,2289,578,572,568,2297,580,1591,2272,2267,2264,1547,538,536,529,2278,525,2275,547,544,541,1574,1571,2237,2235,2229,1493,2225,1489,478,2247,470,2244,465,2241,493,488,484,2250,498,495,1536,1533,1530,1539,2187,2186,2184,2182,1432,2179,1430,2176,1427,414,412,2197,409,2195,405,2193,2190,426,424,421,2203,418,2201,431,429,1473,1471,1469,1466,434,1477,1475,2478,2472,2470,2459,2457,2454,2462,803,2437,2432,2429,1726,2443,2440,792,789,785,2401,2399,2393,1702,2389,1699,2411,2408,2405,745,741,2415,758,755,1721,2358,2357,2355,2353,1661,2350,1660,2347,1657,2368,2366,2364,2361,1666,690,687,2374,683,2372,701,698,705,1691,1689,2619,2617,2610,2608,2605,2613,2593,2588,2585,1803,2599,2596,2563,2561,2555,1797,2551,1795,2573,2570,2567,2577,2525,2524,2522,2520,1786,2517,1785,2514,1783,2535,2533,2531,2528,1788,2541,2539,906,903,911,2721,1844,2715,2712,1838,1836,2699,2696,2693,2703,1827,1826,1824,2673,2671,2669,2666,1829,2679,2677,1858,1857,2772,1854,1853,1851,1856,2766,2764,143,1987,139,1986,135,133,131,1984,128,1983,125,1981,138,137,136,1985,1133,1132,1130,112,110,1974,107,1973,104,1971,1969,122,121,119,117,1977,114,1976,124,1115,1114,1112,1110,1117,1116,84,83,1953,81,1952,78,1950,1948,1945,94,93,91,1959,88,1958,85,1955,99,97,95,1961,1086,1085,1083,1081,1078,100,1090,1089,1087,1091,49,47,1917,44,1915,1913,1910,1907,59,1926,56,1925,53,1922,1919,66,64,1931,61,1929,1042,1040,1038,71,1035,70,1032,68,1048,1047,1045,1043,1050,1049,12,10,1869,1867,1864,1861,21,1880,19,1877,1874,1871,28,1888,25,1886,22,1883,982,980,977,974,32,30,991,989,987,984,34,995,994,992,2151,2150,2147,2146,2144,356,355,354,2149,2139,2138,2136,2134,1359,343,341,338,2143,335,2141,348,347,346,1376,1374,2124,2123,2121,2119,1326,2116,1324,310,308,305,2131,302,2129,298,2127,320,318,316,313,2133,322,321,1355,1353,1351,1357,2092,2091,2089,2087,1276,2084,1274,2081,1271,259,2102,256,2100,252,2098,2095,272,269,2108,266,2106,281,279,277,1317,1315,1313,1310,282,1321,1319,2039,2037,2035,2032,1203,2029,1200,1197,207,2053,205,2051,201,2049,2046,2043,220,218,2064,215,2062,211,2059,228,226,223,2069,1259,1257,1254,232,1251,230,1267,1265,1263,2316,2315,2312,2311,2309,2314,2304,2303,2301,2299,1593,2308,2306,590,2288,2287,2285,2283,1578,2280,1577,2295,2293,2291,579,577,574,571,2298,582,581,1592,2263,2262,2260,2258,1545,2255,1544,2252,1541,2273,2271,2269,2266,1550,535,532,2279,528,2277,546,543,549,1575,1573,2224,2222,2220,1486,2217,1485,2214,1482,1479,2238,2236,2234,2231,1496,2228,1492,480,477,2248,473,2246,469,2243,490,487,2251,497,1537,1535,1532,2477,2476,2474,2479,2469,2468,2466,2464,1730,2473,2471,2453,2452,2450,2448,1729,2445,1728,2460,2458,2456,2463,805,804,2428,2427,2425,2423,1725,2420,1724,2417,1722,2438,2436,2434,2431,1727,2444,2442,793,791,788,795,2388,2386,2384,1697,2381,1696,2378,1694,1692,2402,2400,2398,2395,1703,2392,1701,2412,2410,2407,751,748,744,2416,759,757,1807,2620,2618,1806,1805,2611,2609,2607,2614,1802,1801,1799,2594,2592,2590,2587,1804,2600,2598,1794,1793,1791,1789,2564,2562,2560,2557,1798,2554,1796,2574,2572,2569,2578,1847,1846,2722,1843,1842,1840,1845,2716,2714,1835,1834,1832,1830,1839,1837,2700,2698,2695,2704,1817,1811,1810,897,862,1777,829,826,838,1760,1758,808,2481,1741,1740,1738,1743,2624,1818,2726,2776,782,740,737,1715,686,679,695,1682,1680,639,628,2339,647,644,1645,1643,1640,1648,602,600,597,595,2320,593,2318,609,607,604,1611,1610,1608,1606,613,1615,1613,2328,926,924,892,886,899,857,850,2505,1778,824,823,821,819,2488,818,2486,833,831,828,840,1761,1759,2649,2632,2630,2746,2734,2732,2782,2781,570,567,1587,531,527,523,540,1566,1564,476,467,463,2240,486,483,1524,1521,1518,1529,411,403,2192,399,2189,423,416,1462,1457,1454,428,1468,1465,2210,366,363,2158,360,2156,357,2153,376,373,370,2163,1410,1409,1407,1405,382,1402,380,1417,1415,1412,1421,2175,2174,777,774,771,784,732,725,722,2404,743,1716,676,674,668,2363,665,2360,685,1684,1681,626,624,622,2335,620,2333,617,2330,641,635,649,1646,1644,1642,2566,928,925,2530,2527,894,891,888,2501,2499,2496,858,856,854,851,1779,2692,2668,2665,2645,2643,2640,2651,2768,2759,2757,2744,2743,2741,2748,352,1382,340,337,333,1371,1369,307,300,296,2126,315,312,1347,1342,1350,261,258,250,2097,246,2094,271,268,264,1306,1301,1298,276,1312,1309,2115,203,2048,195,2045,191,2041,213,209,2056,1246,1244,1238,225,1234,222,1256,1253,1249,1262,2080,2079,154,1997,150,1995,147,1992,1989,163,160,2004,156,2001,1175,1174,1172,1170,1167,170,1164,167,1185,1183,1180,1177,174,1190,1188,2025,2024,2022,587,586,564,559,556,2290,573,1588,520,518,512,2268,508,2265,530,1568,1565,461,457,2233,450,2230,446,2226,479,471,489,1526,1523,1520,397,395,2185,392,2183,389,2180,2177,410,2194,402,422,1463,1461,1459,1456,1470,2455,799,2433,2430,779,776,773,2397,2394,2390,734,728,724,746,1717,2356,2354,2351,2348,1658,677,675,673,670,667,688,1685,1683,2606,2589,2586,2559,2556,2552,927,2523,2521,2518,2515,1784,2532,895,893,890,2718,2709,2707,2689,2687,2684,2663,2662,2660,2658,1825,2667,2769,1852,2760,2758,142,141,1139,1138,134,132,129,126,1982,1129,1128,1126,1131,113,111,108,105,1972,101,1970,120,118,115,1109,1108,1106,1104,123,1113,1111,82,79,1951,75,1949,72,1946,92,89,86,1956,1077,1076,1074,1072,98,1069,96,1084,1082,1079,1088,1968,1967,48,45,1916,42,1914,39,1911,1908,60,57,54,1923,50,1920,1031,1030,1028,1026,67,1023,65,1020,62,1041,1039,1036,1033,69,1046,1044,1944,1943,1941,11,9,1868,7,1865,1862,1859,20,1878,16,1875,13,1872,970,968,966,963,29,960,26,23,983,981,978,975,33,971,31,990,988,985,1906,1904,1902,993,351,2145,1383,331,330,328,326,2137,323,2135,339,1372,1370,294,293,291,289,2122,286,2120,283,2117,309,303,317,1348,1346,1344,245,244,242,2090,239,2088,236,2085,2082,260,2099,249,270,1307,1305,1303,1300,1314,189,2038,186,2036,183,2033,2030,2026,206,198,2047,194,216,1247,1245,1243,1240,227,1237,1255,2310,2302,2300,2286,2284,2281,565,563,561,558,575,1589,2261,2259,2256,2253,1542,521,519,517,514,2270,511,533,1569,1567,2223,2221,2218,2215,1483,2211,1480,459,456,453,2232,449,474,491,1527,1525,1522,2475,2467,2465,2451,2449,2446,801,800,2426,2424,2421,2418,1723,2435,780,778,775,2387,2385,2382,2379,1695,2375,1693,2396,735,733,730,727,749,1718,2616,2615,2604,2603,2601,2584,2583,2581,2579,1800,2591,2550,2549,2547,2545,1792,2542,1790,2558,929,2719,1841,2710,2708,1833,1831,2690,2688,2686,1815,1809,1808,1774,1756,1754,1737,1736,1734,1739,1816,1711,1676,1674,633,629,1638,1636,1633,1641,598,1605,1604,1602,1600,605,1609,1607,2327,887,853,1775,822,820,1757,1755,1584,524,1560,1558,468,464,1514,1511,1508,1519,408,404,400,1452,1447,1444,417,1458,1455,2208,364,361,358,2154,1401,1400,1398,1396,374,1393,371,1408,1406,1403,1413,2173,2172,772,726,723,1712,672,669,666,682,1678,1675,625,623,621,618,2331,636,632,1639,1637,1635,920,918,884,880,889,849,848,847,846,2497,855,852,1776,2641,2742,2787,1380,334,1367,1365,301,297,1340,1338,1335,1343,255,251,247,1296,1291,1288,265,1302,1299,2113,204,196,192,2042,1232,1230,1224,214,1220,210,1242,1239,1235,1250,2077,2075,151,148,1993,144,1990,1163,1162,1160,1158,1155,161,1152,157,1173,1171,1168,1165,168,1181,1178,2021,2020,2018,2023,585,560,557,1585,516,509,1562,1559,458,447,2227,472,1516,1513,1510,398,396,393,390,2181,386,2178,407,1453,1451,1449,1446,420,1460,2209,769,764,720,712,2391,729,1713,664,663,661,659,2352,656,2349,671,1679,1677,2553,922,919,2519,2516,885,883,881,2685,2661,2659,2767,2756,2755,140,1137,1136,130,127,1125,1124,1122,1127,109,106,102,1103,1102,1100,1098,116,1107,1105,1980,80,76,73,1947,1068,1067,1065,1063,90,1060,87,1075,1073,1070,1080,1966,1965,46,43,40,1912,36,1909,1019,1018,1016,1014,58,1011,55,1008,51,1029,1027,1024,1021,63,1037,1034,1940,1939,1937,1942,8,1866,4,1863,1,1860,956,954,952,949,946,17,14,969,967,964,961,27,957,24,979,976,972,1901,1900,1898,1896,986,1905,1903,350,349,1381,329,327,324,1368,1366,292,290,287,284,2118,304,1341,1339,1337,1345,243,240,237,2086,233,2083,254,1297,1295,1293,1290,1304,2114,190,187,184,2034,180,2031,177,2027,199,1233,1231,1229,1226,217,1223,1241,2078,2076,584,555,554,552,550,2282,562,1586,507,506,504,502,2257,499,2254,515,1563,1561,445,443,441,2219,438,2216,435,2212,460,454,475,1517,1515,1512,2447,798,797,2422,2419,770,768,766,2383,2380,2376,721,719,717,714,731,1714,2602,2582,2580,2548,2546,2543,923,921,2717,2706,2705,2683,2682,2680,1771,1752,1750,1733,1732,1731,1735,1814,1707,1670,1668,1631,1629,1626,1634,1599,1598,1596,1594,1603,1601,2326,1772,1753,1751,1581,1554,1552,1504,1501,1498,1509,1442,1437,1434,401,1448,1445,2206,1392,1391,1389,1387,1384,359,1399,1397,1394,1404,2171,2170,1708,1672,1669,619,1632,1630,1628,1773,1378,1363,1361,1333,1328,1336,1286,1281,1278,248,1292,1289,2111,1218,1216,1210,197,1206,193,1228,1225,1221,1236,2073,2071,1151,1150,1148,1146,152,1143,149,1140,145,1161,1159,1156,1153,158,1169,1166,2017,2016,2014,2019,1582,510,1556,1553,452,448,1506,1500,394,391,387,1443,1441,1439,1436,1450,2207,765,716,713,1709,662,660,657,1673,1671,916,914,879,878,877,882,1135,1134,1121,1120,1118,1123,1097,1096,1094,1092,103,1101,1099,1979,1059,1058,1056,1054,77,1051,74,1066,1064,1061,1071,1964,1963,1007,1006,1004,1002,999,41,996,37,1017,1015,1012,1009,52,1025,1022,1936,1935,1933,1938,942,940,938,935,932,5,2,955,953,950,947,18,943,15,965,962,958,1895,1894,1892,1890,973,1899,1897,1379,325,1364,1362,288,285,1334,1332,1330,241,238,234,1287,1285,1283,1280,1294,2112,188,185,181,178,2028,1219,1217,1215,1212,200,1209,1227,2074,2072,583,553,551,1583,505,503,500,513,1557,1555,444,442,439,436,2213,455,451,1507,1505,1502,796,763,762,760,767,711,710,708,706,2377,718,715,1710,2544,917,915,2681,1627,1597,1595,2325,1769,1749,1747,1499,1438,1435,2204,1390,1388,1385,1395,2169,2167,1704,1665,1662,1625,1623,1620,1770,1329,1282,1279,2109,1214,1207,1222,2068,2065,1149,1147,1144,1141,146,1157,1154,2013,2011,2008,2015,1579,1549,1546,1495,1487,1433,1431,1428,1425,388,1440,2205,1705,658,1667,1664,1119,1095,1093,1978,1057,1055,1052,1062,1962,1960,1005,1003,1e3,997,38,1013,1010,1932,1930,1927,1934,941,939,936,933,6,930,3,951,948,944,1889,1887,1884,1881,959,1893,1891,35,1377,1360,1358,1327,1325,1322,1331,1277,1275,1272,1269,235,1284,2110,1205,1204,1201,1198,182,1195,179,1213,2070,2067,1580,501,1551,1548,440,437,1497,1494,1490,1503,761,709,707,1706,913,912,2198,1386,2164,2161,1621,1766,2103,1208,2058,2054,1145,1142,2005,2002,1999,2009,1488,1429,1426,2200,1698,1659,1656,1975,1053,1957,1954,1001,998,1924,1921,1918,1928,937,934,931,1879,1876,1873,1870,945,1885,1882,1323,1273,1270,2105,1202,1199,1196,1211,2061,2057,1576,1543,1540,1484,1481,1478,1491,1700]),t}(),zn=function(){function t(t,e){this.bits=t,this.points=e}return t.prototype.getBits=function(){return this.bits},t.prototype.getPoints=function(){return this.points},t}(),Yn=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Zn=function(){function t(){}return t.detectMultiple=function(e,r,n){var o=e.getBlackMatrix(),i=t.detect(n,o);return i.length||((o=o.clone()).rotate180(),i=t.detect(n,o)),new zn(o,i)},t.detect=function(e,r){for(var n,o,i=new Array,a=0,s=0,u=!1;a<r.getHeight();){var c=t.findVertices(r,a,s);if(null!=c[0]||null!=c[3]){if(u=!0,i.push(c),!e)break;null!=c[2]?(s=Math.trunc(c[2].getX()),a=Math.trunc(c[2].getY())):(s=Math.trunc(c[4].getX()),a=Math.trunc(c[4].getY()))}else{if(!u)break;u=!1,s=0;try{for(var f=(n=void 0,Yn(i)),h=f.next();!h.done;h=f.next()){var l=h.value;null!=l[1]&&(a=Math.trunc(Math.max(a,l[1].getY()))),null!=l[3]&&(a=Math.max(a,Math.trunc(l[3].getY())))}}catch(d){n={error:d}}finally{try{h&&!h.done&&(o=f.return)&&o.call(f)}finally{if(n)throw n.error}}a+=t.ROW_STEP}}return i},t.findVertices=function(e,r,n){var o=e.getHeight(),i=e.getWidth(),a=new Array(8);return t.copyToResult(a,t.findRowsWithPattern(e,o,i,r,n,t.START_PATTERN),t.INDEXES_START_PATTERN),null!=a[4]&&(n=Math.trunc(a[4].getX()),r=Math.trunc(a[4].getY())),t.copyToResult(a,t.findRowsWithPattern(e,o,i,r,n,t.STOP_PATTERN),t.INDEXES_STOP_PATTERN),a},t.copyToResult=function(t,e,r){for(var n=0;n<r.length;n++)t[r[n]]=e[n]},t.findRowsWithPattern=function(e,r,n,o,i,a){for(var s=new Array(4),u=!1,c=new Int32Array(a.length);o<r;o+=t.ROW_STEP)if(null!=(d=t.findGuardPattern(e,i,o,n,!1,a,c))){for(;o>0;){if(null==(l=t.findGuardPattern(e,i,--o,n,!1,a,c))){o++;break}d=l}s[0]=new $t(d[0],o),s[1]=new $t(d[1],o),u=!0;break}var f=o+1;if(u){for(var h=0,l=Int32Array.from([Math.trunc(s[0].getX()),Math.trunc(s[1].getX())]);f<r;f++){var d;if(null!=(d=t.findGuardPattern(e,l[0],f,n,!1,a,c))&&Math.abs(l[0]-d[0])<t.MAX_PATTERN_DRIFT&&Math.abs(l[1]-d[1])<t.MAX_PATTERN_DRIFT)l=d,h=0;else{if(h>t.SKIPPED_ROW_COUNT_MAX)break;h++}}f-=h+1,s[2]=new $t(l[0],f),s[3]=new $t(l[1],f)}return f-o<t.BARCODE_MIN_HEIGHT&&rt.fill(s,null),s},t.findGuardPattern=function(e,r,n,o,i,a,s){rt.fillWithin(s,0,s.length,0);for(var u=r,c=0;e.get(u,n)&&u>0&&c++<t.MAX_PIXEL_DRIFT;)u--;for(var f=u,h=0,l=a.length,d=i;f<o;f++)if(e.get(f,n)!==d)s[h]++;else{if(h===l-1){if(t.patternMatchVariance(s,a,t.MAX_INDIVIDUAL_VARIANCE)<t.MAX_AVG_VARIANCE)return new Int32Array([u,f]);u+=s[0]+s[1],q.arraycopy(s,2,s,0,h-1),s[h-1]=0,s[h]=0,h--}else h++;s[h]=1,d=!d}return h===l-1&&t.patternMatchVariance(s,a,t.MAX_INDIVIDUAL_VARIANCE)<t.MAX_AVG_VARIANCE?new Int32Array([u,f-1]):null},t.patternMatchVariance=function(t,e,r){for(var n=t.length,o=0,i=0,a=0;a<n;a++)o+=t[a],i+=e[a];if(o<i)return 1/0;var s=o/i;r*=s;for(var u=0,c=0;c<n;c++){var f=t[c],h=e[c]*s,l=f>h?f-h:h-f;if(l>r)return 1/0;u+=l}return u/o},t.INDEXES_START_PATTERN=Int32Array.from([0,4,1,5]),t.INDEXES_STOP_PATTERN=Int32Array.from([6,2,7,3]),t.MAX_AVG_VARIANCE=.42,t.MAX_INDIVIDUAL_VARIANCE=.8,t.START_PATTERN=Int32Array.from([8,1,1,1,1,1,1,3]),t.STOP_PATTERN=Int32Array.from([7,1,1,3,1,1,1,2,1]),t.MAX_PIXEL_DRIFT=3,t.MAX_PATTERN_DRIFT=5,t.SKIPPED_ROW_COUNT_MAX=25,t.ROW_STEP=5,t.BARCODE_MIN_HEIGHT=10,t}(),Kn=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},qn=function(){function t(t,e){if(0===e.length)throw new j;this.field=t;var r=e.length;if(r>1&&0===e[0]){for(var n=1;n<r&&0===e[n];)n++;n===r?this.coefficients=new Int32Array([0]):(this.coefficients=new Int32Array(r-n),q.arraycopy(e,n,this.coefficients,0,this.coefficients.length))}else this.coefficients=e}return t.prototype.getCoefficients=function(){return this.coefficients},t.prototype.getDegree=function(){return this.coefficients.length-1},t.prototype.isZero=function(){return 0===this.coefficients[0]},t.prototype.getCoefficient=function(t){return this.coefficients[this.coefficients.length-1-t]},t.prototype.evaluateAt=function(t){var e,r;if(0===t)return this.getCoefficient(0);if(1===t){var n=0;try{for(var o=Kn(this.coefficients),i=o.next();!i.done;i=o.next()){var a=i.value;n=this.field.add(n,a)}}catch(f){e={error:f}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n}for(var s=this.coefficients[0],u=this.coefficients.length,c=1;c<u;c++)s=this.field.add(this.field.multiply(t,s),this.coefficients[c]);return s},t.prototype.add=function(e){if(!this.field.equals(e.field))throw new j("ModulusPolys do not have same ModulusGF field");if(this.isZero())return e;if(e.isZero())return this;var r=this.coefficients,n=e.coefficients;if(r.length>n.length){var o=r;r=n,n=o}var i=new Int32Array(n.length),a=n.length-r.length;q.arraycopy(n,0,i,0,a);for(var s=a;s<n.length;s++)i[s]=this.field.add(r[s-a],n[s]);return new t(this.field,i)},t.prototype.subtract=function(t){if(!this.field.equals(t.field))throw new j("ModulusPolys do not have same ModulusGF field");return t.isZero()?this:this.add(t.negative())},t.prototype.multiply=function(e){return e instanceof t?this.multiplyOther(e):this.multiplyScalar(e)},t.prototype.multiplyOther=function(e){if(!this.field.equals(e.field))throw new j("ModulusPolys do not have same ModulusGF field");if(this.isZero()||e.isZero())return new t(this.field,new Int32Array([0]));for(var r=this.coefficients,n=r.length,o=e.coefficients,i=o.length,a=new Int32Array(n+i-1),s=0;s<n;s++)for(var u=r[s],c=0;c<i;c++)a[s+c]=this.field.add(a[s+c],this.field.multiply(u,o[c]));return new t(this.field,a)},t.prototype.negative=function(){for(var e=this.coefficients.length,r=new Int32Array(e),n=0;n<e;n++)r[n]=this.field.subtract(0,this.coefficients[n]);return new t(this.field,r)},t.prototype.multiplyScalar=function(e){if(0===e)return new t(this.field,new Int32Array([0]));if(1===e)return this;for(var r=this.coefficients.length,n=new Int32Array(r),o=0;o<r;o++)n[o]=this.field.multiply(this.coefficients[o],e);return new t(this.field,n)},t.prototype.multiplyByMonomial=function(e,r){if(e<0)throw new j;if(0===r)return new t(this.field,new Int32Array([0]));for(var n=this.coefficients.length,o=new Int32Array(n+e),i=0;i<n;i++)o[i]=this.field.multiply(this.coefficients[i],r);return new t(this.field,o)},t.prototype.toString=function(){for(var t=new gt,e=this.getDegree();e>=0;e--){var r=this.getCoefficient(e);0!==r&&(r<0?(t.append(" - "),r=-r):t.length()>0&&t.append(" + "),0!==e&&1===r||t.append(r),0!==e&&(1===e?t.append("x"):(t.append("x^"),t.append(e))))}return t.toString()},t}(),Qn=function(){function t(){}return t.prototype.add=function(t,e){return(t+e)%this.modulus},t.prototype.subtract=function(t,e){return(this.modulus+t-e)%this.modulus},t.prototype.exp=function(t){return this.expTable[t]},t.prototype.log=function(t){if(0===t)throw new j;return this.logTable[t]},t.prototype.inverse=function(t){if(0===t)throw new Gt;return this.expTable[this.modulus-this.logTable[t]-1]},t.prototype.multiply=function(t,e){return 0===t||0===e?0:this.expTable[(this.logTable[t]+this.logTable[e])%(this.modulus-1)]},t.prototype.getSize=function(){return this.modulus},t.prototype.equals=function(t){return t===this},t}(),Jn=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),$n=function(t){function e(e,r){var n=t.call(this)||this;n.modulus=e,n.expTable=new Int32Array(e),n.logTable=new Int32Array(e);for(var o=1,i=0;i<e;i++)n.expTable[i]=o,o=o*r%e;for(i=0;i<e-1;i++)n.logTable[n.expTable[i]]=i;return n.zero=new qn(n,new Int32Array([0])),n.one=new qn(n,new Int32Array([1])),n}return Jn(e,t),e.prototype.getZero=function(){return this.zero},e.prototype.getOne=function(){return this.one},e.prototype.buildMonomial=function(t,e){if(t<0)throw new j;if(0===e)return this.zero;var r=new Int32Array(t+1);return r[0]=e,new qn(this,r)},e.PDF417_GF=new e(jn.NUMBER_OF_CODEWORDS,3),e}(Qn),to=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},eo=function(){function t(){this.field=$n.PDF417_GF}return t.prototype.decode=function(t,e,r){for(var n,o,i=new qn(this.field,t),a=new Int32Array(e),s=!1,u=e;u>0;u--){var c=i.evaluateAt(this.field.exp(u));a[e-u]=c,0!==c&&(s=!0)}if(!s)return 0;var f=this.field.getOne();if(null!=r)try{for(var h=to(r),l=h.next();!l.done;l=h.next()){var d=l.value,p=this.field.exp(t.length-1-d),g=new qn(this.field,new Int32Array([this.field.subtract(0,p),1]));f=f.multiply(g)}}catch(E){n={error:E}}finally{try{l&&!l.done&&(o=h.return)&&o.call(h)}finally{if(n)throw n.error}}var y=new qn(this.field,a),w=this.runEuclideanAlgorithm(this.field.buildMonomial(e,1),y,e),v=w[0],_=w[1],m=this.findErrorLocations(v),C=this.findErrorMagnitudes(_,v,m);for(u=0;u<m.length;u++){var A=t.length-1-this.field.log(m[u]);if(A<0)throw Z.getChecksumInstance();t[A]=this.field.subtract(t[A],C[u])}return m.length},t.prototype.runEuclideanAlgorithm=function(t,e,r){if(t.getDegree()<e.getDegree()){var n=t;t=e,e=n}for(var o=t,i=e,a=this.field.getZero(),s=this.field.getOne();i.getDegree()>=Math.round(r/2);){var u=o,c=a;if(a=s,(o=i).isZero())throw Z.getChecksumInstance();i=u;for(var f=this.field.getZero(),h=o.getCoefficient(o.getDegree()),l=this.field.inverse(h);i.getDegree()>=o.getDegree()&&!i.isZero();){var d=i.getDegree()-o.getDegree(),p=this.field.multiply(i.getCoefficient(i.getDegree()),l);f=f.add(this.field.buildMonomial(d,p)),i=i.subtract(o.multiplyByMonomial(d,p))}s=f.multiply(a).subtract(c).negative()}var g=s.getCoefficient(0);if(0===g)throw Z.getChecksumInstance();var y=this.field.inverse(g);return[s.multiply(y),i.multiply(y)]},t.prototype.findErrorLocations=function(t){for(var e=t.getDegree(),r=new Int32Array(e),n=0,o=1;o<this.field.getSize()&&n<e;o++)0===t.evaluateAt(o)&&(r[n]=this.field.inverse(o),n++);if(n!==e)throw Z.getChecksumInstance();return r},t.prototype.findErrorMagnitudes=function(t,e,r){for(var n=e.getDegree(),o=new Int32Array(n),i=1;i<=n;i++)o[n-i]=this.field.multiply(i,e.getCoefficient(i));var a=new qn(this.field,o),s=r.length,u=new Int32Array(s);for(i=0;i<s;i++){var c=this.field.inverse(r[i]),f=this.field.subtract(0,t.evaluateAt(c)),h=this.field.inverse(a.evaluateAt(c));u[i]=this.field.multiply(f,h)}return u},t}(),ro=function(){function t(e,r,n,o,i){e instanceof t?this.constructor_2(e):this.constructor_1(e,r,n,o,i)}return t.prototype.constructor_1=function(t,e,r,n,o){var i=null==e||null==r,a=null==n||null==o;if(i&&a)throw new vt;i?(e=new $t(0,n.getY()),r=new $t(0,o.getY())):a&&(n=new $t(t.getWidth()-1,e.getY()),o=new $t(t.getWidth()-1,r.getY())),this.image=t,this.topLeft=e,this.bottomLeft=r,this.topRight=n,this.bottomRight=o,this.minX=Math.trunc(Math.min(e.getX(),r.getX())),this.maxX=Math.trunc(Math.max(n.getX(),o.getX())),this.minY=Math.trunc(Math.min(e.getY(),n.getY())),this.maxY=Math.trunc(Math.max(r.getY(),o.getY()))},t.prototype.constructor_2=function(t){this.image=t.image,this.topLeft=t.getTopLeft(),this.bottomLeft=t.getBottomLeft(),this.topRight=t.getTopRight(),this.bottomRight=t.getBottomRight(),this.minX=t.getMinX(),this.maxX=t.getMaxX(),this.minY=t.getMinY(),this.maxY=t.getMaxY()},t.merge=function(e,r){return null==e?r:null==r?e:new t(e.image,e.topLeft,e.bottomLeft,r.topRight,r.bottomRight)},t.prototype.addMissingRows=function(e,r,n){var o=this.topLeft,i=this.bottomLeft,a=this.topRight,s=this.bottomRight;if(e>0){var u=n?this.topLeft:this.topRight,c=Math.trunc(u.getY()-e);c<0&&(c=0);var f=new $t(u.getX(),c);n?o=f:a=f}if(r>0){var h=n?this.bottomLeft:this.bottomRight,l=Math.trunc(h.getY()+r);l>=this.image.getHeight()&&(l=this.image.getHeight()-1);var d=new $t(h.getX(),l);n?i=d:s=d}return new t(this.image,o,i,a,s)},t.prototype.getMinX=function(){return this.minX},t.prototype.getMaxX=function(){return this.maxX},t.prototype.getMinY=function(){return this.minY},t.prototype.getMaxY=function(){return this.maxY},t.prototype.getTopLeft=function(){return this.topLeft},t.prototype.getTopRight=function(){return this.topRight},t.prototype.getBottomLeft=function(){return this.bottomLeft},t.prototype.getBottomRight=function(){return this.bottomRight},t}(),no=function(){function t(t,e,r,n){this.columnCount=t,this.errorCorrectionLevel=n,this.rowCountUpperPart=e,this.rowCountLowerPart=r,this.rowCount=e+r}return t.prototype.getColumnCount=function(){return this.columnCount},t.prototype.getErrorCorrectionLevel=function(){return this.errorCorrectionLevel},t.prototype.getRowCount=function(){return this.rowCount},t.prototype.getRowCountUpperPart=function(){return this.rowCountUpperPart},t.prototype.getRowCountLowerPart=function(){return this.rowCountLowerPart},t}(),oo=function(){function t(){this.buffer=""}return t.form=function(t,e){var r=-1;return t.replace(/%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g,function(t,n,o,i,a,s){if("%%"===t)return"%";if(void 0!==e[++r]){t=i?parseInt(i.substr(1)):void 0;var u,c=a?parseInt(a.substr(1)):void 0;switch(s){case"s":u=e[r];break;case"c":u=e[r][0];break;case"f":u=parseFloat(e[r]).toFixed(t);break;case"p":u=parseFloat(e[r]).toPrecision(t);break;case"e":u=parseFloat(e[r]).toExponential(t);break;case"x":u=parseInt(e[r]).toString(c||16);break;case"d":u=parseFloat(parseInt(e[r],c||10).toPrecision(t)).toFixed(0)}u="object"==typeof u?JSON.stringify(u):(+u).toString(c);for(var f=parseInt(o),h=o&&o[0]+""=="0"?"0":" ";u.length<f;)u=void 0!==n?u+h:h+u;return u}})},t.prototype.format=function(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];this.buffer+=t.form(e,r)},t.prototype.toString=function(){return this.buffer},t}(),io=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},ao=function(){function t(t){this.boundingBox=new ro(t),this.codewords=new Array(t.getMaxY()-t.getMinY()+1)}return t.prototype.getCodewordNearby=function(e){var r=this.getCodeword(e);if(null!=r)return r;for(var n=1;n<t.MAX_NEARBY_DISTANCE;n++){var o=this.imageRowToCodewordIndex(e)-n;if(o>=0&&null!=(r=this.codewords[o]))return r;if((o=this.imageRowToCodewordIndex(e)+n)<this.codewords.length&&null!=(r=this.codewords[o]))return r}return null},t.prototype.imageRowToCodewordIndex=function(t){return t-this.boundingBox.getMinY()},t.prototype.setCodeword=function(t,e){this.codewords[this.imageRowToCodewordIndex(t)]=e},t.prototype.getCodeword=function(t){return this.codewords[this.imageRowToCodewordIndex(t)]},t.prototype.getBoundingBox=function(){return this.boundingBox},t.prototype.getCodewords=function(){return this.codewords},t.prototype.toString=function(){var t,e,r=new oo,n=0;try{for(var o=io(this.codewords),i=o.next();!i.done;i=o.next()){var a=i.value;null!=a?r.format("%3d: %3d|%3d%n",n++,a.getRowNumber(),a.getValue()):r.format("%3d:    |   %n",n++)}}catch(s){t={error:s}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}return r.toString()},t.MAX_NEARBY_DISTANCE=5,t}(),so=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},uo=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(s){o={error:s}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},co=function(){function t(){this.values=new Map}return t.prototype.setValue=function(t){t=Math.trunc(t);var e=this.values.get(t);null==e&&(e=0),e++,this.values.set(t,e)},t.prototype.getValue=function(){var t,e,r=-1,n=new Array,o=function(t,e){var o=function(){return t},i=function(){return e};i()>r?(r=i(),(n=[]).push(o())):i()===r&&n.push(o())};try{for(var i=so(this.values.entries()),a=i.next();!a.done;a=i.next()){var s=uo(a.value,2);o(s[0],s[1])}}catch(u){t={error:u}}finally{try{a&&!a.done&&(e=i.return)&&e.call(i)}finally{if(t)throw t.error}}return jn.toIntArray(n)},t.prototype.getConfidence=function(t){return this.values.get(t)},t}(),fo=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),ho=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},lo=function(t){function e(e,r){var n=t.call(this,e)||this;return n._isLeft=r,n}return fo(e,t),e.prototype.setRowNumbers=function(){var t,e;try{for(var r=ho(this.getCodewords()),n=r.next();!n.done;n=r.next()){var o=n.value;null!=o&&o.setRowNumberAsRowIndicatorColumn()}}catch(i){t={error:i}}finally{try{n&&!n.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}},e.prototype.adjustCompleteIndicatorColumnRowNumbers=function(t){var e=this.getCodewords();this.setRowNumbers(),this.removeIncorrectCodewords(e,t);for(var r=this.getBoundingBox(),n=this._isLeft?r.getTopLeft():r.getTopRight(),o=this._isLeft?r.getBottomLeft():r.getBottomRight(),i=this.imageRowToCodewordIndex(Math.trunc(n.getY())),a=this.imageRowToCodewordIndex(Math.trunc(o.getY())),s=-1,u=1,c=0,f=i;f<a;f++)if(null!=e[f]){var h=e[f],l=h.getRowNumber()-s;if(0===l)c++;else if(1===l)u=Math.max(u,c),c=1,s=h.getRowNumber();else if(l<0||h.getRowNumber()>=t.getRowCount()||l>f)e[f]=null;else{for(var d=void 0,p=(d=u>2?(u-2)*l:l)>=f,g=1;g<=d&&!p;g++)p=null!=e[f-g];p?e[f]=null:(s=h.getRowNumber(),c=1)}}},e.prototype.getRowHeights=function(){var t,e,r=this.getBarcodeMetadata();if(null==r)return null;this.adjustIncompleteIndicatorColumnRowNumbers(r);var n=new Int32Array(r.getRowCount());try{for(var o=ho(this.getCodewords()),i=o.next();!i.done;i=o.next()){var a=i.value;if(null!=a){var s=a.getRowNumber();if(s>=n.length)continue;n[s]++}}}catch(u){t={error:u}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}return n},e.prototype.adjustIncompleteIndicatorColumnRowNumbers=function(t){for(var e=this.getBoundingBox(),r=this._isLeft?e.getTopLeft():e.getTopRight(),n=this._isLeft?e.getBottomLeft():e.getBottomRight(),o=this.imageRowToCodewordIndex(Math.trunc(r.getY())),i=this.imageRowToCodewordIndex(Math.trunc(n.getY())),a=this.getCodewords(),s=-1,u=o;u<i;u++)if(null!=a[u]){var c=a[u];c.setRowNumberAsRowIndicatorColumn();var f=c.getRowNumber()-s;0===f||(1===f?s=c.getRowNumber():c.getRowNumber()>=t.getRowCount()?a[u]=null:s=c.getRowNumber())}},e.prototype.getBarcodeMetadata=function(){var t,e,r=this.getCodewords(),n=new co,o=new co,i=new co,a=new co;try{for(var s=ho(r),u=s.next();!u.done;u=s.next()){var c=u.value;if(null!=c){c.setRowNumberAsRowIndicatorColumn();var f=c.getValue()%30,h=c.getRowNumber();switch(this._isLeft||(h+=2),h%3){case 0:o.setValue(3*f+1);break;case 1:a.setValue(f/3),i.setValue(f%3);break;case 2:n.setValue(f+1)}}}}catch(d){t={error:d}}finally{try{u&&!u.done&&(e=s.return)&&e.call(s)}finally{if(t)throw t.error}}if(0===n.getValue().length||0===o.getValue().length||0===i.getValue().length||0===a.getValue().length||n.getValue()[0]<1||o.getValue()[0]+i.getValue()[0]<jn.MIN_ROWS_IN_BARCODE||o.getValue()[0]+i.getValue()[0]>jn.MAX_ROWS_IN_BARCODE)return null;var l=new no(n.getValue()[0],o.getValue()[0],i.getValue()[0],a.getValue()[0]);return this.removeIncorrectCodewords(r,l),l},e.prototype.removeIncorrectCodewords=function(t,e){for(var r=0;r<t.length;r++){var n=t[r];if(null!=t[r]){var o=n.getValue()%30,i=n.getRowNumber();if(i>e.getRowCount())t[r]=null;else switch(this._isLeft||(i+=2),i%3){case 0:3*o+1!==e.getRowCountUpperPart()&&(t[r]=null);break;case 1:Math.trunc(o/3)===e.getErrorCorrectionLevel()&&o%3===e.getRowCountLowerPart()||(t[r]=null);break;case 2:o+1!==e.getColumnCount()&&(t[r]=null)}}}},e.prototype.isLeft=function(){return this._isLeft},e.prototype.toString=function(){return"IsLeft: "+this._isLeft+"\n"+t.prototype.toString.call(this)},e}(ao),po=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},go=function(){function t(t,e){this.ADJUST_ROW_NUMBER_SKIP=2,this.barcodeMetadata=t,this.barcodeColumnCount=t.getColumnCount(),this.boundingBox=e,this.detectionResultColumns=new Array(this.barcodeColumnCount+2)}return t.prototype.getDetectionResultColumns=function(){this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[0]),this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[this.barcodeColumnCount+1]);var t,e=jn.MAX_CODEWORDS_IN_BARCODE;do{t=e,e=this.adjustRowNumbersAndGetCount()}while(e>0&&e<t);return this.detectionResultColumns},t.prototype.adjustIndicatorColumnRowNumbers=function(t){null!=t&&t.adjustCompleteIndicatorColumnRowNumbers(this.barcodeMetadata)},t.prototype.adjustRowNumbersAndGetCount=function(){var t=this.adjustRowNumbersByRow();if(0===t)return 0;for(var e=1;e<this.barcodeColumnCount+1;e++)for(var r=this.detectionResultColumns[e].getCodewords(),n=0;n<r.length;n++)null!=r[n]&&(r[n].hasValidRowNumber()||this.adjustRowNumbers(e,n,r));return t},t.prototype.adjustRowNumbersByRow=function(){return this.adjustRowNumbersFromBothRI(),this.adjustRowNumbersFromLRI()+this.adjustRowNumbersFromRRI()},t.prototype.adjustRowNumbersFromBothRI=function(){if(null!=this.detectionResultColumns[0]&&null!=this.detectionResultColumns[this.barcodeColumnCount+1])for(var t=this.detectionResultColumns[0].getCodewords(),e=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords(),r=0;r<t.length;r++)if(null!=t[r]&&null!=e[r]&&t[r].getRowNumber()===e[r].getRowNumber())for(var n=1;n<=this.barcodeColumnCount;n++){var o=this.detectionResultColumns[n].getCodewords()[r];null!=o&&(o.setRowNumber(t[r].getRowNumber()),o.hasValidRowNumber()||(this.detectionResultColumns[n].getCodewords()[r]=null))}},t.prototype.adjustRowNumbersFromRRI=function(){if(null==this.detectionResultColumns[this.barcodeColumnCount+1])return 0;for(var e=0,r=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords(),n=0;n<r.length;n++)if(null!=r[n])for(var o=r[n].getRowNumber(),i=0,a=this.barcodeColumnCount+1;a>0&&i<this.ADJUST_ROW_NUMBER_SKIP;a--){var s=this.detectionResultColumns[a].getCodewords()[n];null!=s&&(i=t.adjustRowNumberIfValid(o,i,s),s.hasValidRowNumber()||e++)}return e},t.prototype.adjustRowNumbersFromLRI=function(){if(null==this.detectionResultColumns[0])return 0;for(var e=0,r=this.detectionResultColumns[0].getCodewords(),n=0;n<r.length;n++)if(null!=r[n])for(var o=r[n].getRowNumber(),i=0,a=1;a<this.barcodeColumnCount+1&&i<this.ADJUST_ROW_NUMBER_SKIP;a++){var s=this.detectionResultColumns[a].getCodewords()[n];null!=s&&(i=t.adjustRowNumberIfValid(o,i,s),s.hasValidRowNumber()||e++)}return e},t.adjustRowNumberIfValid=function(t,e,r){return null==r||r.hasValidRowNumber()||(r.isValidRowNumber(t)?(r.setRowNumber(t),e=0):++e),e},t.prototype.adjustRowNumbers=function(e,r,n){var o,i;if(null!=this.detectionResultColumns[e-1]){var a=n[r],s=this.detectionResultColumns[e-1].getCodewords(),u=s;null!=this.detectionResultColumns[e+1]&&(u=this.detectionResultColumns[e+1].getCodewords());var c=new Array(14);c[2]=s[r],c[3]=u[r],r>0&&(c[0]=n[r-1],c[4]=s[r-1],c[5]=u[r-1]),r>1&&(c[8]=n[r-2],c[10]=s[r-2],c[11]=u[r-2]),r<n.length-1&&(c[1]=n[r+1],c[6]=s[r+1],c[7]=u[r+1]),r<n.length-2&&(c[9]=n[r+2],c[12]=s[r+2],c[13]=u[r+2]);try{for(var f=po(c),h=f.next();!h.done;h=f.next()){var l=h.value;if(t.adjustRowNumber(a,l))return}}catch(d){o={error:d}}finally{try{h&&!h.done&&(i=f.return)&&i.call(f)}finally{if(o)throw o.error}}}},t.adjustRowNumber=function(t,e){return!(null==e||!e.hasValidRowNumber()||e.getBucket()!==t.getBucket()||(t.setRowNumber(e.getRowNumber()),0))},t.prototype.getBarcodeColumnCount=function(){return this.barcodeColumnCount},t.prototype.getBarcodeRowCount=function(){return this.barcodeMetadata.getRowCount()},t.prototype.getBarcodeECLevel=function(){return this.barcodeMetadata.getErrorCorrectionLevel()},t.prototype.setBoundingBox=function(t){this.boundingBox=t},t.prototype.getBoundingBox=function(){return this.boundingBox},t.prototype.setDetectionResultColumn=function(t,e){this.detectionResultColumns[t]=e},t.prototype.getDetectionResultColumn=function(t){return this.detectionResultColumns[t]},t.prototype.toString=function(){var t=this.detectionResultColumns[0];null==t&&(t=this.detectionResultColumns[this.barcodeColumnCount+1]);for(var e=new oo,r=0;r<t.getCodewords().length;r++){e.format("CW %3d:",r);for(var n=0;n<this.barcodeColumnCount+2;n++)if(null!=this.detectionResultColumns[n]){var o=this.detectionResultColumns[n].getCodewords()[r];null!=o?e.format(" %3d|%3d",o.getRowNumber(),o.getValue()):e.format("    |   ")}else e.format("    |   ");e.format("%n")}return e.toString()},t}(),yo=function(){function t(e,r,n,o){this.rowNumber=t.BARCODE_ROW_UNKNOWN,this.startX=Math.trunc(e),this.endX=Math.trunc(r),this.bucket=Math.trunc(n),this.value=Math.trunc(o)}return t.prototype.hasValidRowNumber=function(){return this.isValidRowNumber(this.rowNumber)},t.prototype.isValidRowNumber=function(e){return e!==t.BARCODE_ROW_UNKNOWN&&this.bucket===e%3*3},t.prototype.setRowNumberAsRowIndicatorColumn=function(){this.rowNumber=Math.trunc(3*Math.trunc(this.value/30)+Math.trunc(this.bucket/3))},t.prototype.getWidth=function(){return this.endX-this.startX},t.prototype.getStartX=function(){return this.startX},t.prototype.getEndX=function(){return this.endX},t.prototype.getBucket=function(){return this.bucket},t.prototype.getValue=function(){return this.value},t.prototype.getRowNumber=function(){return this.rowNumber},t.prototype.setRowNumber=function(t){this.rowNumber=t},t.prototype.toString=function(){return this.rowNumber+"|"+this.value},t.BARCODE_ROW_UNKNOWN=-1,t}(),wo=function(){function t(){}return t.initialize=function(){for(var e=0;e<jn.SYMBOL_TABLE.length;e++)for(var r=jn.SYMBOL_TABLE[e],n=1&r,o=0;o<jn.BARS_IN_MODULE;o++){for(var i=0;(1&r)===n;)i+=1,r>>=1;n=1&r,t.RATIOS_TABLE[e]||(t.RATIOS_TABLE[e]=new Array(jn.BARS_IN_MODULE)),t.RATIOS_TABLE[e][jn.BARS_IN_MODULE-o-1]=Math.fround(i/jn.MODULES_IN_CODEWORD)}this.bSymbolTableReady=!0},t.getDecodedValue=function(e){var r=t.getDecodedCodewordValue(t.sampleBitCounts(e));return-1!==r?r:t.getClosestDecodedValue(e)},t.sampleBitCounts=function(t){for(var e=Qt.sum(t),r=new Int32Array(jn.BARS_IN_MODULE),n=0,o=0,i=0;i<jn.MODULES_IN_CODEWORD;i++){var a=e/(2*jn.MODULES_IN_CODEWORD)+i*e/jn.MODULES_IN_CODEWORD;o+t[n]<=a&&(o+=t[n],n++),r[n]++}return r},t.getDecodedCodewordValue=function(e){var r=t.getBitValue(e);return-1===jn.getCodeword(r)?-1:r},t.getBitValue=function(t){for(var e=0,r=0;r<t.length;r++)for(var n=0;n<t[r];n++)e=e<<1|(r%2==0?1:0);return Math.trunc(e)},t.getClosestDecodedValue=function(e){var r=Qt.sum(e),n=new Array(jn.BARS_IN_MODULE);if(r>1)for(var o=0;o<n.length;o++)n[o]=Math.fround(e[o]/r);var i=Jt.MAX_VALUE,a=-1;this.bSymbolTableReady||t.initialize();for(var s=0;s<t.RATIOS_TABLE.length;s++){for(var u=0,c=t.RATIOS_TABLE[s],f=0;f<jn.BARS_IN_MODULE;f++){var h=Math.fround(c[f]-n[f]);if((u+=Math.fround(h*h))>=i)break}u<i&&(i=u,a=jn.SYMBOL_TABLE[s])}return a},t.bSymbolTableReady=!1,t.RATIOS_TABLE=new Array(jn.SYMBOL_TABLE.length).map(function(t){return new Array(jn.BARS_IN_MODULE)}),t}(),vo=function(){function t(){this.segmentCount=-1,this.fileSize=-1,this.timestamp=-1,this.checksum=-1}return t.prototype.getSegmentIndex=function(){return this.segmentIndex},t.prototype.setSegmentIndex=function(t){this.segmentIndex=t},t.prototype.getFileId=function(){return this.fileId},t.prototype.setFileId=function(t){this.fileId=t},t.prototype.getOptionalData=function(){return this.optionalData},t.prototype.setOptionalData=function(t){this.optionalData=t},t.prototype.isLastSegment=function(){return this.lastSegment},t.prototype.setLastSegment=function(t){this.lastSegment=t},t.prototype.getSegmentCount=function(){return this.segmentCount},t.prototype.setSegmentCount=function(t){this.segmentCount=t},t.prototype.getSender=function(){return this.sender||null},t.prototype.setSender=function(t){this.sender=t},t.prototype.getAddressee=function(){return this.addressee||null},t.prototype.setAddressee=function(t){this.addressee=t},t.prototype.getFileName=function(){return this.fileName},t.prototype.setFileName=function(t){this.fileName=t},t.prototype.getFileSize=function(){return this.fileSize},t.prototype.setFileSize=function(t){this.fileSize=t},t.prototype.getChecksum=function(){return this.checksum},t.prototype.setChecksum=function(t){this.checksum=t},t.prototype.getTimestamp=function(){return this.timestamp},t.prototype.setTimestamp=function(t){this.timestamp=t},t}(),_o=function(){function t(){}return t.parseLong=function(t,e){return void 0===e&&(e=void 0),parseInt(t,e)},t}(),mo=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Co=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return mo(e,t),e.kind="NullPointerException",e}(H),Ao=function(){function t(){}return t.prototype.writeBytes=function(t){this.writeBytesOffset(t,0,t.length)},t.prototype.writeBytesOffset=function(t,e,r){if(null==t)throw new Co;if(e<0||e>t.length||r<0||e+r>t.length||e+r<0)throw new J;if(0!==r)for(var n=0;n<r;n++)this.write(t[e+n])},t.prototype.flush=function(){},t.prototype.close=function(){},t}(),Eo=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Io=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return Eo(e,t),e}(H),So=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),bo=function(t){function e(e){void 0===e&&(e=32);var r=t.call(this)||this;if(r.count=0,e<0)throw new j("Negative initial size: "+e);return r.buf=new Uint8Array(e),r}return So(e,t),e.prototype.ensureCapacity=function(t){t-this.buf.length>0&&this.grow(t)},e.prototype.grow=function(t){var e=this.buf.length<<1;if(e-t<0&&(e=t),e<0){if(t<0)throw new Io;e=nt.MAX_VALUE}this.buf=rt.copyOfUint8Array(this.buf,e)},e.prototype.write=function(t){this.ensureCapacity(this.count+1),this.buf[this.count]=t,this.count+=1},e.prototype.writeBytesOffset=function(t,e,r){if(e<0||e>t.length||r<0||e+r-t.length>0)throw new J;this.ensureCapacity(this.count+r),q.arraycopy(t,e,this.buf,this.count,r),this.count+=r},e.prototype.writeTo=function(t){t.writeBytesOffset(this.buf,0,this.count)},e.prototype.reset=function(){this.count=0},e.prototype.toByteArray=function(){return rt.copyOfUint8Array(this.buf,this.count)},e.prototype.size=function(){return this.count},e.prototype.toString=function(t){return t?"string"==typeof t?this.toString_string(t):this.toString_number(t):this.toString_void()},e.prototype.toString_void=function(){return new String(this.buf).toString()},e.prototype.toString_string=function(t){return new String(this.buf).toString()},e.prototype.toString_number=function(t){return new String(this.buf).toString()},e.prototype.close=function(){},e}(Ao);function To(){if("undefined"!=typeof window)return window.BigInt||null;if("undefined"!=typeof global)return global.BigInt||null;if("undefined"!=typeof self)return self.BigInt||null;throw new Error("Can't search globals for BigInt!")}function Oo(t){if(void 0===Tn&&(Tn=To()),null===Tn)throw new Error("BigInt is not supported!");return Tn(t)}!function(t){t[t.ALPHA=0]="ALPHA",t[t.LOWER=1]="LOWER",t[t.MIXED=2]="MIXED",t[t.PUNCT=3]="PUNCT",t[t.ALPHA_SHIFT=4]="ALPHA_SHIFT",t[t.PUNCT_SHIFT=5]="PUNCT_SHIFT"}(bn||(bn={}));var Ro=function(){function t(){}return t.decode=function(e,r){var n=new gt(""),o=ft.ISO8859_1;n.enableDecoding(o);for(var i=1,a=e[i++],s=new vo;i<e[0];){switch(a){case t.TEXT_COMPACTION_MODE_LATCH:i=t.textCompaction(e,i,n);break;case t.BYTE_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:i=t.byteCompaction(a,e,o,i,n);break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:n.append(e[i++]);break;case t.NUMERIC_COMPACTION_MODE_LATCH:i=t.numericCompaction(e,i,n);break;case t.ECI_CHARSET:ft.getCharacterSetECIByValue(e[i++]);break;case t.ECI_GENERAL_PURPOSE:i+=2;break;case t.ECI_USER_DEFINED:i++;break;case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:i=t.decodeMacroBlock(e,i,s);break;case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:throw new st;default:i--,i=t.textCompaction(e,i,n)}if(!(i<e.length))throw st.getFormatInstance();a=e[i++]}if(0===n.length())throw st.getFormatInstance();var u=new xt(null,n.toString(),null,r);return u.setOther(s),u},t.decodeMacroBlock=function(e,r,n){if(r+t.NUMBER_OF_SEQUENCE_CODEWORDS>e[0])throw st.getFormatInstance();for(var o=new Int32Array(t.NUMBER_OF_SEQUENCE_CODEWORDS),i=0;i<t.NUMBER_OF_SEQUENCE_CODEWORDS;i++,r++)o[i]=e[r];n.setSegmentIndex(nt.parseInt(t.decodeBase900toBase10(o,t.NUMBER_OF_SEQUENCE_CODEWORDS)));var a=new gt;r=t.textCompaction(e,r,a),n.setFileId(a.toString());var s=-1;for(e[r]===t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD&&(s=r+1);r<e[0];)switch(e[r]){case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:switch(e[++r]){case t.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME:var u=new gt;r=t.textCompaction(e,r+1,u),n.setFileName(u.toString());break;case t.MACRO_PDF417_OPTIONAL_FIELD_SENDER:var c=new gt;r=t.textCompaction(e,r+1,c),n.setSender(c.toString());break;case t.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE:var f=new gt;r=t.textCompaction(e,r+1,f),n.setAddressee(f.toString());break;case t.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT:var h=new gt;r=t.numericCompaction(e,r+1,h),n.setSegmentCount(nt.parseInt(h.toString()));break;case t.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP:var l=new gt;r=t.numericCompaction(e,r+1,l),n.setTimestamp(_o.parseLong(l.toString()));break;case t.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM:var d=new gt;r=t.numericCompaction(e,r+1,d),n.setChecksum(nt.parseInt(d.toString()));break;case t.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE:var p=new gt;r=t.numericCompaction(e,r+1,p),n.setFileSize(_o.parseLong(p.toString()));break;default:throw st.getFormatInstance()}break;case t.MACRO_PDF417_TERMINATOR:r++,n.setLastSegment(!0);break;default:throw st.getFormatInstance()}if(-1!==s){var g=r-s;n.isLastSegment()&&g--,n.setOptionalData(rt.copyOfRange(e,s,s+g))}return r},t.textCompaction=function(e,r,n){for(var o=new Int32Array(2*(e[0]-r)),i=new Int32Array(2*(e[0]-r)),a=0,s=!1;r<e[0]&&!s;){var u=e[r++];if(u<t.TEXT_COMPACTION_MODE_LATCH)o[a]=u/30,o[a+1]=u%30,a+=2;else switch(u){case t.TEXT_COMPACTION_MODE_LATCH:o[a++]=t.TEXT_COMPACTION_MODE_LATCH;break;case t.BYTE_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:case t.NUMERIC_COMPACTION_MODE_LATCH:case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:r--,s=!0;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:o[a]=t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE,u=e[r++],i[a]=u,a++}}return t.decodeTextCompaction(o,i,a,n),r},t.decodeTextCompaction=function(e,r,n,o){for(var i=bn.ALPHA,a=bn.ALPHA,s=0;s<n;){var u=e[s],c="";switch(i){case bn.ALPHA:if(u<26)c=String.fromCharCode(65+u);else switch(u){case 26:c=" ";break;case t.LL:i=bn.LOWER;break;case t.ML:i=bn.MIXED;break;case t.PS:a=i,i=bn.PUNCT_SHIFT;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:o.append(r[s]);break;case t.TEXT_COMPACTION_MODE_LATCH:i=bn.ALPHA}break;case bn.LOWER:if(u<26)c=String.fromCharCode(97+u);else switch(u){case 26:c=" ";break;case t.AS:a=i,i=bn.ALPHA_SHIFT;break;case t.ML:i=bn.MIXED;break;case t.PS:a=i,i=bn.PUNCT_SHIFT;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:o.append(r[s]);break;case t.TEXT_COMPACTION_MODE_LATCH:i=bn.ALPHA}break;case bn.MIXED:if(u<t.PL)c=t.MIXED_CHARS[u];else switch(u){case t.PL:i=bn.PUNCT;break;case 26:c=" ";break;case t.LL:i=bn.LOWER;break;case t.AL:i=bn.ALPHA;break;case t.PS:a=i,i=bn.PUNCT_SHIFT;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:o.append(r[s]);break;case t.TEXT_COMPACTION_MODE_LATCH:i=bn.ALPHA}break;case bn.PUNCT:if(u<t.PAL)c=t.PUNCT_CHARS[u];else switch(u){case t.PAL:i=bn.ALPHA;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:o.append(r[s]);break;case t.TEXT_COMPACTION_MODE_LATCH:i=bn.ALPHA}break;case bn.ALPHA_SHIFT:if(i=a,u<26)c=String.fromCharCode(65+u);else switch(u){case 26:c=" ";break;case t.TEXT_COMPACTION_MODE_LATCH:i=bn.ALPHA}break;case bn.PUNCT_SHIFT:if(i=a,u<t.PAL)c=t.PUNCT_CHARS[u];else switch(u){case t.PAL:i=bn.ALPHA;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:o.append(r[s]);break;case t.TEXT_COMPACTION_MODE_LATCH:i=bn.ALPHA}}""!==c&&o.append(c),s++}},t.byteCompaction=function(e,r,n,o,i){var a=new bo,s=0,u=0,c=!1;switch(e){case t.BYTE_COMPACTION_MODE_LATCH:for(var f=new Int32Array(6),h=r[o++];o<r[0]&&!c;)switch(f[s++]=h,u=900*u+h,h=r[o++]){case t.TEXT_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH:case t.NUMERIC_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:o--,c=!0;break;default:if(s%5==0&&s>0){for(var l=0;l<6;++l)a.write(Number(Oo(u)>>Oo(8*(5-l))));u=0,s=0}}o===r[0]&&h<t.TEXT_COMPACTION_MODE_LATCH&&(f[s++]=h);for(var d=0;d<s;d++)a.write(f[d]);break;case t.BYTE_COMPACTION_MODE_LATCH_6:for(;o<r[0]&&!c;){var p=r[o++];if(p<t.TEXT_COMPACTION_MODE_LATCH)s++,u=900*u+p;else switch(p){case t.TEXT_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH:case t.NUMERIC_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:o--,c=!0}if(s%5==0&&s>0){for(l=0;l<6;++l)a.write(Number(Oo(u)>>Oo(8*(5-l))));u=0,s=0}}}return i.append(dt.decode(a.toByteArray(),n)),o},t.numericCompaction=function(e,r,n){for(var o=0,i=!1,a=new Int32Array(t.MAX_NUMERIC_CODEWORDS);r<e[0]&&!i;){var s=e[r++];if(r===e[0]&&(i=!0),s<t.TEXT_COMPACTION_MODE_LATCH)a[o]=s,o++;else switch(s){case t.TEXT_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:r--,i=!0}(o%t.MAX_NUMERIC_CODEWORDS===0||s===t.NUMERIC_COMPACTION_MODE_LATCH||i)&&o>0&&(n.append(t.decodeBase900toBase10(a,o)),o=0)}return r},t.decodeBase900toBase10=function(e,r){for(var n=Oo(0),o=0;o<r;o++)n+=t.EXP900[r-o-1]*Oo(e[o]);var i=n.toString();if("1"!==i.charAt(0))throw new st;return i.substring(1)},t.TEXT_COMPACTION_MODE_LATCH=900,t.BYTE_COMPACTION_MODE_LATCH=901,t.NUMERIC_COMPACTION_MODE_LATCH=902,t.BYTE_COMPACTION_MODE_LATCH_6=924,t.ECI_USER_DEFINED=925,t.ECI_GENERAL_PURPOSE=926,t.ECI_CHARSET=927,t.BEGIN_MACRO_PDF417_CONTROL_BLOCK=928,t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD=923,t.MACRO_PDF417_TERMINATOR=922,t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE=913,t.MAX_NUMERIC_CODEWORDS=15,t.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME=0,t.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT=1,t.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP=2,t.MACRO_PDF417_OPTIONAL_FIELD_SENDER=3,t.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE=4,t.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE=5,t.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM=6,t.PL=25,t.LL=27,t.AS=27,t.ML=28,t.AL=28,t.PS=29,t.PAL=29,t.PUNCT_CHARS=";<>@[\\]_`~!\r\t,:\n-.$/\"|*()?{}'",t.MIXED_CHARS="0123456789&\r\t,:#-.$/+%*=^",t.EXP900=To()?function(){var t=[];t[0]=Oo(1);var e=Oo(900);t[1]=e;for(var r=2;r<16;r++)t[r]=t[r-1]*e;return t}():[],t.NUMBER_OF_SEQUENCE_CODEWORDS=2,t}(),No=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Do=function(){function t(){}return t.decode=function(e,r,n,o,i,a,s){for(var u,c=new ro(e,r,n,o,i),f=null,h=null,l=!0;;l=!1){if(null!=r&&(f=t.getRowIndicatorColumn(e,c,r,!0,a,s)),null!=o&&(h=t.getRowIndicatorColumn(e,c,o,!1,a,s)),null==(u=t.merge(f,h)))throw vt.getNotFoundInstance();var d=u.getBoundingBox();if(!l||null==d||!(d.getMinY()<c.getMinY()||d.getMaxY()>c.getMaxY()))break;c=d}u.setBoundingBox(c);var p=u.getBarcodeColumnCount()+1;u.setDetectionResultColumn(0,f),u.setDetectionResultColumn(p,h);for(var g=null!=f,y=1;y<=p;y++){var w=g?y:p-y;if(void 0===u.getDetectionResultColumn(w)){var v=void 0;v=0===w||w===p?new lo(c,0===w):new ao(c),u.setDetectionResultColumn(w,v);for(var _=-1,m=_,C=c.getMinY();C<=c.getMaxY();C++){if((_=t.getStartColumn(u,w,C,g))<0||_>c.getMaxX()){if(-1===m)continue;_=m}var A=t.detectCodeword(e,c.getMinX(),c.getMaxX(),g,_,C,a,s);null!=A&&(v.setCodeword(C,A),m=_,a=Math.min(a,A.getWidth()),s=Math.max(s,A.getWidth()))}}}return t.createDecoderResult(u)},t.merge=function(e,r){if(null==e&&null==r)return null;var n=t.getBarcodeMetadata(e,r);if(null==n)return null;var o=ro.merge(t.adjustBoundingBox(e),t.adjustBoundingBox(r));return new go(n,o)},t.adjustBoundingBox=function(e){var r,n;if(null==e)return null;var o=e.getRowHeights();if(null==o)return null;var i=t.getMax(o),a=0;try{for(var s=No(o),u=s.next();!u.done;u=s.next()){var c=u.value;if(a+=i-c,c>0)break}}catch(d){r={error:d}}finally{try{u&&!u.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}for(var f=e.getCodewords(),h=0;a>0&&null==f[h];h++)a--;var l=0;for(h=o.length-1;h>=0&&(l+=i-o[h],!(o[h]>0));h--);for(h=f.length-1;l>0&&null==f[h];h--)l--;return e.getBoundingBox().addMissingRows(a,l,e.isLeft())},t.getMax=function(t){var e,r,n=-1;try{for(var o=No(t),i=o.next();!i.done;i=o.next()){var a=i.value;n=Math.max(n,a)}}catch(s){e={error:s}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n},t.getBarcodeMetadata=function(t,e){var r,n;return null==t||null==(r=t.getBarcodeMetadata())?null==e?null:e.getBarcodeMetadata():null==e||null==(n=e.getBarcodeMetadata())?r:r.getColumnCount()!==n.getColumnCount()&&r.getErrorCorrectionLevel()!==n.getErrorCorrectionLevel()&&r.getRowCount()!==n.getRowCount()?null:r},t.getRowIndicatorColumn=function(e,r,n,o,i,a){for(var s=new lo(r,o),u=0;u<2;u++)for(var c=0===u?1:-1,f=Math.trunc(Math.trunc(n.getX())),h=Math.trunc(Math.trunc(n.getY()));h<=r.getMaxY()&&h>=r.getMinY();h+=c){var l=t.detectCodeword(e,0,e.getWidth(),o,f,h,i,a);null!=l&&(s.setCodeword(h,l),f=o?l.getStartX():l.getEndX())}return s},t.adjustCodewordCount=function(e,r){var n=r[0][1],o=n.getValue(),i=e.getBarcodeColumnCount()*e.getBarcodeRowCount()-t.getNumberOfECCodeWords(e.getBarcodeECLevel());if(0===o.length){if(i<1||i>jn.MAX_CODEWORDS_IN_BARCODE)throw vt.getNotFoundInstance();n.setValue(i)}else o[0]!==i&&n.setValue(i)},t.createDecoderResult=function(e){var r=t.createBarcodeMatrix(e);t.adjustCodewordCount(e,r);for(var n=new Array,o=new Int32Array(e.getBarcodeRowCount()*e.getBarcodeColumnCount()),i=[],a=new Array,s=0;s<e.getBarcodeRowCount();s++)for(var u=0;u<e.getBarcodeColumnCount();u++){var c=r[s][u+1].getValue(),f=s*e.getBarcodeColumnCount()+u;0===c.length?n.push(f):1===c.length?o[f]=c[0]:(a.push(f),i.push(c))}for(var h=new Array(i.length),l=0;l<h.length;l++)h[l]=i[l];return t.createDecoderResultFromAmbiguousValues(e.getBarcodeECLevel(),o,jn.toIntArray(n),jn.toIntArray(a),h)},t.createDecoderResultFromAmbiguousValues=function(e,r,n,o,i){for(var a=new Int32Array(o.length),s=100;s-- >0;){for(var u=0;u<a.length;u++)r[o[u]]=i[u][a[u]];try{return t.decodeCodewords(r,e,n)}catch(c){if(!(c instanceof Z))throw c}if(0===a.length)throw Z.getChecksumInstance();for(u=0;u<a.length;u++){if(a[u]<i[u].length-1){a[u]++;break}if(a[u]=0,u===a.length-1)throw Z.getChecksumInstance()}}throw Z.getChecksumInstance()},t.createBarcodeMatrix=function(t){for(var e,r,n,o,i=Array.from({length:t.getBarcodeRowCount()},function(){return new Array(t.getBarcodeColumnCount()+2)}),a=0;a<i.length;a++)for(var s=0;s<i[a].length;s++)i[a][s]=new co;var u=0;try{for(var c=No(t.getDetectionResultColumns()),f=c.next();!f.done;f=c.next()){var h=f.value;if(null!=h)try{for(var l=(n=void 0,No(h.getCodewords())),d=l.next();!d.done;d=l.next()){var p=d.value;if(null!=p){var g=p.getRowNumber();if(g>=0){if(g>=i.length)continue;i[g][u].setValue(p.getValue())}}}}catch(y){n={error:y}}finally{try{d&&!d.done&&(o=l.return)&&o.call(l)}finally{if(n)throw n.error}}u++}}catch(w){e={error:w}}finally{try{f&&!f.done&&(r=c.return)&&r.call(c)}finally{if(e)throw e.error}}return i},t.isValidBarcodeColumn=function(t,e){return e>=0&&e<=t.getBarcodeColumnCount()+1},t.getStartColumn=function(e,r,n,o){var i,a,s=o?1:-1,u=null;if(t.isValidBarcodeColumn(e,r-s)&&(u=e.getDetectionResultColumn(r-s).getCodeword(n)),null!=u)return o?u.getEndX():u.getStartX();if(null!=(u=e.getDetectionResultColumn(r).getCodewordNearby(n)))return o?u.getStartX():u.getEndX();if(t.isValidBarcodeColumn(e,r-s)&&(u=e.getDetectionResultColumn(r-s).getCodewordNearby(n)),null!=u)return o?u.getEndX():u.getStartX();for(var c=0;t.isValidBarcodeColumn(e,r-s);){r-=s;try{for(var f=(i=void 0,No(e.getDetectionResultColumn(r).getCodewords())),h=f.next();!h.done;h=f.next()){var l=h.value;if(null!=l)return(o?l.getEndX():l.getStartX())+s*c*(l.getEndX()-l.getStartX())}}catch(d){i={error:d}}finally{try{h&&!h.done&&(a=f.return)&&a.call(f)}finally{if(i)throw i.error}}c++}return o?e.getBoundingBox().getMinX():e.getBoundingBox().getMaxX()},t.detectCodeword=function(e,r,n,o,i,a,s,u){i=t.adjustCodewordStartColumn(e,r,n,o,i,a);var c,f=t.getModuleBitCount(e,r,n,o,i,a);if(null==f)return null;var h=Qt.sum(f);if(o)c=i+h;else{for(var l=0;l<f.length/2;l++){var d=f[l];f[l]=f[f.length-1-l],f[f.length-1-l]=d}i=(c=i)-h}if(!t.checkCodewordSkew(h,s,u))return null;var p=wo.getDecodedValue(f),g=jn.getCodeword(p);return-1===g?null:new yo(i,c,t.getCodewordBucketNumber(p),g)},t.getModuleBitCount=function(t,e,r,n,o,i){for(var a=o,s=new Int32Array(8),u=0,c=n?1:-1,f=n;(n?a<r:a>=e)&&u<s.length;)t.get(a,i)===f?(s[u]++,a+=c):(u++,f=!f);return u===s.length||a===(n?r:e)&&u===s.length-1?s:null},t.getNumberOfECCodeWords=function(t){return 2<<t},t.adjustCodewordStartColumn=function(e,r,n,o,i,a){for(var s=i,u=o?-1:1,c=0;c<2;c++){for(;(o?s>=r:s<n)&&o===e.get(s,a);){if(Math.abs(i-s)>t.CODEWORD_SKEW_SIZE)return i;s+=u}u=-u,o=!o}return s},t.checkCodewordSkew=function(e,r,n){return r-t.CODEWORD_SKEW_SIZE<=e&&e<=n+t.CODEWORD_SKEW_SIZE},t.decodeCodewords=function(e,r,n){if(0===e.length)throw st.getFormatInstance();var o=1<<r+1,i=t.correctErrors(e,n,o);t.verifyCodewordCount(e,o);var a=Ro.decode(e,""+r);return a.setErrorsCorrected(i),a.setErasures(n.length),a},t.correctErrors=function(e,r,n){if(null!=r&&r.length>n/2+t.MAX_ERRORS||n<0||n>t.MAX_EC_CODEWORDS)throw Z.getChecksumInstance();return t.errorCorrection.decode(e,n,r)},t.verifyCodewordCount=function(t,e){if(t.length<4)throw st.getFormatInstance();var r=t[0];if(r>t.length)throw st.getFormatInstance();if(0===r){if(!(e<t.length))throw st.getFormatInstance();t[0]=t.length-e}},t.getBitCountForCodeword=function(t){for(var e=new Int32Array(8),r=0,n=e.length-1;!((1&t)!==r&&(r=1&t,--n<0));)e[n]++,t>>=1;return e},t.getCodewordBucketNumber=function(t){return t instanceof Int32Array?this.getCodewordBucketNumber_Int32Array(t):this.getCodewordBucketNumber_number(t)},t.getCodewordBucketNumber_number=function(e){return t.getCodewordBucketNumber(t.getBitCountForCodeword(e))},t.getCodewordBucketNumber_Int32Array=function(t){return(t[0]-t[2]+t[4]-t[6]+9)%9},t.toString=function(t){for(var e=new oo,r=0;r<t.length;r++){e.format("Row %2d: ",r);for(var n=0;n<t[r].length;n++){var o=t[r][n];0===o.getValue().length?e.format("        ",null):e.format("%4d(%2d)",o.getValue()[0],o.getConfidence(o.getValue()[0]))}e.format("%n")}return e.toString()},t.CODEWORD_SKEW_SIZE=2,t.MAX_ERRORS=3,t.MAX_EC_CODEWORDS=512,t.errorCorrection=new eo,t}(),Mo=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Po=function(){function t(){}return t.prototype.decode=function(e,r){void 0===r&&(r=null);var n=t.decode(e,r,!1);if(null==n||0===n.length||null==n[0])throw vt.getNotFoundInstance();return n[0]},t.prototype.decodeMultiple=function(e,r){void 0===r&&(r=null);try{return t.decode(e,r,!0)}catch(n){if(n instanceof st||n instanceof Z)throw vt.getNotFoundInstance();throw n}},t.decode=function(e,r,n){var o,i,a=new Array,s=Zn.detectMultiple(e,r,n);try{for(var u=Mo(s.getPoints()),c=u.next();!c.done;c=u.next()){var f=c.value,h=Do.decode(s.getBits(),f[4],f[5],f[6],f[7],t.getMinCodewordWidth(f),t.getMaxCodewordWidth(f)),l=new Pt(h.getText(),h.getRawBytes(),void 0,f,Bt.PDF_417);l.putMetadata(Ft.ERROR_CORRECTION_LEVEL,h.getECLevel());var d=h.getOther();null!=d&&l.putMetadata(Ft.PDF417_EXTRA_METADATA,d),a.push(l)}}catch(p){o={error:p}}finally{try{c&&!c.done&&(i=u.return)&&i.call(u)}finally{if(o)throw o.error}}return a.map(function(t){return t})},t.getMaxWidth=function(t,e){return null==t||null==e?0:Math.trunc(Math.abs(t.getX()-e.getX()))},t.getMinWidth=function(t,e){return null==t||null==e?nt.MAX_VALUE:Math.trunc(Math.abs(t.getX()-e.getX()))},t.getMaxCodewordWidth=function(e){return Math.floor(Math.max(Math.max(t.getMaxWidth(e[0],e[4]),t.getMaxWidth(e[6],e[2])*jn.MODULES_IN_CODEWORD/jn.MODULES_IN_STOP_PATTERN),Math.max(t.getMaxWidth(e[1],e[5]),t.getMaxWidth(e[7],e[3])*jn.MODULES_IN_CODEWORD/jn.MODULES_IN_STOP_PATTERN)))},t.getMinCodewordWidth=function(e){return Math.floor(Math.min(Math.min(t.getMinWidth(e[0],e[4]),t.getMinWidth(e[6],e[2])*jn.MODULES_IN_CODEWORD/jn.MODULES_IN_STOP_PATTERN),Math.min(t.getMinWidth(e[1],e[5]),t.getMinWidth(e[7],e[3])*jn.MODULES_IN_CODEWORD/jn.MODULES_IN_STOP_PATTERN)))},t.prototype.reset=function(){},t}(),Bo=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Lo=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return Bo(e,t),e.kind="ReaderException",e}(H),Fo=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},ko=function(){function t(){}return t.prototype.decode=function(t,e){return this.setHints(e),this.decodeInternal(t)},t.prototype.decodeWithState=function(t){return null!==this.readers&&void 0!==this.readers||this.setHints(null),this.decodeInternal(t)},t.prototype.setHints=function(t){this.hints=t;var e=null!=t&&void 0!==t.get(k.TRY_HARDER),r=null==t?null:t.get(k.POSSIBLE_FORMATS),n=new Array;if(null!=r){var o=r.some(function(t){return t===Bt.UPC_A||t===Bt.UPC_E||t===Bt.EAN_13||t===Bt.EAN_8||t===Bt.CODABAR||t===Bt.CODE_39||t===Bt.CODE_93||t===Bt.CODE_128||t===Bt.ITF||t===Bt.RSS_14||t===Bt.RSS_EXPANDED});o&&!e&&n.push(new Yr(t)),r.includes(Bt.QR_CODE)&&n.push(new Xn),r.includes(Bt.DATA_MATRIX)&&n.push(new fn),r.includes(Bt.AZTEC)&&n.push(new he),r.includes(Bt.PDF_417)&&n.push(new Po),o&&e&&n.push(new Yr(t))}0===n.length&&(e||n.push(new Yr(t)),n.push(new Xn),n.push(new fn),n.push(new he),n.push(new Po),e&&n.push(new Yr(t))),this.readers=n},t.prototype.reset=function(){var t,e;if(null!==this.readers)try{for(var r=Fo(this.readers),n=r.next();!n.done;n=r.next())n.value.reset()}catch(o){t={error:o}}finally{try{n&&!n.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}},t.prototype.decodeInternal=function(t){var e,r;if(null===this.readers)throw new Lo("No readers where selected, nothing can be read.");try{for(var n=Fo(this.readers),o=n.next();!o.done;o=n.next()){var i=o.value;try{return i.decode(t,this.hints)}catch(a){if(a instanceof Lo)continue}}}catch(s){e={error:s}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}throw new vt("No MultiFormat Readers were able to detect the code.")},t}(),xo=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();!function(t){function e(e,r){void 0===e&&(e=null),void 0===r&&(r=500);var n=new ko;return n.setHints(e),t.call(this,n,r)||this}xo(e,t),e.prototype.decodeBitmap=function(t){return this.reader.decodeWithState(t)}}(Mt);var Vo=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();!function(t){function e(e){return void 0===e&&(e=500),t.call(this,new Po,e)||this}Vo(e,t)}(Mt);var Uo,Ho=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();!function(t){function e(e){return void 0===e&&(e=500),t.call(this,new Xn,e)||this}Ho(e,t)}(Mt),function(t){t[t.ERROR_CORRECTION=0]="ERROR_CORRECTION",t[t.CHARACTER_SET=1]="CHARACTER_SET",t[t.DATA_MATRIX_SHAPE=2]="DATA_MATRIX_SHAPE",t[t.DATA_MATRIX_COMPACT=3]="DATA_MATRIX_COMPACT",t[t.MIN_SIZE=4]="MIN_SIZE",t[t.MAX_SIZE=5]="MAX_SIZE",t[t.MARGIN=6]="MARGIN",t[t.PDF417_COMPACT=7]="PDF417_COMPACT",t[t.PDF417_COMPACTION=8]="PDF417_COMPACTION",t[t.PDF417_DIMENSIONS=9]="PDF417_DIMENSIONS",t[t.AZTEC_LAYERS=10]="AZTEC_LAYERS",t[t.QR_VERSION=11]="QR_VERSION",t[t.GS1_FORMAT=12]="GS1_FORMAT",t[t.FORCE_C40=13]="FORCE_C40"}(Uo||(Uo={}));const Go=Uo;var Xo=function(){function t(t){this.field=t,this.cachedGenerators=[],this.cachedGenerators.push(new Ut(t,Int32Array.from([1])))}return t.prototype.buildGenerator=function(t){var e=this.cachedGenerators;if(t>=e.length)for(var r=e[e.length-1],n=this.field,o=e.length;o<=t;o++){var i=r.multiply(new Ut(n,Int32Array.from([1,n.exp(o-1+n.getGeneratorBase())])));e.push(i),r=i}return e[t]},t.prototype.encode=function(t,e){if(0===e)throw new j("No error correction bytes");var r=t.length-e;if(r<=0)throw new j("No data bytes provided");var n=this.buildGenerator(e),o=new Int32Array(r);q.arraycopy(t,0,o,0,r);for(var i=new Ut(this.field,o),a=(i=i.multiplyByMonomial(e,1)).divide(n)[1].getCoefficients(),s=e-a.length,u=0;u<s;u++)t[r+u]=0;q.arraycopy(a,0,t,r+s,a.length)},t}(),Wo=function(){function t(){}return t.applyMaskPenaltyRule1=function(e){return t.applyMaskPenaltyRule1Internal(e,!0)+t.applyMaskPenaltyRule1Internal(e,!1)},t.applyMaskPenaltyRule2=function(e){for(var r=0,n=e.getArray(),o=e.getWidth(),i=e.getHeight(),a=0;a<i-1;a++)for(var s=n[a],u=0;u<o-1;u++){var c=s[u];c===s[u+1]&&c===n[a+1][u]&&c===n[a+1][u+1]&&r++}return t.N2*r},t.applyMaskPenaltyRule3=function(e){for(var r=0,n=e.getArray(),o=e.getWidth(),i=e.getHeight(),a=0;a<i;a++)for(var s=0;s<o;s++){var u=n[a];s+6<o&&1===u[s]&&0===u[s+1]&&1===u[s+2]&&1===u[s+3]&&1===u[s+4]&&0===u[s+5]&&1===u[s+6]&&(t.isWhiteHorizontal(u,s-4,s)||t.isWhiteHorizontal(u,s+7,s+11))&&r++,a+6<i&&1===n[a][s]&&0===n[a+1][s]&&1===n[a+2][s]&&1===n[a+3][s]&&1===n[a+4][s]&&0===n[a+5][s]&&1===n[a+6][s]&&(t.isWhiteVertical(n,s,a-4,a)||t.isWhiteVertical(n,s,a+7,a+11))&&r++}return r*t.N3},t.isWhiteHorizontal=function(t,e,r){e=Math.max(e,0),r=Math.min(r,t.length);for(var n=e;n<r;n++)if(1===t[n])return!1;return!0},t.isWhiteVertical=function(t,e,r,n){r=Math.max(r,0),n=Math.min(n,t.length);for(var o=r;o<n;o++)if(1===t[o][e])return!1;return!0},t.applyMaskPenaltyRule4=function(e){for(var r=0,n=e.getArray(),o=e.getWidth(),i=e.getHeight(),a=0;a<i;a++)for(var s=n[a],u=0;u<o;u++)1===s[u]&&r++;var c=e.getHeight()*e.getWidth();return Math.floor(10*Math.abs(2*r-c)/c)*t.N4},t.getDataMaskBit=function(t,e,r){var n,o;switch(t){case 0:n=r+e&1;break;case 1:n=1&r;break;case 2:n=e%3;break;case 3:n=(r+e)%3;break;case 4:n=Math.floor(r/2)+Math.floor(e/3)&1;break;case 5:n=(1&(o=r*e))+o%3;break;case 6:n=(1&(o=r*e))+o%3&1;break;case 7:n=(o=r*e)%3+(r+e&1)&1;break;default:throw new j("Invalid mask pattern: "+t)}return 0===n},t.applyMaskPenaltyRule1Internal=function(e,r){for(var n=0,o=r?e.getHeight():e.getWidth(),i=r?e.getWidth():e.getHeight(),a=e.getArray(),s=0;s<o;s++){for(var u=0,c=-1,f=0;f<i;f++){var h=r?a[s][f]:a[f][s];h===c?u++:(u>=5&&(n+=t.N1+(u-5)),u=1,c=h)}u>=5&&(n+=t.N1+(u-5))}return n},t.N1=3,t.N2=3,t.N3=40,t.N4=10,t}(),jo=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},zo=function(){function t(t,e){this.width=t,this.height=e;for(var r=new Array(e),n=0;n!==e;n++)r[n]=new Uint8Array(t);this.bytes=r}return t.prototype.getHeight=function(){return this.height},t.prototype.getWidth=function(){return this.width},t.prototype.get=function(t,e){return this.bytes[e][t]},t.prototype.getArray=function(){return this.bytes},t.prototype.setNumber=function(t,e,r){this.bytes[e][t]=r},t.prototype.setBoolean=function(t,e,r){this.bytes[e][t]=r?1:0},t.prototype.clear=function(t){var e,r;try{for(var n=jo(this.bytes),o=n.next();!o.done;o=n.next()){var i=o.value;rt.fill(i,t)}}catch(a){e={error:a}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}},t.prototype.equals=function(e){if(!(e instanceof t))return!1;var r=e;if(this.width!==r.width)return!1;if(this.height!==r.height)return!1;for(var n=0,o=this.height;n<o;++n)for(var i=this.bytes[n],a=r.bytes[n],s=0,u=this.width;s<u;++s)if(i[s]!==a[s])return!1;return!0},t.prototype.toString=function(){for(var t=new gt,e=0,r=this.height;e<r;++e){for(var n=this.bytes[e],o=0,i=this.width;o<i;++o)switch(n[o]){case 0:t.append(" 0");break;case 1:t.append(" 1");break;default:t.append("  ")}t.append("\n")}return t.toString()},t}(),Yo=function(){function t(){this.maskPattern=-1}return t.prototype.getMode=function(){return this.mode},t.prototype.getECLevel=function(){return this.ecLevel},t.prototype.getVersion=function(){return this.version},t.prototype.getMaskPattern=function(){return this.maskPattern},t.prototype.getMatrix=function(){return this.matrix},t.prototype.toString=function(){var t=new gt;return t.append("<<\n"),t.append(" mode: "),t.append(this.mode?this.mode.toString():"null"),t.append("\n ecLevel: "),t.append(this.ecLevel?this.ecLevel.toString():"null"),t.append("\n version: "),t.append(this.version?this.version.toString():"null"),t.append("\n maskPattern: "),t.append(this.maskPattern.toString()),this.matrix?(t.append("\n matrix:\n"),t.append(this.matrix.toString())):t.append("\n matrix: null\n"),t.append(">>\n"),t.toString()},t.prototype.setMode=function(t){this.mode=t},t.prototype.setECLevel=function(t){this.ecLevel=t},t.prototype.setVersion=function(t){this.version=t},t.prototype.setMaskPattern=function(t){this.maskPattern=t},t.prototype.setMatrix=function(t){this.matrix=t},t.isValidMaskPattern=function(e){return e>=0&&e<t.NUM_MASK_PATTERNS},t.NUM_MASK_PATTERNS=8,t}(),Zo=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Ko=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return Zo(e,t),e.kind="WriterException",e}(H),qo=function(){function t(){}return t.clearMatrix=function(t){t.clear(255)},t.buildMatrix=function(e,r,n,o,i){t.clearMatrix(i),t.embedBasicPatterns(n,i),t.embedTypeInfo(r,o,i),t.maybeEmbedVersionInfo(n,i),t.embedDataBits(e,o,i)},t.embedBasicPatterns=function(e,r){t.embedPositionDetectionPatternsAndSeparators(r),t.embedDarkDotAtLeftBottomCorner(r),t.maybeEmbedPositionAdjustmentPatterns(e,r),t.embedTimingPatterns(r)},t.embedTypeInfo=function(e,r,n){var o=new ot;t.makeTypeInfoBits(e,r,o);for(var i=0,a=o.getSize();i<a;++i){var s=o.get(o.getSize()-1-i),u=t.TYPE_INFO_COORDINATES[i],c=u[0],f=u[1];if(n.setBoolean(c,f,s),i<8){var h=n.getWidth()-i-1,l=8;n.setBoolean(h,l,s)}else h=8,l=n.getHeight()-7+(i-8),n.setBoolean(h,l,s)}},t.maybeEmbedVersionInfo=function(e,r){if(!(e.getVersionNumber()<7)){var n=new ot;t.makeVersionInfoBits(e,n);for(var o=17,i=0;i<6;++i)for(var a=0;a<3;++a){var s=n.get(o);o--,r.setBoolean(i,r.getHeight()-11+a,s),r.setBoolean(r.getHeight()-11+a,i,s)}}},t.embedDataBits=function(e,r,n){for(var o=0,i=-1,a=n.getWidth()-1,s=n.getHeight()-1;a>0;){for(6===a&&(a-=1);s>=0&&s<n.getHeight();){for(var u=0;u<2;++u){var c=a-u;if(t.isEmpty(n.get(c,s))){var f=void 0;o<e.getSize()?(f=e.get(o),++o):f=!1,255!==r&&Wo.getDataMaskBit(r,c,s)&&(f=!f),n.setBoolean(c,s,f)}}s+=i}s+=i=-i,a-=2}if(o!==e.getSize())throw new Ko("Not all bits consumed: "+o+"/"+e.getSize())},t.findMSBSet=function(t){return 32-nt.numberOfLeadingZeros(t)},t.calculateBCHCode=function(e,r){if(0===r)throw new j("0 polynomial");var n=t.findMSBSet(r);for(e<<=n-1;t.findMSBSet(e)>=n;)e^=r<<t.findMSBSet(e)-n;return e},t.makeTypeInfoBits=function(e,r,n){if(!Yo.isValidMaskPattern(r))throw new Ko("Invalid mask pattern");var o=e.getBits()<<3|r;n.appendBits(o,5);var i=t.calculateBCHCode(o,t.TYPE_INFO_POLY);n.appendBits(i,10);var a=new ot;if(a.appendBits(t.TYPE_INFO_MASK_PATTERN,15),n.xor(a),15!==n.getSize())throw new Ko("should not happen but we got: "+n.getSize())},t.makeVersionInfoBits=function(e,r){r.appendBits(e.getVersionNumber(),6);var n=t.calculateBCHCode(e.getVersionNumber(),t.VERSION_INFO_POLY);if(r.appendBits(n,12),18!==r.getSize())throw new Ko("should not happen but we got: "+r.getSize())},t.isEmpty=function(t){return 255===t},t.embedTimingPatterns=function(e){for(var r=8;r<e.getWidth()-8;++r){var n=(r+1)%2;t.isEmpty(e.get(r,6))&&e.setNumber(r,6,n),t.isEmpty(e.get(6,r))&&e.setNumber(6,r,n)}},t.embedDarkDotAtLeftBottomCorner=function(t){if(0===t.get(8,t.getHeight()-8))throw new Ko;t.setNumber(8,t.getHeight()-8,1)},t.embedHorizontalSeparationPattern=function(e,r,n){for(var o=0;o<8;++o){if(!t.isEmpty(n.get(e+o,r)))throw new Ko;n.setNumber(e+o,r,0)}},t.embedVerticalSeparationPattern=function(e,r,n){for(var o=0;o<7;++o){if(!t.isEmpty(n.get(e,r+o)))throw new Ko;n.setNumber(e,r+o,0)}},t.embedPositionAdjustmentPattern=function(e,r,n){for(var o=0;o<5;++o)for(var i=t.POSITION_ADJUSTMENT_PATTERN[o],a=0;a<5;++a)n.setNumber(e+a,r+o,i[a])},t.embedPositionDetectionPattern=function(e,r,n){for(var o=0;o<7;++o)for(var i=t.POSITION_DETECTION_PATTERN[o],a=0;a<7;++a)n.setNumber(e+a,r+o,i[a])},t.embedPositionDetectionPatternsAndSeparators=function(e){var r=t.POSITION_DETECTION_PATTERN[0].length;t.embedPositionDetectionPattern(0,0,e),t.embedPositionDetectionPattern(e.getWidth()-r,0,e),t.embedPositionDetectionPattern(0,e.getWidth()-r,e),t.embedHorizontalSeparationPattern(0,7,e),t.embedHorizontalSeparationPattern(e.getWidth()-8,7,e),t.embedHorizontalSeparationPattern(0,e.getWidth()-8,e),t.embedVerticalSeparationPattern(7,0,e),t.embedVerticalSeparationPattern(e.getHeight()-7-1,0,e),t.embedVerticalSeparationPattern(7,e.getHeight()-7,e)},t.maybeEmbedPositionAdjustmentPatterns=function(e,r){if(!(e.getVersionNumber()<2))for(var n=e.getVersionNumber()-1,o=t.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE[n],i=0,a=o.length;i!==a;i++){var s=o[i];if(s>=0)for(var u=0;u!==a;u++){var c=o[u];c>=0&&t.isEmpty(r.get(c,s))&&t.embedPositionAdjustmentPattern(c-2,s-2,r)}}},t.POSITION_DETECTION_PATTERN=Array.from([Int32Array.from([1,1,1,1,1,1,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,1,1,1,1,1,1])]),t.POSITION_ADJUSTMENT_PATTERN=Array.from([Int32Array.from([1,1,1,1,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,0,1,0,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,1,1,1,1])]),t.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE=Array.from([Int32Array.from([-1,-1,-1,-1,-1,-1,-1]),Int32Array.from([6,18,-1,-1,-1,-1,-1]),Int32Array.from([6,22,-1,-1,-1,-1,-1]),Int32Array.from([6,26,-1,-1,-1,-1,-1]),Int32Array.from([6,30,-1,-1,-1,-1,-1]),Int32Array.from([6,34,-1,-1,-1,-1,-1]),Int32Array.from([6,22,38,-1,-1,-1,-1]),Int32Array.from([6,24,42,-1,-1,-1,-1]),Int32Array.from([6,26,46,-1,-1,-1,-1]),Int32Array.from([6,28,50,-1,-1,-1,-1]),Int32Array.from([6,30,54,-1,-1,-1,-1]),Int32Array.from([6,32,58,-1,-1,-1,-1]),Int32Array.from([6,34,62,-1,-1,-1,-1]),Int32Array.from([6,26,46,66,-1,-1,-1]),Int32Array.from([6,26,48,70,-1,-1,-1]),Int32Array.from([6,26,50,74,-1,-1,-1]),Int32Array.from([6,30,54,78,-1,-1,-1]),Int32Array.from([6,30,56,82,-1,-1,-1]),Int32Array.from([6,30,58,86,-1,-1,-1]),Int32Array.from([6,34,62,90,-1,-1,-1]),Int32Array.from([6,28,50,72,94,-1,-1]),Int32Array.from([6,26,50,74,98,-1,-1]),Int32Array.from([6,30,54,78,102,-1,-1]),Int32Array.from([6,28,54,80,106,-1,-1]),Int32Array.from([6,32,58,84,110,-1,-1]),Int32Array.from([6,30,58,86,114,-1,-1]),Int32Array.from([6,34,62,90,118,-1,-1]),Int32Array.from([6,26,50,74,98,122,-1]),Int32Array.from([6,30,54,78,102,126,-1]),Int32Array.from([6,26,52,78,104,130,-1]),Int32Array.from([6,30,56,82,108,134,-1]),Int32Array.from([6,34,60,86,112,138,-1]),Int32Array.from([6,30,58,86,114,142,-1]),Int32Array.from([6,34,62,90,118,146,-1]),Int32Array.from([6,30,54,78,102,126,150]),Int32Array.from([6,24,50,76,102,128,154]),Int32Array.from([6,28,54,80,106,132,158]),Int32Array.from([6,32,58,84,110,136,162]),Int32Array.from([6,26,54,82,110,138,166]),Int32Array.from([6,30,58,86,114,142,170])]),t.TYPE_INFO_COORDINATES=Array.from([Int32Array.from([8,0]),Int32Array.from([8,1]),Int32Array.from([8,2]),Int32Array.from([8,3]),Int32Array.from([8,4]),Int32Array.from([8,5]),Int32Array.from([8,7]),Int32Array.from([8,8]),Int32Array.from([7,8]),Int32Array.from([5,8]),Int32Array.from([4,8]),Int32Array.from([3,8]),Int32Array.from([2,8]),Int32Array.from([1,8]),Int32Array.from([0,8])]),t.VERSION_INFO_POLY=7973,t.TYPE_INFO_POLY=1335,t.TYPE_INFO_MASK_PATTERN=21522,t}(),Qo=function(){function t(t,e){this.dataBytes=t,this.errorCorrectionBytes=e}return t.prototype.getDataBytes=function(){return this.dataBytes},t.prototype.getErrorCorrectionBytes=function(){return this.errorCorrectionBytes},t}(),Jo=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};!function(){function t(){}t.calculateMaskPenalty=function(t){return Wo.applyMaskPenaltyRule1(t)+Wo.applyMaskPenaltyRule2(t)+Wo.applyMaskPenaltyRule3(t)+Wo.applyMaskPenaltyRule4(t)},t.encode=function(e,r,n){void 0===n&&(n=null);var o=t.DEFAULT_BYTE_MODE_ENCODING,i=null!==n&&void 0!==n.get(Go.CHARACTER_SET);i&&(o=n.get(Go.CHARACTER_SET).toString());var a=this.chooseMode(e,o),s=new ot;if(a===On.BYTE&&(i||t.DEFAULT_BYTE_MODE_ENCODING!==o)){var u=ft.getCharacterSetECIByName(o);void 0!==u&&this.appendECI(u,s)}this.appendModeInfo(a,s);var c,f=new ot;if(this.appendBytes(e,a,f,o),null!==n&&void 0!==n.get(Go.QR_VERSION)){var h=Number.parseInt(n.get(Go.QR_VERSION).toString(),10);c=mn.getVersionForNumber(h);var l=this.calculateBitsNeeded(a,s,f,c);if(!this.willFit(l,c,r))throw new Ko("Data too big for requested version")}else c=this.recommendVersion(r,a,s,f);var d=new ot;d.appendBitArray(s);var p=a===On.BYTE?f.getSizeInBytes():e.length;this.appendLengthInfo(p,c,a,d),d.appendBitArray(f);var g=c.getECBlocksForLevel(r),y=c.getTotalCodewords()-g.getTotalECCodewords();this.terminateBits(y,d);var w=this.interleaveWithECBytes(d,c.getTotalCodewords(),y,g.getNumBlocks()),v=new Yo;v.setECLevel(r),v.setMode(a),v.setVersion(c);var _=c.getDimensionForVersion(),m=new zo(_,_),C=this.chooseMaskPattern(w,r,c,m);return v.setMaskPattern(C),qo.buildMatrix(w,r,c,C,m),v.setMatrix(m),v},t.recommendVersion=function(t,e,r,n){var o=this.calculateBitsNeeded(e,r,n,mn.getVersionForNumber(1)),i=this.chooseVersion(o,t),a=this.calculateBitsNeeded(e,r,n,i);return this.chooseVersion(a,t)},t.calculateBitsNeeded=function(t,e,r,n){return e.getSize()+t.getCharacterCountBits(n)+r.getSize()},t.getAlphanumericCode=function(e){return e<t.ALPHANUMERIC_TABLE.length?t.ALPHANUMERIC_TABLE[e]:-1},t.chooseMode=function(e,r){if(void 0===r&&(r=null),ft.SJIS.getName()===r&&this.isOnlyDoubleByteKanji(e))return On.KANJI;for(var n=!1,o=!1,i=0,a=e.length;i<a;++i){var s=e.charAt(i);if(t.isDigit(s))n=!0;else{if(-1===this.getAlphanumericCode(s.charCodeAt(0)))return On.BYTE;o=!0}}return o?On.ALPHANUMERIC:n?On.NUMERIC:On.BYTE},t.isOnlyDoubleByteKanji=function(t){var e;try{e=dt.encode(t,ft.SJIS)}catch(i){return!1}var r=e.length;if(r%2!=0)return!1;for(var n=0;n<r;n+=2){var o=255&e[n];if((o<129||o>159)&&(o<224||o>235))return!1}return!0},t.chooseMaskPattern=function(t,e,r,n){for(var o=Number.MAX_SAFE_INTEGER,i=-1,a=0;a<Yo.NUM_MASK_PATTERNS;a++){qo.buildMatrix(t,e,r,a,n);var s=this.calculateMaskPenalty(n);s<o&&(o=s,i=a)}return i},t.chooseVersion=function(e,r){for(var n=1;n<=40;n++){var o=mn.getVersionForNumber(n);if(t.willFit(e,o,r))return o}throw new Ko("Data too big")},t.willFit=function(t,e,r){return e.getTotalCodewords()-e.getECBlocksForLevel(r).getTotalECCodewords()>=(t+7)/8},t.terminateBits=function(t,e){var r=8*t;if(e.getSize()>r)throw new Ko("data bits cannot fit in the QR Code"+e.getSize()+" > "+r);for(var n=0;n<4&&e.getSize()<r;++n)e.appendBit(!1);var o=7&e.getSize();if(o>0)for(n=o;n<8;n++)e.appendBit(!1);var i=t-e.getSizeInBytes();for(n=0;n<i;++n)e.appendBits(1&n?17:236,8);if(e.getSize()!==r)throw new Ko("Bits size does not equal capacity")},t.getNumDataBytesAndNumECBytesForBlockID=function(t,e,r,n,o,i){if(n>=r)throw new Ko("Block ID too large");var a=t%r,s=r-a,u=Math.floor(t/r),c=u+1,f=Math.floor(e/r),h=f+1,l=u-f,d=c-h;if(l!==d)throw new Ko("EC bytes mismatch");if(r!==s+a)throw new Ko("RS blocks mismatch");if(t!==(f+l)*s+(h+d)*a)throw new Ko("Total bytes mismatch");n<s?(o[0]=f,i[0]=l):(o[0]=h,i[0]=d)},t.interleaveWithECBytes=function(e,r,n,o){var i,a,s,u;if(e.getSizeInBytes()!==n)throw new Ko("Number of bits and data bytes does not match");for(var c=0,f=0,h=0,l=new Array,d=0;d<o;++d){var p=new Int32Array(1),g=new Int32Array(1);t.getNumDataBytesAndNumECBytesForBlockID(r,n,o,d,p,g);var y=p[0],w=new Uint8Array(y);e.toBytes(8*c,w,0,y);var v=t.generateECBytes(w,g[0]);l.push(new Qo(w,v)),f=Math.max(f,y),h=Math.max(h,v.length),c+=p[0]}if(n!==c)throw new Ko("Data bytes does not match offset");var _=new ot;for(d=0;d<f;++d)try{for(var m=(i=void 0,Jo(l)),C=m.next();!C.done;C=m.next())d<(w=C.value.getDataBytes()).length&&_.appendBits(w[d],8)}catch(I){i={error:I}}finally{try{C&&!C.done&&(a=m.return)&&a.call(m)}finally{if(i)throw i.error}}for(d=0;d<h;++d)try{for(var A=(s=void 0,Jo(l)),E=A.next();!E.done;E=A.next())d<(v=E.value.getErrorCorrectionBytes()).length&&_.appendBits(v[d],8)}catch(S){s={error:S}}finally{try{E&&!E.done&&(u=A.return)&&u.call(A)}finally{if(s)throw s.error}}if(r!==_.getSizeInBytes())throw new Ko("Interleaving error: "+r+" and "+_.getSizeInBytes()+" differ.");return _},t.generateECBytes=function(t,e){for(var r=t.length,n=new Int32Array(r+e),o=0;o<r;o++)n[o]=255&t[o];new Xo(Wt.QR_CODE_FIELD_256).encode(n,e);var i=new Uint8Array(e);for(o=0;o<e;o++)i[o]=n[r+o];return i},t.appendModeInfo=function(t,e){e.appendBits(t.getBits(),4)},t.appendLengthInfo=function(t,e,r,n){var o=r.getCharacterCountBits(e);if(t>=1<<o)throw new Ko(t+" is bigger than "+((1<<o)-1));n.appendBits(t,o)},t.appendBytes=function(e,r,n,o){switch(r){case On.NUMERIC:t.appendNumericBytes(e,n);break;case On.ALPHANUMERIC:t.appendAlphanumericBytes(e,n);break;case On.BYTE:t.append8BitBytes(e,n,o);break;case On.KANJI:t.appendKanjiBytes(e,n);break;default:throw new Ko("Invalid mode: "+r)}},t.getDigit=function(t){return t.charCodeAt(0)-48},t.isDigit=function(e){var r=t.getDigit(e);return r>=0&&r<=9},t.appendNumericBytes=function(e,r){for(var n=e.length,o=0;o<n;){var i=t.getDigit(e.charAt(o));if(o+2<n){var a=t.getDigit(e.charAt(o+1)),s=t.getDigit(e.charAt(o+2));r.appendBits(100*i+10*a+s,10),o+=3}else o+1<n?(a=t.getDigit(e.charAt(o+1)),r.appendBits(10*i+a,7),o+=2):(r.appendBits(i,4),o++)}},t.appendAlphanumericBytes=function(e,r){for(var n=e.length,o=0;o<n;){var i=t.getAlphanumericCode(e.charCodeAt(o));if(-1===i)throw new Ko;if(o+1<n){var a=t.getAlphanumericCode(e.charCodeAt(o+1));if(-1===a)throw new Ko;r.appendBits(45*i+a,11),o+=2}else r.appendBits(i,6),o++}},t.append8BitBytes=function(t,e,r){var n;try{n=dt.encode(t,r)}catch(s){throw new Ko(s)}for(var o=0,i=n.length;o!==i;o++){var a=n[o];e.appendBits(a,8)}},t.appendKanjiBytes=function(t,e){var r;try{r=dt.encode(t,ft.SJIS)}catch(u){throw new Ko(u)}for(var n=r.length,o=0;o<n;o+=2){var i=(255&r[o])<<8&4294967295|255&r[o+1],a=-1;if(i>=33088&&i<=40956?a=i-33088:i>=57408&&i<=60351&&(a=i-49472),-1===a)throw new Ko("Invalid byte sequence");var s=192*(a>>8)+(255&a);e.appendBits(s,13)}},t.appendECI=function(t,e){e.appendBits(On.ECI.getBits(),4),e.appendBits(t.getValue(),8)},t.ALPHANUMERIC_TABLE=Int32Array.from([-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,36,-1,-1,-1,37,38,-1,-1,-1,-1,39,40,-1,41,42,43,0,1,2,3,4,5,6,7,8,9,44,-1,-1,-1,-1,-1,-1,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,-1,-1,-1,-1,-1]),t.DEFAULT_BYTE_MODE_ENCODING=ft.UTF8.getName()}();var $o=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();!function(t){function e(e,r,n,o,i,a,s,u){var c=t.call(this,a,s)||this;if(c.yuvData=e,c.dataWidth=r,c.dataHeight=n,c.left=o,c.top=i,o+a>r||i+s>n)throw new j("Crop rectangle does not fit within image data.");return u&&c.reverseHorizontal(a,s),c}$o(e,t),e.prototype.getRow=function(t,e){if(t<0||t>=this.getHeight())throw new j("Requested row is outside the image: "+t);var r=this.getWidth();(null==e||e.length<r)&&(e=new Uint8ClampedArray(r));var n=(t+this.top)*this.dataWidth+this.left;return q.arraycopy(this.yuvData,n,e,0,r),e},e.prototype.getMatrix=function(){var t=this.getWidth(),e=this.getHeight();if(t===this.dataWidth&&e===this.dataHeight)return this.yuvData;var r=t*e,n=new Uint8ClampedArray(r),o=this.top*this.dataWidth+this.left;if(t===this.dataWidth)return q.arraycopy(this.yuvData,o,n,0,r),n;for(var i=0;i<e;i++){var a=i*t;q.arraycopy(this.yuvData,o,n,a,t),o+=this.dataWidth}return n},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(t,r,n,o){return new e(this.yuvData,this.dataWidth,this.dataHeight,this.left+t,this.top+r,n,o,!1)},e.prototype.renderThumbnail=function(){for(var t=this.getWidth()/e.THUMBNAIL_SCALE_FACTOR,r=this.getHeight()/e.THUMBNAIL_SCALE_FACTOR,n=new Int32Array(t*r),o=this.yuvData,i=this.top*this.dataWidth+this.left,a=0;a<r;a++){for(var s=a*t,u=0;u<t;u++){var c=255&o[i+u*e.THUMBNAIL_SCALE_FACTOR];n[s+u]=4278190080|65793*c}i+=this.dataWidth*e.THUMBNAIL_SCALE_FACTOR}return n},e.prototype.getThumbnailWidth=function(){return this.getWidth()/e.THUMBNAIL_SCALE_FACTOR},e.prototype.getThumbnailHeight=function(){return this.getHeight()/e.THUMBNAIL_SCALE_FACTOR},e.prototype.reverseHorizontal=function(t,e){for(var r=this.yuvData,n=0,o=this.top*this.dataWidth+this.left;n<e;n++,o+=this.dataWidth)for(var i=o+t/2,a=o,s=o+t-1;a<i;a++,s--){var u=r[a];r[a]=r[s],r[s]=u}},e.prototype.invert=function(){return new St(this)},e.THUMBNAIL_SCALE_FACTOR=2}(Et);var ti=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();!function(t){function e(e,r,n,o,i,a,s){var u=t.call(this,r,n)||this;if(u.dataWidth=o,u.dataHeight=i,u.left=a,u.top=s,4===e.BYTES_PER_ELEMENT){for(var c=r*n,f=new Uint8ClampedArray(c),h=0;h<c;h++){var l=e[h],d=l>>16&255,p=l>>7&510,g=255&l;f[h]=(d+p+g)/4&255}u.luminances=f}else u.luminances=e;if(void 0===o&&(u.dataWidth=r),void 0===i&&(u.dataHeight=n),void 0===a&&(u.left=0),void 0===s&&(u.top=0),u.left+r>u.dataWidth||u.top+n>u.dataHeight)throw new j("Crop rectangle does not fit within image data.");return u}ti(e,t),e.prototype.getRow=function(t,e){if(t<0||t>=this.getHeight())throw new j("Requested row is outside the image: "+t);var r=this.getWidth();(null==e||e.length<r)&&(e=new Uint8ClampedArray(r));var n=(t+this.top)*this.dataWidth+this.left;return q.arraycopy(this.luminances,n,e,0,r),e},e.prototype.getMatrix=function(){var t=this.getWidth(),e=this.getHeight();if(t===this.dataWidth&&e===this.dataHeight)return this.luminances;var r=t*e,n=new Uint8ClampedArray(r),o=this.top*this.dataWidth+this.left;if(t===this.dataWidth)return q.arraycopy(this.luminances,o,n,0,r),n;for(var i=0;i<e;i++){var a=i*t;q.arraycopy(this.luminances,o,n,a,t),o+=this.dataWidth}return n},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(t,r,n,o){return new e(this.luminances,n,o,this.dataWidth,this.dataHeight,this.left+t,this.top+r)},e.prototype.invert=function(){return new St(this)}}(Et);var ei,ri,ni=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),oi=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ni(e,t),e.forName=function(t){return this.getCharacterSetECIByName(t)},e}(ft),ii=function(){function t(){}return t.ISO_8859_1=ft.ISO8859_1,t}();(ei=function(t,e){for(var r=1,n=0;n<255;n++)e[n]=r,t[r]=n,(r*=2)>=256&&(r^=301);return{LOG:t,ALOG:e}}([],[])).LOG,ei.ALOG,function(t){t[t.FORCE_NONE=0]="FORCE_NONE",t[t.FORCE_SQUARE=1]="FORCE_SQUARE",t[t.FORCE_RECTANGLE=2]="FORCE_RECTANGLE"}(ri||(ri={}));var ai,si="[)>05",ui="[)>06",ci=function(){function t(){}return t.prototype.getEncodingMode=function(){return 0},t.prototype.encode=function(t){if(Ei.determineConsecutiveDigitCount(t.getMessage(),t.pos)>=2)t.writeCodeword(this.encodeASCIIDigits(t.getMessage().charCodeAt(t.pos),t.getMessage().charCodeAt(t.pos+1))),t.pos+=2;else{var e=t.getCurrentChar(),r=Ei.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode());if(r!==this.getEncodingMode())switch(r){case 5:return t.writeCodeword(231),void t.signalEncoderChange(5);case 1:return t.writeCodeword(230),void t.signalEncoderChange(1);case 3:t.writeCodeword(238),t.signalEncoderChange(3);break;case 2:t.writeCodeword(239),t.signalEncoderChange(2);break;case 4:t.writeCodeword(240),t.signalEncoderChange(4);break;default:throw new Error("Illegal mode: "+r)}else Ei.isExtendedASCII(e)?(t.writeCodeword(235),t.writeCodeword(e-128+1),t.pos++):(t.writeCodeword(e+1),t.pos++)}},t.prototype.encodeASCIIDigits=function(t,e){if(Ei.isDigit(t)&&Ei.isDigit(e))return 10*(t-48)+(e-48)+130;throw new Error("not digits: "+t+e)},t}(),fi=function(){function t(){}return t.prototype.getEncodingMode=function(){return 5},t.prototype.encode=function(t){var e=new gt;for(e.append(0);t.hasMoreCharacters();){var r=t.getCurrentChar();if(e.append(r),t.pos++,Ei.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode())!==this.getEncodingMode()){t.signalEncoderChange(0);break}}var n=e.length()-1,o=t.getCodewordCount()+n+1;t.updateSymbolInfo(o);var i=t.getSymbolInfo().getDataCapacity()-o>0;if(t.hasMoreCharacters()||i)if(n<=249)e.setCharAt(0,pt.getCharAt(n));else{if(!(n<=1555))throw new Error("Message length not in valid ranges: "+n);e.setCharAt(0,pt.getCharAt(Math.floor(n/250)+249)),e.insert(1,pt.getCharAt(n%250))}var a=0;for(r=e.length();a<r;a++)t.writeCodeword(this.randomize255State(e.charAt(a).charCodeAt(0),t.getCodewordCount()+1))},t.prototype.randomize255State=function(t,e){var r=t+(149*e%255+1);return r<=255?r:r-256},t}(),hi=function(){function t(){}return t.prototype.getEncodingMode=function(){return 1},t.prototype.encodeMaximal=function(t){for(var e=new gt,r=0,n=t.pos,o=0;t.hasMoreCharacters();){var i=t.getCurrentChar();t.pos++,r=this.encodeChar(i,e),e.length()%3==0&&(n=t.pos,o=e.length())}if(o!==e.length()){var a=Math.floor(e.length()/3*2),s=Math.floor(t.getCodewordCount()+a+1);t.updateSymbolInfo(s);var u=t.getSymbolInfo().getDataCapacity()-s,c=Math.floor(e.length()%3);(2===c&&2!==u||1===c&&(r>3||1!==u))&&(t.pos=n)}e.length()>0&&t.writeCodeword(230),this.handleEOD(t,e)},t.prototype.encode=function(t){for(var e=new gt;t.hasMoreCharacters();){var r=t.getCurrentChar();t.pos++;var n=this.encodeChar(r,e),o=2*Math.floor(e.length()/3),i=t.getCodewordCount()+o;t.updateSymbolInfo(i);var a=t.getSymbolInfo().getDataCapacity()-i;if(!t.hasMoreCharacters()){var s=new gt;for(e.length()%3==2&&2!==a&&(n=this.backtrackOneCharacter(t,e,s,n));e.length()%3==1&&(n>3||1!==a);)n=this.backtrackOneCharacter(t,e,s,n);break}if(e.length()%3==0&&Ei.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode())!==this.getEncodingMode()){t.signalEncoderChange(0);break}}this.handleEOD(t,e)},t.prototype.backtrackOneCharacter=function(t,e,r,n){var o=e.length(),i=e.toString().substring(0,o-n);e.setLengthToZero(),e.append(i),t.pos--;var a=t.getCurrentChar();return n=this.encodeChar(a,r),t.resetSymbolInfo(),n},t.prototype.writeNextTriplet=function(t,e){t.writeCodewords(this.encodeToCodewords(e.toString()));var r=e.toString().substring(3);e.setLengthToZero(),e.append(r)},t.prototype.handleEOD=function(t,e){var r=Math.floor(e.length()/3*2),n=e.length()%3,o=t.getCodewordCount()+r;t.updateSymbolInfo(o);var i=t.getSymbolInfo().getDataCapacity()-o;if(2===n){for(e.append("\0");e.length()>=3;)this.writeNextTriplet(t,e);t.hasMoreCharacters()&&t.writeCodeword(254)}else if(1===i&&1===n){for(;e.length()>=3;)this.writeNextTriplet(t,e);t.hasMoreCharacters()&&t.writeCodeword(254),t.pos--}else{if(0!==n)throw new Error("Unexpected case. Please report!");for(;e.length()>=3;)this.writeNextTriplet(t,e);(i>0||t.hasMoreCharacters())&&t.writeCodeword(254)}t.signalEncoderChange(0)},t.prototype.encodeChar=function(t,e){if(t===" ".charCodeAt(0))return e.append(3),1;if(t>="0".charCodeAt(0)&&t<="9".charCodeAt(0))return e.append(t-48+4),1;if(t>="A".charCodeAt(0)&&t<="Z".charCodeAt(0))return e.append(t-65+14),1;if(t<" ".charCodeAt(0))return e.append(0),e.append(t),2;if(t<="/".charCodeAt(0))return e.append(1),e.append(t-33),2;if(t<="@".charCodeAt(0))return e.append(1),e.append(t-58+15),2;if(t<="_".charCodeAt(0))return e.append(1),e.append(t-91+22),2;if(t<=127)return e.append(2),e.append(t-96),2;e.append("1");var r=2;return r+=this.encodeChar(t-128,e)},t.prototype.encodeToCodewords=function(t){var e=1600*t.charCodeAt(0)+40*t.charCodeAt(1)+t.charCodeAt(2)+1,r=e/256,n=e%256,o=new gt;return o.append(r),o.append(n),o.toString()},t}(),li=function(){function t(){}return t.prototype.getEncodingMode=function(){return 4},t.prototype.encode=function(t){for(var e=new gt;t.hasMoreCharacters();){var r=t.getCurrentChar();if(this.encodeChar(r,e),t.pos++,e.length()>=4){t.writeCodewords(this.encodeToCodewords(e.toString()));var n=e.toString().substring(4);if(e.setLengthToZero(),e.append(n),Ei.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode())!==this.getEncodingMode()){t.signalEncoderChange(0);break}}}e.append(pt.getCharAt(31)),this.handleEOD(t,e)},t.prototype.handleEOD=function(t,e){try{var r=e.length();if(0===r)return;if(1===r){t.updateSymbolInfo();var n=t.getSymbolInfo().getDataCapacity()-t.getCodewordCount(),o=t.getRemainingCharacters();if(o>n&&(t.updateSymbolInfo(t.getCodewordCount()+1),n=t.getSymbolInfo().getDataCapacity()-t.getCodewordCount()),o<=n&&n<=2)return}if(r>4)throw new Error("Count must not exceed 4");var i=r-1,a=this.encodeToCodewords(e.toString()),s=!t.hasMoreCharacters()&&i<=2;i<=2&&(t.updateSymbolInfo(t.getCodewordCount()+i),(n=t.getSymbolInfo().getDataCapacity()-t.getCodewordCount())>=3&&(s=!1,t.updateSymbolInfo(t.getCodewordCount()+a.length))),s?(t.resetSymbolInfo(),t.pos-=i):t.writeCodewords(a)}finally{t.signalEncoderChange(0)}},t.prototype.encodeChar=function(t,e){t>=" ".charCodeAt(0)&&t<="?".charCodeAt(0)?e.append(t):t>="@".charCodeAt(0)&&t<="^".charCodeAt(0)?e.append(pt.getCharAt(t-64)):Ei.illegalCharacter(pt.getCharAt(t))},t.prototype.encodeToCodewords=function(t){var e=t.length;if(0===e)throw new Error("StringBuilder must not be empty");var r=(t.charAt(0).charCodeAt(0)<<18)+((e>=2?t.charAt(1).charCodeAt(0):0)<<12)+((e>=3?t.charAt(2).charCodeAt(0):0)<<6)+(e>=4?t.charAt(3).charCodeAt(0):0),n=r>>16&255,o=r>>8&255,i=255&r,a=new gt;return a.append(n),e>=2&&a.append(o),e>=3&&a.append(i),a.toString()},t}(),di=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),pi=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},gi=function(){function t(t,e,r,n,o,i,a,s){void 0===a&&(a=0),void 0===s&&(s=0),this.rectangular=t,this.dataCapacity=e,this.errorCodewords=r,this.matrixWidth=n,this.matrixHeight=o,this.dataRegions=i,this.rsBlockData=a,this.rsBlockError=s}return t.lookup=function(t,e,r,n,o){var i,a;void 0===e&&(e=0),void 0===r&&(r=null),void 0===n&&(n=null),void 0===o&&(o=!0);try{for(var s=pi(wi),u=s.next();!u.done;u=s.next()){var c=u.value;if((1!==e||!c.rectangular)&&(2!==e||c.rectangular)&&(null==r||!(c.getSymbolWidth()<r.getWidth()||c.getSymbolHeight()<r.getHeight()))&&(null==n||!(c.getSymbolWidth()>n.getWidth()||c.getSymbolHeight()>n.getHeight()))&&t<=c.dataCapacity)return c}}catch(f){i={error:f}}finally{try{u&&!u.done&&(a=s.return)&&a.call(s)}finally{if(i)throw i.error}}if(o)throw new Error("Can't find a symbol arrangement that matches the message. Data codewords: "+t);return null},t.prototype.getHorizontalDataRegions=function(){switch(this.dataRegions){case 1:return 1;case 2:case 4:return 2;case 16:return 4;case 36:return 6;default:throw new Error("Cannot handle this number of data regions")}},t.prototype.getVerticalDataRegions=function(){switch(this.dataRegions){case 1:case 2:return 1;case 4:return 2;case 16:return 4;case 36:return 6;default:throw new Error("Cannot handle this number of data regions")}},t.prototype.getSymbolDataWidth=function(){return this.getHorizontalDataRegions()*this.matrixWidth},t.prototype.getSymbolDataHeight=function(){return this.getVerticalDataRegions()*this.matrixHeight},t.prototype.getSymbolWidth=function(){return this.getSymbolDataWidth()+2*this.getHorizontalDataRegions()},t.prototype.getSymbolHeight=function(){return this.getSymbolDataHeight()+2*this.getVerticalDataRegions()},t.prototype.getCodewordCount=function(){return this.dataCapacity+this.errorCodewords},t.prototype.getInterleavedBlockCount=function(){return this.rsBlockData?this.dataCapacity/this.rsBlockData:1},t.prototype.getDataCapacity=function(){return this.dataCapacity},t.prototype.getErrorCodewords=function(){return this.errorCodewords},t.prototype.getDataLengthForInterleavedBlock=function(t){return this.rsBlockData},t.prototype.getErrorLengthForInterleavedBlock=function(t){return this.rsBlockError},t}(),yi=function(t){function e(){return t.call(this,!1,1558,620,22,22,36,-1,62)||this}return di(e,t),e.prototype.getInterleavedBlockCount=function(){return 10},e.prototype.getDataLengthForInterleavedBlock=function(t){return t<=8?156:155},e}(gi),wi=[new gi(!1,3,5,8,8,1),new gi(!1,5,7,10,10,1),new gi(!0,5,7,16,6,1),new gi(!1,8,10,12,12,1),new gi(!0,10,11,14,6,2),new gi(!1,12,12,14,14,1),new gi(!0,16,14,24,10,1),new gi(!1,18,14,16,16,1),new gi(!1,22,18,18,18,1),new gi(!0,22,18,16,10,2),new gi(!1,30,20,20,20,1),new gi(!0,32,24,16,14,2),new gi(!1,36,24,22,22,1),new gi(!1,44,28,24,24,1),new gi(!0,49,28,22,14,2),new gi(!1,62,36,14,14,4),new gi(!1,86,42,16,16,4),new gi(!1,114,48,18,18,4),new gi(!1,144,56,20,20,4),new gi(!1,174,68,22,22,4),new gi(!1,204,84,24,24,4,102,42),new gi(!1,280,112,14,14,16,140,56),new gi(!1,368,144,16,16,16,92,36),new gi(!1,456,192,18,18,16,114,48),new gi(!1,576,224,20,20,16,144,56),new gi(!1,696,272,22,22,16,174,68),new gi(!1,816,336,24,24,16,136,56),new gi(!1,1050,408,18,18,36,175,68),new gi(!1,1304,496,20,20,36,163,62),new yi],vi=function(){function t(t){this.msg=t,this.pos=0,this.skipAtEnd=0;for(var e=t.split("").map(function(t){return t.charCodeAt(0)}),r=new gt,n=0,o=e.length;n<o;n++){var i=String.fromCharCode(255&e[n]);if("?"===i&&"?"!==t.charAt(n))throw new Error("Message contains characters outside ISO-8859-1 encoding.");r.append(i)}this.msg=r.toString(),this.shape=0,this.codewords=new gt,this.newEncoding=-1}return t.prototype.setSymbolShape=function(t){this.shape=t},t.prototype.setSizeConstraints=function(t,e){this.minSize=t,this.maxSize=e},t.prototype.getMessage=function(){return this.msg},t.prototype.setSkipAtEnd=function(t){this.skipAtEnd=t},t.prototype.getCurrentChar=function(){return this.msg.charCodeAt(this.pos)},t.prototype.getCurrent=function(){return this.msg.charCodeAt(this.pos)},t.prototype.getCodewords=function(){return this.codewords},t.prototype.writeCodewords=function(t){this.codewords.append(t)},t.prototype.writeCodeword=function(t){this.codewords.append(t)},t.prototype.getCodewordCount=function(){return this.codewords.length()},t.prototype.getNewEncoding=function(){return this.newEncoding},t.prototype.signalEncoderChange=function(t){this.newEncoding=t},t.prototype.resetEncoderSignal=function(){this.newEncoding=-1},t.prototype.hasMoreCharacters=function(){return this.pos<this.getTotalMessageCharCount()},t.prototype.getTotalMessageCharCount=function(){return this.msg.length-this.skipAtEnd},t.prototype.getRemainingCharacters=function(){return this.getTotalMessageCharCount()-this.pos},t.prototype.getSymbolInfo=function(){return this.symbolInfo},t.prototype.updateSymbolInfo=function(t){void 0===t&&(t=this.getCodewordCount()),(null==this.symbolInfo||t>this.symbolInfo.getDataCapacity())&&(this.symbolInfo=gi.lookup(t,this.shape,this.minSize,this.maxSize,!0))},t.prototype.resetSymbolInfo=function(){this.symbolInfo=null},t}(),_i=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),mi=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return _i(e,t),e.prototype.getEncodingMode=function(){return 3},e.prototype.encode=function(t){for(var e=new gt;t.hasMoreCharacters();){var r=t.getCurrentChar();if(t.pos++,this.encodeChar(r,e),e.length()%3==0&&(this.writeNextTriplet(t,e),Ei.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode())!==this.getEncodingMode())){t.signalEncoderChange(0);break}}this.handleEOD(t,e)},e.prototype.encodeChar=function(t,e){switch(t){case 13:e.append(0);break;case"*".charCodeAt(0):e.append(1);break;case">".charCodeAt(0):e.append(2);break;case" ".charCodeAt(0):e.append(3);break;default:t>="0".charCodeAt(0)&&t<="9".charCodeAt(0)?e.append(t-48+4):t>="A".charCodeAt(0)&&t<="Z".charCodeAt(0)?e.append(t-65+14):Ei.illegalCharacter(pt.getCharAt(t))}return 1},e.prototype.handleEOD=function(t,e){t.updateSymbolInfo();var r=t.getSymbolInfo().getDataCapacity()-t.getCodewordCount(),n=e.length();t.pos-=n,(t.getRemainingCharacters()>1||r>1||t.getRemainingCharacters()!==r)&&t.writeCodeword(254),t.getNewEncoding()<0&&t.signalEncoderChange(0)},e}(hi),Ci=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Ai=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return Ci(e,t),e.prototype.getEncodingMode=function(){return 2},e.prototype.encodeChar=function(t,e){if(t===" ".charCodeAt(0))return e.append(3),1;if(t>="0".charCodeAt(0)&&t<="9".charCodeAt(0))return e.append(t-48+4),1;if(t>="a".charCodeAt(0)&&t<="z".charCodeAt(0))return e.append(t-97+14),1;if(t<" ".charCodeAt(0))return e.append(0),e.append(t),2;if(t<="/".charCodeAt(0))return e.append(1),e.append(t-33),2;if(t<="@".charCodeAt(0))return e.append(1),e.append(t-58+15),2;if(t>="[".charCodeAt(0)&&t<="_".charCodeAt(0))return e.append(1),e.append(t-91+22),2;if(t==="`".charCodeAt(0))return e.append(2),e.append(0),2;if(t<="Z".charCodeAt(0))return e.append(2),e.append(t-65+1),2;if(t<=127)return e.append(2),e.append(t-123+27),2;e.append("1");var r=2;return r+=this.encodeChar(t-128,e)},e}(hi),Ei=function(){function t(){}return t.randomize253State=function(t){var e=149*t%253+1+129;return e<=254?e:e-254},t.encodeHighLevel=function(t,e,r,n,o){void 0===e&&(e=0),void 0===r&&(r=null),void 0===n&&(n=null),void 0===o&&(o=!1);var i=new hi,a=[new ci,i,new Ai,new mi,new li,new fi],s=new vi(t);s.setSymbolShape(e),s.setSizeConstraints(r,n),t.startsWith(si)&&t.endsWith("")?(s.writeCodeword(236),s.setSkipAtEnd(2),s.pos+=7):t.startsWith(ui)&&t.endsWith("")&&(s.writeCodeword(237),s.setSkipAtEnd(2),s.pos+=7);var u=0;for(o&&(i.encodeMaximal(s),u=s.getNewEncoding(),s.resetEncoderSignal());s.hasMoreCharacters();)a[u].encode(s),s.getNewEncoding()>=0&&(u=s.getNewEncoding(),s.resetEncoderSignal());var c=s.getCodewordCount();s.updateSymbolInfo();var f=s.getSymbolInfo().getDataCapacity();c<f&&0!==u&&5!==u&&4!==u&&s.writeCodeword("þ");var h=s.getCodewords();for(h.length()<f&&h.append(129);h.length()<f;)h.append(this.randomize253State(h.length()+1));return s.getCodewords().toString()},t.lookAheadTest=function(t,e,r){var n=this.lookAheadTestIntern(t,e,r);if(3===r&&3===n){for(var o=Math.min(e+3,t.length),i=e;i<o;i++)if(!this.isNativeX12(t.charCodeAt(i)))return 0}else if(4===r&&4===n)for(o=Math.min(e+4,t.length),i=e;i<o;i++)if(!this.isNativeEDIFACT(t.charCodeAt(i)))return 0;return n},t.lookAheadTestIntern=function(t,e,r){if(e>=t.length)return r;var n;0===r?n=[0,1,1,1,1,1.25]:(n=[1,2,2,2,2,2.25])[r]=0;for(var o=0,i=new Uint8Array(6),a=[];;){if(e+o===t.length){rt.fill(i,0),rt.fill(a,0);var s=this.findMinimums(n,a,nt.MAX_VALUE,i),u=this.getMinimumCount(i);if(a[0]===s)return 0;if(1===u){if(i[5]>0)return 5;if(i[4]>0)return 4;if(i[2]>0)return 2;if(i[3]>0)return 3}return 1}var c=t.charCodeAt(e+o);if(o++,this.isDigit(c)?n[0]+=.5:this.isExtendedASCII(c)?(n[0]=Math.ceil(n[0]),n[0]+=2):(n[0]=Math.ceil(n[0]),n[0]++),this.isNativeC40(c)?n[1]+=2/3:this.isExtendedASCII(c)?n[1]+=8/3:n[1]+=4/3,this.isNativeText(c)?n[2]+=2/3:this.isExtendedASCII(c)?n[2]+=8/3:n[2]+=4/3,this.isNativeX12(c)?n[3]+=2/3:this.isExtendedASCII(c)?n[3]+=13/3:n[3]+=10/3,this.isNativeEDIFACT(c)?n[4]+=3/4:this.isExtendedASCII(c)?n[4]+=4.25:n[4]+=3.25,this.isSpecialB256(c)?n[5]+=4:n[5]++,o>=4){if(rt.fill(i,0),rt.fill(a,0),this.findMinimums(n,a,nt.MAX_VALUE,i),a[0]<this.min(a[5],a[1],a[2],a[3],a[4]))return 0;if(a[5]<a[0]||a[5]+1<this.min(a[1],a[2],a[3],a[4]))return 5;if(a[4]+1<this.min(a[5],a[1],a[2],a[3],a[0]))return 4;if(a[2]+1<this.min(a[5],a[1],a[4],a[3],a[0]))return 2;if(a[3]+1<this.min(a[5],a[1],a[4],a[2],a[0]))return 3;if(a[1]+1<this.min(a[0],a[5],a[4],a[2])){if(a[1]<a[3])return 1;if(a[1]===a[3]){for(var f=e+o+1;f<t.length;){var h=t.charCodeAt(f);if(this.isX12TermSep(h))return 3;if(!this.isNativeX12(h))break;f++}return 1}}}}},t.min=function(t,e,r,n,o){var i=Math.min(t,Math.min(e,Math.min(r,n)));return void 0===o?i:Math.min(i,o)},t.findMinimums=function(t,e,r,n){for(var o=0;o<6;o++){var i=e[o]=Math.ceil(t[o]);r>i&&(r=i,rt.fill(n,0)),r===i&&(n[o]=n[o]+1)}return r},t.getMinimumCount=function(t){for(var e=0,r=0;r<6;r++)e+=t[r];return e||0},t.isDigit=function(t){return t>="0".charCodeAt(0)&&t<="9".charCodeAt(0)},t.isExtendedASCII=function(t){return t>=128&&t<=255},t.isNativeC40=function(t){return t===" ".charCodeAt(0)||t>="0".charCodeAt(0)&&t<="9".charCodeAt(0)||t>="A".charCodeAt(0)&&t<="Z".charCodeAt(0)},t.isNativeText=function(t){return t===" ".charCodeAt(0)||t>="0".charCodeAt(0)&&t<="9".charCodeAt(0)||t>="a".charCodeAt(0)&&t<="z".charCodeAt(0)},t.isNativeX12=function(t){return this.isX12TermSep(t)||t===" ".charCodeAt(0)||t>="0".charCodeAt(0)&&t<="9".charCodeAt(0)||t>="A".charCodeAt(0)&&t<="Z".charCodeAt(0)},t.isX12TermSep=function(t){return 13===t||t==="*".charCodeAt(0)||t===">".charCodeAt(0)},t.isNativeEDIFACT=function(t){return t>=" ".charCodeAt(0)&&t<="^".charCodeAt(0)},t.isSpecialB256=function(t){return!1},t.determineConsecutiveDigitCount=function(t,e){void 0===e&&(e=0);for(var r=t.length,n=e;n<r&&this.isDigit(t.charCodeAt(n));)n++;return n-e},t.illegalCharacter=function(t){var e=nt.toHexString(t.charCodeAt(0));throw e="0000".substring(0,4-e.length)+e,new Error("Illegal character: "+t+" (0x"+e+")")},t}(),Ii=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Si=function(){function t(t){this.charset=t,this.name=t.name}return t.prototype.canEncode=function(t){try{return null!=dt.encode(t,this.charset)}catch(e){return!1}},t}(),bi=function(){function t(t,e,r){var n,o,i,a,s,u;this.ENCODERS=["IBM437","ISO-8859-2","ISO-8859-3","ISO-8859-4","ISO-8859-5","ISO-8859-6","ISO-8859-7","ISO-8859-8","ISO-8859-9","ISO-8859-10","ISO-8859-11","ISO-8859-13","ISO-8859-14","ISO-8859-15","ISO-8859-16","windows-1250","windows-1251","windows-1252","windows-1256","Shift_JIS"].map(function(t){return new Si(oi.forName(t))}),this.encoders=[];var c=[];c.push(new Si(ii.ISO_8859_1));for(var f=null!=e&&e.name.startsWith("UTF"),h=0;h<t.length;h++){var l=!1;try{for(var d=(n=void 0,Ii(c)),p=d.next();!p.done;p=d.next()){var g=p.value,y=t.charAt(h);if(y.charCodeAt(0)===r||g.canEncode(y)){l=!0;break}}}catch(E){n={error:E}}finally{try{p&&!p.done&&(o=d.return)&&o.call(d)}finally{if(n)throw n.error}}if(!l)try{for(var w=(i=void 0,Ii(this.ENCODERS)),v=w.next();!v.done;v=w.next())if((g=v.value).canEncode(t.charAt(h))){c.push(g),l=!0;break}}catch(I){i={error:I}}finally{try{v&&!v.done&&(a=w.return)&&a.call(w)}finally{if(i)throw i.error}}l||(f=!0)}if(1!==c.length||f){this.encoders=[];var _=0;try{for(var m=Ii(c),C=m.next();!C.done;C=m.next())g=C.value,this.encoders[_++]=g}catch(S){s={error:S}}finally{try{C&&!C.done&&(u=m.return)&&u.call(m)}finally{if(s)throw s.error}}}else this.encoders=[c[0]];var A=-1;if(null!=e)for(h=0;h<this.encoders.length;h++)if(null!=this.encoders[h]&&e.name===this.encoders[h].name){A=h;break}this.priorityEncoderIndex=A}return t.prototype.length=function(){return this.encoders.length},t.prototype.getCharsetName=function(t){if(!(t<this.length()))throw new Error("index must be less than length");return this.encoders[t].name},t.prototype.getCharset=function(t){if(!(t<this.length()))throw new Error("index must be less than length");return this.encoders[t].charset},t.prototype.getECIValue=function(t){return this.encoders[t].charset.getValueIdentifier()},t.prototype.getPriorityEncoderIndex=function(){return this.priorityEncoderIndex},t.prototype.canEncode=function(t,e){if(!(e<this.length()))throw new Error("index must be less than length");return!0},t.prototype.encode=function(t,e){if(!(e<this.length()))throw new Error("index must be less than length");return dt.encode(pt.getCharAt(t),this.encoders[e].name)},t}(),Ti=function(){function t(t,e,r){this.fnc1=r;var n=new bi(t,e,r);if(1===n.length())for(var o=0;o<this.bytes.length;o++){var i=t.charAt(o).charCodeAt(0);this.bytes[o]=i===r?1e3:i}else this.bytes=this.encodeMinimally(t,n,r)}return t.prototype.getFNC1Character=function(){return this.fnc1},t.prototype.length=function(){return this.bytes.length},t.prototype.haveNCharacters=function(t,e){if(t+e-1>=this.bytes.length)return!1;for(var r=0;r<e;r++)if(this.isECI(t+r))return!1;return!0},t.prototype.charAt=function(t){if(t<0||t>=this.length())throw new Error(""+t);if(this.isECI(t))throw new Error("value at "+t+" is not a character but an ECI");return this.isFNC1(t)?this.fnc1:this.bytes[t]},t.prototype.subSequence=function(t,e){if(t<0||t>e||e>this.length())throw new Error(""+t);for(var r=new gt,n=t;n<e;n++){if(this.isECI(n))throw new Error("value at "+n+" is not a character but an ECI");r.append(this.charAt(n))}return r.toString()},t.prototype.isECI=function(t){if(t<0||t>=this.length())throw new Error(""+t);return this.bytes[t]>255&&this.bytes[t]<=999},t.prototype.isFNC1=function(t){if(t<0||t>=this.length())throw new Error(""+t);return 1e3===this.bytes[t]},t.prototype.getECIValue=function(t){if(t<0||t>=this.length())throw new Error(""+t);if(!this.isECI(t))throw new Error("value at "+t+" is not an ECI but a character");return this.bytes[t]-256},t.prototype.addEdge=function(t,e,r){(null==t[e][r.encoderIndex]||t[e][r.encoderIndex].cachedTotalSize>r.cachedTotalSize)&&(t[e][r.encoderIndex]=r)},t.prototype.addEdges=function(t,e,r,n,o,i){var a=t.charAt(n).charCodeAt(0),s=0,u=e.length();e.getPriorityEncoderIndex()>=0&&(a===i||e.canEncode(a,e.getPriorityEncoderIndex()))&&(u=(s=e.getPriorityEncoderIndex())+1);for(var c=s;c<u;c++)(a===i||e.canEncode(a,c))&&this.addEdge(r,n+1,new Oi(a,e,c,o,i))},t.prototype.encodeMinimally=function(t,e,r){var n=t.length,o=new(Oi[n+1][e.length()]);this.addEdges(t,e,o,0,null,r);for(var i=1;i<=n;i++){for(var a=0;a<e.length();a++)null!=o[i][a]&&i<n&&this.addEdges(t,e,o,i,o[i][a],r);for(a=0;a<e.length();a++)o[i-1][a]=null}var s=-1,u=nt.MAX_VALUE;for(a=0;a<e.length();a++)if(null!=o[n][a]){var c=o[n][a];c.cachedTotalSize<u&&(u=c.cachedTotalSize,s=a)}if(s<0)throw new Error('Failed to encode "'+t+'"');for(var f=[],h=o[n][s];null!=h;){if(h.isFNC1())f.unshift(1e3);else{var l=e.encode(h.c,h.encoderIndex);for(i=l.length-1;i>=0;i--)f.unshift(255&l[i])}(null===h.previous?0:h.previous.encoderIndex)!==h.encoderIndex&&f.unshift(256+e.getECIValue(h.encoderIndex)),h=h.previous}var d=[];for(i=0;i<d.length;i++)d[i]=f[i];return d},t}(),Oi=function(){function t(t,e,r,n,o){this.c=t,this.encoderSet=e,this.encoderIndex=r,this.previous=n,this.fnc1=o,this.c=t===o?1e3:t;var i=this.isFNC1()?1:e.encode(t,r).length;(null===n?0:n.encoderIndex)!==r&&(i+=3),null!=n&&(i+=n.cachedTotalSize),this.cachedTotalSize=i}return t.prototype.isFNC1=function(){return 1e3===this.c},t}(),Ri=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();!function(t){t[t.ASCII=0]="ASCII",t[t.C40=1]="C40",t[t.TEXT=2]="TEXT",t[t.X12=3]="X12",t[t.EDF=4]="EDF",t[t.B256=5]="B256"}(ai||(ai={})),function(t){function e(e,r,n,o,i){var a=t.call(this,e,r,n)||this;return a.shape=o,a.macroId=i,a}Ri(e,t),e.prototype.getMacroId=function(){return this.macroId},e.prototype.getShapeHint=function(){return this.shape}}(Ti);var Ni=function(){function t(){}return t.prototype.isCompact=function(){return this.compact},t.prototype.setCompact=function(t){this.compact=t},t.prototype.getSize=function(){return this.size},t.prototype.setSize=function(t){this.size=t},t.prototype.getLayers=function(){return this.layers},t.prototype.setLayers=function(t){this.layers=t},t.prototype.getCodeWords=function(){return this.codeWords},t.prototype.setCodeWords=function(t){this.codeWords=t},t.prototype.getMatrix=function(){return this.matrix},t.prototype.setMatrix=function(t){this.matrix=t},t}(),Di=function(){function t(){}return t.singletonList=function(t){return[t]},t.min=function(t,e){return t.sort(e)[0]},t}(),Mi=function(){function t(t){this.previous=t}return t.prototype.getPrevious=function(){return this.previous},t}(),Pi=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Bi=function(t){function e(e,r,n){var o=t.call(this,e)||this;return o.value=r,o.bitCount=n,o}return Pi(e,t),e.prototype.appendTo=function(t,e){t.appendBits(this.value,this.bitCount)},e.prototype.add=function(t,r){return new e(this,t,r)},e.prototype.addBinaryShift=function(t,r){return console.warn("addBinaryShift on SimpleToken, this simply returns a copy of this token"),new e(this,t,r)},e.prototype.toString=function(){var t=this.value&(1<<this.bitCount)-1;return t|=1<<this.bitCount,"<"+nt.toBinaryString(t|1<<this.bitCount).substring(1)+">"},e}(Mi),Li=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},t(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Fi=function(t){function e(e,r,n){var o=t.call(this,e,0,0)||this;return o.binaryShiftStart=r,o.binaryShiftByteCount=n,o}return Li(e,t),e.prototype.appendTo=function(t,e){for(var r=0;r<this.binaryShiftByteCount;r++)(0===r||31===r&&this.binaryShiftByteCount<=62)&&(t.appendBits(31,5),this.binaryShiftByteCount>62?t.appendBits(this.binaryShiftByteCount-31,16):0===r?t.appendBits(Math.min(this.binaryShiftByteCount,31),5):t.appendBits(this.binaryShiftByteCount-31,5)),t.appendBits(e[this.binaryShiftStart+r],8)},e.prototype.addBinaryShift=function(t,r){return new e(this,t,r)},e.prototype.toString=function(){return"<"+this.binaryShiftStart+"::"+(this.binaryShiftStart+this.binaryShiftByteCount-1)+">"},e}(Bi);function ki(t,e,r){return new Bi(t,e,r)}var xi=["UPPER","LOWER","DIGIT","MIXED","PUNCT"],Vi=new Bi(null,0,0),Ui=[Int32Array.from([0,327708,327710,327709,656318]),Int32Array.from([590318,0,327710,327709,656318]),Int32Array.from([262158,590300,0,590301,932798]),Int32Array.from([327709,327708,656318,0,327710]),Int32Array.from([327711,656380,656382,656381,0])],Hi=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Gi=function(t){var e,r;try{for(var n=Hi(t),o=n.next();!o.done;o=n.next()){var i=o.value;rt.fill(i,-1)}}catch(a){e={error:a}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}return t[0][4]=0,t[1][4]=0,t[1][0]=28,t[3][4]=0,t[2][4]=0,t[2][0]=15,t}(rt.createInt32Array(6,6)),Xi=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Wi=function(){function t(t,e,r,n){this.token=t,this.mode=e,this.binaryShiftByteCount=r,this.bitCount=n}return t.prototype.getMode=function(){return this.mode},t.prototype.getToken=function(){return this.token},t.prototype.getBinaryShiftByteCount=function(){return this.binaryShiftByteCount},t.prototype.getBitCount=function(){return this.bitCount},t.prototype.latchAndAppend=function(e,r){var n=this.bitCount,o=this.token;if(e!==this.mode){var i=Ui[this.mode][e];o=ki(o,65535&i,i>>16),n+=i>>16}var a=2===e?4:5;return new t(o=ki(o,r,a),e,0,n+a)},t.prototype.shiftAndAppend=function(e,r){var n=this.token,o=2===this.mode?4:5;return n=ki(n,Gi[this.mode][e],o),new t(n=ki(n,r,5),this.mode,0,this.bitCount+o+5)},t.prototype.addBinaryShiftChar=function(e){var r=this.token,n=this.mode,o=this.bitCount;if(4===this.mode||2===this.mode){var i=Ui[n][0];r=ki(r,65535&i,i>>16),o+=i>>16,n=0}var a=0===this.binaryShiftByteCount||31===this.binaryShiftByteCount?18:62===this.binaryShiftByteCount?9:8,s=new t(r,n,this.binaryShiftByteCount+1,o+a);return 2078===s.binaryShiftByteCount&&(s=s.endBinaryShift(e+1)),s},t.prototype.endBinaryShift=function(e){if(0===this.binaryShiftByteCount)return this;var r=this.token;return new t(r=function(t,e,r){return new Fi(t,e,r)}(r,e-this.binaryShiftByteCount,this.binaryShiftByteCount),this.mode,0,this.bitCount)},t.prototype.isBetterThanOrEqualTo=function(e){var r=this.bitCount+(Ui[this.mode][e.mode]>>16);return this.binaryShiftByteCount<e.binaryShiftByteCount?r+=t.calculateBinaryShiftCost(e)-t.calculateBinaryShiftCost(this):this.binaryShiftByteCount>e.binaryShiftByteCount&&e.binaryShiftByteCount>0&&(r+=10),r<=e.bitCount},t.prototype.toBitArray=function(t){for(var e,r,n=[],o=this.endBinaryShift(t.length).token;null!==o;o=o.getPrevious())n.unshift(o);var i=new ot;try{for(var a=Xi(n),s=a.next();!s.done;s=a.next())s.value.appendTo(i,t)}catch(u){e={error:u}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return i},t.prototype.toString=function(){return pt.format("%s bits=%d bytes=%d",xi[this.mode],this.bitCount,this.binaryShiftByteCount)},t.calculateBinaryShiftCost=function(t){return t.binaryShiftByteCount>62?21:t.binaryShiftByteCount>31?20:t.binaryShiftByteCount>0?10:0},t.INITIAL_STATE=new t(Vi,0,0,0),t}(),ji=function(t){var e=pt.getCharCode(" "),r=pt.getCharCode("."),n=pt.getCharCode(",");t[0][e]=1;for(var o=pt.getCharCode("Z"),i=pt.getCharCode("A"),a=i;a<=o;a++)t[0][a]=a-i+2;t[1][e]=1;var s=pt.getCharCode("z"),u=pt.getCharCode("a");for(a=u;a<=s;a++)t[1][a]=a-u+2;t[2][e]=1;var c=pt.getCharCode("9"),f=pt.getCharCode("0");for(a=f;a<=c;a++)t[2][a]=a-f+2;t[2][n]=12,t[2][r]=13;for(var h=["\0"," ","","","","","","","","\b","\t","\n","\v","\f","\r","","","","","","@","\\","^","_","`","|","~",""],l=0;l<h.length;l++)t[3][pt.getCharCode(h[l])]=l;var d=["\0","\r","\0","\0","\0","\0","!","'","#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","[","]","{","}"];for(l=0;l<d.length;l++)pt.getCharCode(d[l])>0&&(t[4][pt.getCharCode(d[l])]=l);return t}(rt.createInt32Array(5,256)),zi=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Yi=function(){function t(t){this.text=t}return t.prototype.encode=function(){for(var e=pt.getCharCode(" "),r=pt.getCharCode("\n"),n=Di.singletonList(Wi.INITIAL_STATE),o=0;o<this.text.length;o++){var i=void 0,a=o+1<this.text.length?this.text[o+1]:0;switch(this.text[o]){case pt.getCharCode("\r"):i=a===r?2:0;break;case pt.getCharCode("."):i=a===e?3:0;break;case pt.getCharCode(","):i=a===e?4:0;break;case pt.getCharCode(":"):i=a===e?5:0;break;default:i=0}i>0?(n=t.updateStateListForPair(n,o,i),o++):n=this.updateStateListForChar(n,o)}return Di.min(n,function(t,e){return t.getBitCount()-e.getBitCount()}).toBitArray(this.text)},t.prototype.updateStateListForChar=function(e,r){var n,o,i=[];try{for(var a=zi(e),s=a.next();!s.done;s=a.next()){var u=s.value;this.updateStateForChar(u,r,i)}}catch(c){n={error:c}}finally{try{s&&!s.done&&(o=a.return)&&o.call(a)}finally{if(n)throw n.error}}return t.simplifyStates(i)},t.prototype.updateStateForChar=function(t,e,r){for(var n=255&this.text[e],o=ji[t.getMode()][n]>0,i=null,a=0;a<=4;a++){var s=ji[a][n];if(s>0){if(null==i&&(i=t.endBinaryShift(e)),!o||a===t.getMode()||2===a){var u=i.latchAndAppend(a,s);r.push(u)}if(!o&&Gi[t.getMode()][a]>=0){var c=i.shiftAndAppend(a,s);r.push(c)}}}if(t.getBinaryShiftByteCount()>0||0===ji[t.getMode()][n]){var f=t.addBinaryShiftChar(e);r.push(f)}},t.updateStateListForPair=function(t,e,r){var n,o,i=[];try{for(var a=zi(t),s=a.next();!s.done;s=a.next()){var u=s.value;this.updateStateForPair(u,e,r,i)}}catch(c){n={error:c}}finally{try{s&&!s.done&&(o=a.return)&&o.call(a)}finally{if(n)throw n.error}}return this.simplifyStates(i)},t.updateStateForPair=function(t,e,r,n){var o=t.endBinaryShift(e);if(n.push(o.latchAndAppend(4,r)),4!==t.getMode()&&n.push(o.shiftAndAppend(4,r)),3===r||4===r){var i=o.latchAndAppend(2,16-r).latchAndAppend(2,1);n.push(i)}if(t.getBinaryShiftByteCount()>0){var a=t.addBinaryShiftChar(e).addBinaryShiftChar(e+1);n.push(a)}},t.simplifyStates=function(t){var e,r,n,o,i=[];try{for(var a=zi(t),s=a.next();!s.done;s=a.next()){var u=s.value,c=!0,f=function(t){if(t.isBetterThanOrEqualTo(u))return c=!1,"break";u.isBetterThanOrEqualTo(t)&&(i=i.filter(function(e){return e!==t}))};try{for(var h=(n=void 0,zi(i)),l=h.next();!l.done&&"break"!==f(l.value);l=h.next());}catch(d){n={error:d}}finally{try{l&&!l.done&&(o=h.return)&&o.call(h)}finally{if(n)throw n.error}}c&&i.push(u)}}catch(p){e={error:p}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return i},t}(),Zi=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};!function(){function t(){}t.encodeBytes=function(e){return t.encode(e,t.DEFAULT_EC_PERCENT,t.DEFAULT_AZTEC_LAYERS)},t.encode=function(e,r,n){var o,i,a,s,u,c=new Yi(e).encode(),f=nt.truncDivision(c.getSize()*r,100)+11,h=c.getSize()+f;if(n!==t.DEFAULT_AZTEC_LAYERS){if(o=n<0,(i=Math.abs(n))>(o?t.MAX_NB_BITS_COMPACT:t.MAX_NB_BITS))throw new j(pt.format("Illegal value %s for layers",n));var l=(a=t.totalBitsInLayer(i,o))-a%(s=t.WORD_SIZE[i]);if((u=t.stuffBits(c,s)).getSize()+f>l)throw new j("Data to large for user specified layer");if(o&&u.getSize()>64*s)throw new j("Data to large for user specified layer")}else{s=0,u=null;for(var d=0;;d++){if(d>t.MAX_NB_BITS)throw new j("Data too large for an Aztec code");if(!(i=(o=d<=3)?d+1:d,h>(a=t.totalBitsInLayer(i,o))||(null!=u&&s===t.WORD_SIZE[i]||(s=t.WORD_SIZE[i],u=t.stuffBits(c,s)),l=a-a%s,o&&u.getSize()>64*s||!(u.getSize()+f<=l))))break}}var p,g=t.generateCheckWords(u,a,s),y=u.getSize()/s,w=t.generateModeMessage(o,i,y),v=(o?11:14)+4*i,_=new Int32Array(v);if(o)for(p=v,d=0;d<_.length;d++)_[d]=d;else{p=v+1+2*nt.truncDivision(nt.truncDivision(v,2)-1,15);var m=nt.truncDivision(v,2),C=nt.truncDivision(p,2);for(d=0;d<m;d++){var A=d+nt.truncDivision(d,15);_[m-d-1]=C-A-1,_[m+d]=C+A+1}}for(var E=new yt(p),I=(d=0,0);d<i;d++){for(var S=4*(i-d)+(o?9:12),b=0;b<S;b++)for(var T=2*b,O=0;O<2;O++)g.get(I+T+O)&&E.set(_[2*d+O],_[2*d+b]),g.get(I+2*S+T+O)&&E.set(_[2*d+b],_[v-1-2*d-O]),g.get(I+4*S+T+O)&&E.set(_[v-1-2*d-O],_[v-1-2*d-b]),g.get(I+6*S+T+O)&&E.set(_[v-1-2*d-b],_[2*d+O]);I+=8*S}if(t.drawModeMessage(E,o,p,w),o)t.drawBullsEye(E,nt.truncDivision(p,2),5);else for(t.drawBullsEye(E,nt.truncDivision(p,2),7),d=0,b=0;d<nt.truncDivision(v,2)-1;d+=15,b+=16)for(O=1&nt.truncDivision(p,2);O<p;O+=2)E.set(nt.truncDivision(p,2)-b,O),E.set(nt.truncDivision(p,2)+b,O),E.set(O,nt.truncDivision(p,2)-b),E.set(O,nt.truncDivision(p,2)+b);var R=new Ni;return R.setCompact(o),R.setSize(p),R.setLayers(i),R.setCodeWords(y),R.setMatrix(E),R},t.drawBullsEye=function(t,e,r){for(var n=0;n<r;n+=2)for(var o=e-n;o<=e+n;o++)t.set(o,e-n),t.set(o,e+n),t.set(e-n,o),t.set(e+n,o);t.set(e-r,e-r),t.set(e-r+1,e-r),t.set(e-r,e-r+1),t.set(e+r,e-r),t.set(e+r,e-r+1),t.set(e+r,e+r-1)},t.generateModeMessage=function(e,r,n){var o=new ot;return e?(o.appendBits(r-1,2),o.appendBits(n-1,6),o=t.generateCheckWords(o,28,4)):(o.appendBits(r-1,5),o.appendBits(n-1,11),o=t.generateCheckWords(o,40,4)),o},t.drawModeMessage=function(t,e,r,n){var o=nt.truncDivision(r,2);if(e)for(var i=0;i<7;i++){var a=o-3+i;n.get(i)&&t.set(a,o-5),n.get(i+7)&&t.set(o+5,a),n.get(20-i)&&t.set(a,o+5),n.get(27-i)&&t.set(o-5,a)}else for(i=0;i<10;i++)a=o-5+i+nt.truncDivision(i,5),n.get(i)&&t.set(a,o-7),n.get(i+10)&&t.set(o+7,a),n.get(29-i)&&t.set(a,o+7),n.get(39-i)&&t.set(o-7,a)},t.generateCheckWords=function(e,r,n){var o,i,a=e.getSize()/n,s=new Xo(t.getGF(n)),u=nt.truncDivision(r,n),c=t.bitsToWords(e,n,u);s.encode(c,u-a);var f=r%n,h=new ot;h.appendBits(0,f);try{for(var l=Zi(Array.from(c)),d=l.next();!d.done;d=l.next()){var p=d.value;h.appendBits(p,n)}}catch(g){o={error:g}}finally{try{d&&!d.done&&(i=l.return)&&i.call(l)}finally{if(o)throw o.error}}return h},t.bitsToWords=function(t,e,r){var n,o,i=new Int32Array(r);for(n=0,o=t.getSize()/e;n<o;n++){for(var a=0,s=0;s<e;s++)a|=t.get(n*e+s)?1<<e-s-1:0;i[n]=a}return i},t.getGF=function(t){switch(t){case 4:return Wt.AZTEC_PARAM;case 6:return Wt.AZTEC_DATA_6;case 8:return Wt.AZTEC_DATA_8;case 10:return Wt.AZTEC_DATA_10;case 12:return Wt.AZTEC_DATA_12;default:throw new j("Unsupported word size "+t)}},t.stuffBits=function(t,e){for(var r=new ot,n=t.getSize(),o=(1<<e)-2,i=0;i<n;i+=e){for(var a=0,s=0;s<e;s++)(i+s>=n||t.get(i+s))&&(a|=1<<e-1-s);(a&o)===o?(r.appendBits(a&o,e),i--):0===(a&o)?(r.appendBits(1|a,e),i--):r.appendBits(a,e)}return r},t.totalBitsInLayer=function(t,e){return((e?88:112)+16*t)*t},t.DEFAULT_EC_PERCENT=33,t.DEFAULT_AZTEC_LAYERS=0,t.MAX_NB_BITS=32,t.MAX_NB_BITS_COMPACT=4,t.WORD_SIZE=Int32Array.from([4,6,6,8,8,8,8,8,8,10,10,10,10,10,10,10,10,10,10,10,10,10,10,12,12,12,12,12,12,12,12,12,12])}(),t("BarcodeScannerWeb",class extends _{constructor(){super(...arguments),this._formats=[],this._controls=null,this._torchState=!1,this._video=null,this._options=null,this._backgroundColor=null}async prepare(){await this._getVideoElement()}async hideBackground(){this._backgroundColor=document.documentElement.style.backgroundColor,document.documentElement.style.backgroundColor="transparent"}async showBackground(){document.documentElement.style.backgroundColor=this._backgroundColor||""}async startScan(t){var e;if(this._options=t,this._formats=[],null===(e=null==t?void 0:t.targetedFormats)||void 0===e||e.forEach(t=>{Object.keys(m).indexOf(t)>=0?this._formats.push(0):console.error(t,"is not supported on web")}),await this._getVideoElement())return await this._getFirstResultFromReader();throw this.unavailable("Missing video element")}async startScanning(t,e){throw this.unimplemented("Not implemented on web.")}async pauseScanning(){this._controls&&(this._controls.stop(),this._controls=null)}async resumeScanning(){this._getFirstResultFromReader()}async stopScan(t){this._stop(),this._controls&&(this._controls.stop(),this._controls=null)}async checkPermission(t){if("undefined"==typeof navigator||!navigator.permissions)throw this.unavailable("Permissions API not available in this browser");try{const t=await window.navigator.permissions.query({name:"camera"});return"prompt"===t.state?{neverAsked:!0}:"denied"===t.state?{denied:!0}:"granted"===t.state?{granted:!0}:{unknown:!0}}catch(ei){throw this.unavailable("Camera permissions are not available in this browser")}}async openAppSettings(){throw this.unavailable("App settings are not available in this browser")}async disableTorch(){this._controls&&this._controls.switchTorch&&(this._controls.switchTorch(!1),this._torchState=!1)}async enableTorch(){this._controls&&this._controls.switchTorch&&(this._controls.switchTorch(!0),this._torchState=!0)}async toggleTorch(){this._controls&&this._controls.switchTorch&&this._controls.switchTorch(!0)}async getTorchState(){return{isEnabled:this._torchState}}async _getVideoElement(){return this._video||await this._startVideo(),this._video}async _getFirstResultFromReader(){const t=await this._getVideoElement();return new Promise(async e=>{if(t){let r;this._formats.length&&(r=new Map,r.set(k.POSSIBLE_FORMATS,this._formats));const n=new V(r);this._controls=await n.decodeFromVideoElement(t,(t,r,n)=>{!r&&t&&t.getText()&&(e({hasContent:!0,content:t.getText(),format:t.getBarcodeFormat().toString()}),n.stop(),this._controls=null,this._stop()),r&&r.message&&console.error(r.message)})}})}async _startVideo(){return new Promise(async(t,r)=>{var n;await navigator.mediaDevices.getUserMedia({audio:!1,video:!0}).then(t=>{t.getTracks().forEach(t=>t.stop())}).catch(t=>{r(t)});const o=document.body;if(document.getElementById("video"))r({message:"camera already started"});else{const i=document.createElement("div");i.setAttribute("style","position:absolute; top: 0; left: 0; width:100%; height: 100%; background-color: black;"),this._video=document.createElement("video"),this._video.id="video",(null===(n=this._options)||void 0===n?void 0:n.cameraDirection)!==e?this._video.setAttribute("style","-webkit-transform: scaleX(-1); transform: scaleX(-1); width:100%; height: 100%;"):this._video.setAttribute("style","width:100%; height: 100%;");const a=navigator.userAgent.toLowerCase();if(a.includes("safari")&&!a.includes("chrome")&&(this._video.setAttribute("autoplay","true"),this._video.setAttribute("muted","true"),this._video.setAttribute("playsinline","true")),i.appendChild(this._video),o.appendChild(i),navigator.mediaDevices&&navigator.mediaDevices.getUserMedia){const e={video:{}};navigator.mediaDevices.getUserMedia(e).then(e=>{this._video&&(this._video.srcObject=e,this._video.play()),t({})},t=>{r(t)})}}})}async _stop(){var t;if(this._video){this._video.pause();const r=this._video.srcObject.getTracks();for(var e=0;e<r.length;e++)r[e].stop();null===(t=this._video.parentElement)||void 0===t||t.remove(),this._video=null}}})}}});
