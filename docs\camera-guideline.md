# Camera Guideline untuk Absensi

## Deskripsi
Fitur guideline kamera membantu karyawan memposisikan wajah mereka dengan benar saat melakukan absensi. Guideline berupa overlay visual yang menunjukkan area optimal untuk penempatan wajah.

## Fitur Utama

### 1. Visual Guideline
- **Oval Guide**: Area berbentuk oval biru untuk mengarahkan posisi wajah
- **Corner Indicators**: Indikator sudut yang berkedip untuk menarik perhatian
- **Overlay Mask**: Background gelap semi-transparan untuk fokus pada area wajah
- **Instruction Text**: Teks panduan di bagian bawah kamera

### 2. Animasi dan Efek Visual
- **Pulse Animation**: Guideline berkedip dengan efek pulse
- **Corner Blink**: Indikator sudut berkedip bergantian
- **Scale Effect**: Sedikit perubahan ukuran untuk menarik perhatian
- **Color Transition**: Transisi warna dari biru ke cyan

### 3. Responsif Design
- **Viewport Units**: Menggunakan `vw` untuk responsivitas
- **Min/Max Constraints**: Batasan ukuran minimum dan maksimum
- **Mobile Optimized**: Dioptimalkan untuk berbagai ukuran layar mobile

## Implementasi Teknis

### CSS Animations
```css
@keyframes pulse {
  0% {
    border-color: #1880ff;
    transform: scale(1);
  }
  50% {
    border-color: #00d4ff;
    transform: scale(1.02);
  }
  100% {
    border-color: #1880ff;
    transform: scale(1);
  }
}

@keyframes cornerBlink {
  0%, 100% { opacity: 1; background: #1880ff; }
  50% { opacity: 0.6; background: #00d4ff; }
}
```

### Struktur Guideline
```jsx
<div style={{ position: 'relative' }}>
  <video /> {/* Camera feed */}
  
  {/* Guideline Overlay */}
  <div style={{ position: 'absolute', ... }}>
    {/* Face Oval */}
    <div style={{ 
      width: 'min(200px, 50vw)',
      height: 'min(260px, 65vw)',
      border: '3px solid #1880ff',
      borderRadius: '50%',
      animation: 'pulse 2s ease-in-out infinite'
    }}>
      {/* Corner Indicators */}
      <div style={{ animation: 'cornerBlink 1.5s ...' }} />
      {/* ... 3 corner indicators lainnya */}
    </div>
  </div>
  
  {/* Instruction Text */}
  <div style={{ position: 'absolute', bottom: '20px', ... }}>
    📸 Posisikan wajah di dalam area biru
  </div>
</div>
```

### State Management
```typescript
const [showGuideline, setShowGuideline] = useState(true);

// Toggle guideline visibility
const toggleGuideline = () => setShowGuideline(!showGuideline);
```

## Kontrol User

### Toggle Button
- **Lokasi**: Di atas area kamera
- **Fungsi**: Menampilkan/menyembunyikan guideline
- **Icon**: 👁️ dengan teks deskriptif
- **Style**: Outline button dengan border radius

### Kondisi Tampil
- **Hanya saat kamera aktif**: Guideline hanya muncul saat video stream aktif
- **Tidak saat preview foto**: Guideline disembunyikan saat menampilkan hasil foto
- **Dapat di-toggle**: User bisa menyembunyikan jika mengganggu

## Spesifikasi Visual

### Ukuran Guideline
- **Desktop**: 200px × 260px (fixed)
- **Mobile**: min(200px, 50vw) × min(260px, 65vw)
- **Maximum**: 250px × 320px

### Warna Scheme
- **Primary**: #1880ff (biru aplikasi)
- **Secondary**: #00d4ff (cyan untuk animasi)
- **Background**: rgba(0, 0, 0, 0.5) (overlay gelap)
- **Guideline Area**: rgba(24, 128, 255, 0.1) (biru transparan)

### Timing Animasi
- **Pulse**: 2 detik per cycle
- **Corner Blink**: 1.5 detik per cycle
- **Stagger Delay**: 0.3s antar corner indicator

## User Experience

### Manfaat
1. **Guidance**: Membantu user memposisikan wajah dengan benar
2. **Consistency**: Memastikan kualitas foto absensi yang konsisten
3. **Accessibility**: Visual cue yang jelas untuk semua user
4. **Professional**: Memberikan kesan aplikasi yang profesional

### Best Practices
1. **Posisi Wajah**: Tengah dalam oval guideline
2. **Jarak Kamera**: Sekitar 30-50cm dari layar
3. **Pencahayaan**: Pastikan wajah terlihat jelas
4. **Orientasi**: Wajah menghadap langsung ke kamera

## Troubleshooting

### Guideline Tidak Muncul
- Pastikan state `showGuideline` adalah `true`
- Cek apakah kamera sudah aktif
- Periksa CSS animations sudah ter-load

### Ukuran Tidak Sesuai
- Cek viewport units support di browser
- Pastikan min/max constraints berfungsi
- Test di berbagai ukuran layar

### Animasi Tidak Smooth
- Periksa performance browser
- Cek apakah ada konflik CSS
- Reduce animation complexity jika perlu

### Toggle Button Tidak Berfungsi
- Cek state management
- Pastikan event handler terpasang
- Debug dengan console.log

## Customization

### Mengubah Ukuran
```typescript
// Ubah di style guideline
width: 'min(250px, 55vw)', // Lebih besar
height: 'min(320px, 70vw)'
```

### Mengubah Warna
```css
/* Ubah di guidelineStyles */
border-color: #ff6b35; /* Orange */
background: rgba(255, 107, 53, 0.1);
```

### Mengubah Animasi
```css
/* Ubah timing */
animation: 'pulse 3s ease-in-out infinite'; /* Lebih lambat */

/* Ubah easing */
animation: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite';
```

### Menambah Guideline Baru
```jsx
{/* Guideline untuk mata */}
<div style={{
  position: 'absolute',
  top: '35%',
  left: '30%',
  right: '30%',
  height: '2px',
  background: '#1880ff',
  opacity: 0.7
}} />
```

## Future Enhancements

### Deteksi Wajah
- Integrasi dengan face detection API
- Real-time feedback posisi wajah
- Auto-capture saat wajah dalam posisi optimal

### Smart Guideline
- Adaptive sizing berdasarkan deteksi wajah
- Dynamic positioning
- Quality score indicator

### Accessibility
- Voice guidance untuk visually impaired
- High contrast mode
- Haptic feedback untuk mobile
